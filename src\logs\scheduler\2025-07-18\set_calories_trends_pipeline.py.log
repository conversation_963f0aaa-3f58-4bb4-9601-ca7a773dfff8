[2025-07-18T11:26:01.343+0000] {processor.py:186} INFO - Started process (PID=306) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:26:01.344+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:26:01.347+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.346+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:26:01.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.410+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:26:01.416+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:26:01.436+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.100 seconds
[2025-07-18T11:26:32.369+0000] {processor.py:186} INFO - Started process (PID=439) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:26:32.370+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:26:32.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.371+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:26:32.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.560+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:26:32.566+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:26:32.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.218 seconds
[2025-07-18T11:27:03.055+0000] {processor.py:186} INFO - Started process (PID=568) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:27:03.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:27:03.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.058+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:27:03.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.120+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:27:03.127+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:27:03.147+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.098 seconds
[2025-07-18T11:27:34.006+0000] {processor.py:186} INFO - Started process (PID=701) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:27:34.007+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:27:34.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:34.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:27:34.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:34.069+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:27:34.077+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:27:34.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.094 seconds
[2025-07-18T11:28:04.466+0000] {processor.py:186} INFO - Started process (PID=832) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:28:04.467+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:28:04.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.469+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:28:04.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.527+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:28:04.534+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:28:04.552+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.092 seconds
[2025-07-18T11:28:34.866+0000] {processor.py:186} INFO - Started process (PID=963) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:28:34.867+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:28:34.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.869+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:28:34.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.925+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:28:34.932+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:28:34.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.092 seconds
[2025-07-18T11:29:05.491+0000] {processor.py:186} INFO - Started process (PID=1092) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:29:05.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:29:05.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.494+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:29:05.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.551+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:29:05.555+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:29:05.572+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.087 seconds
[2025-07-18T11:29:36.410+0000] {processor.py:186} INFO - Started process (PID=1223) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:29:36.411+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:29:36.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:29:36.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.472+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:29:36.479+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:29:36.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.095 seconds
[2025-07-18T11:30:06.839+0000] {processor.py:186} INFO - Started process (PID=1349) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:30:06.840+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:30:06.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.842+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:30:06.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.900+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:30:06.906+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:30:06.929+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.098 seconds
[2025-07-18T11:30:37.947+0000] {processor.py:186} INFO - Started process (PID=1485) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:30:37.948+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:30:37.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.950+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:30:38.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:38.013+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:30:38.021+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:30:38.045+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.105 seconds
[2025-07-18T11:31:08.827+0000] {processor.py:186} INFO - Started process (PID=1618) to work on /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:31:08.828+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/set_calories_trends_pipeline.py for tasks to queue
[2025-07-18T11:31:08.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:31:08.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.891+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/set_calories_trends_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 995, in exec_module
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/set_calories_trends_pipeline.py", line 30, in <module>
    BEARER_TOKEN = config.get('DATA', 'BEARER_TOKEN')
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 759, in get
    d = self._unify_values(section, vars)
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.12/configparser.py", line 1132, in _unify_values
    raise NoSectionError(section) from None
configparser.NoSectionError: No section: 'DATA'
[2025-07-18T11:31:08.896+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/set_calories_trends_pipeline.py
[2025-07-18T11:31:08.916+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/set_calories_trends_pipeline.py took 0.095 seconds
