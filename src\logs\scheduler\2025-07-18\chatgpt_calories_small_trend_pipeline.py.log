[2025-07-18T11:25:56.378+0000] {processor.py:186} INFO - Started process (PID=201) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:25:56.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:25:56.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:25:56.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.473+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:56.482+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:25:56.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.635+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.653+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.662+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.672+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.677+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.684+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.690+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.691+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:56.872+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:56.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.873+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.874+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:25:56.895+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.523 seconds
[2025-07-18T11:26:27.947+0000] {processor.py:186} INFO - Started process (PID=334) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:27.948+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:26:27.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.951+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:28.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.027+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.036+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:28.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.284+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:28.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.300+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:26:28.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.380 seconds
[2025-07-18T11:26:58.907+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:58.908+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:26:58.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.910+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:59.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.120+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.128+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:59.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.224+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:59.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.235+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:26:59.254+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.353 seconds
[2025-07-18T11:27:29.360+0000] {processor.py:186} INFO - Started process (PID=601) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:27:29.361+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:27:29.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.363+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:27:29.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.431+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:29.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:27:29.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.536+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:29.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.546+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:27:29.566+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.211 seconds
[2025-07-18T11:28:00.190+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:00.191+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:28:00.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.193+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:00.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.268+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:00.277+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:00.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.375+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:00.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.385+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:28:00.406+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.222 seconds
[2025-07-18T11:28:30.685+0000] {processor.py:186} INFO - Started process (PID=863) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:30.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:28:30.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:30.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.775+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.784+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:30.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.894+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.905+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:28:30.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.250 seconds
[2025-07-18T11:29:01.037+0000] {processor.py:186} INFO - Started process (PID=994) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:01.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:29:01.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:01.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.109+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:01.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:01.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.217+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:01.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.228+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:29:01.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.219 seconds
[2025-07-18T11:29:31.732+0000] {processor.py:186} INFO - Started process (PID=1125) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:31.734+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:29:31.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.736+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:31.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.814+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.823+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:31.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:29:31.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.242 seconds
[2025-07-18T11:30:02.644+0000] {processor.py:186} INFO - Started process (PID=1251) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:02.645+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:30:02.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.647+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:02.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.720+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.727+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:02.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.823+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.834+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:30:02.853+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.214 seconds
[2025-07-18T11:30:33.316+0000] {processor.py:186} INFO - Started process (PID=1387) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:33.317+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:30:33.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:33.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.398+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:33.406+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:33.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.517+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:33.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.531+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:30:33.554+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.244 seconds
[2025-07-18T11:31:03.969+0000] {processor.py:186} INFO - Started process (PID=1518) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:03.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:31:03.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.974+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:04.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.072+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:04.080+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:04.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.217+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:04.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.234+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:31:04.262+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.307 seconds
[2025-07-18T11:31:34.691+0000] {processor.py:186} INFO - Started process (PID=1642) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:34.692+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:31:34.696+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:34.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.781+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:34.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:34.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:34.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:31:34.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.271 seconds
