[2025-07-18T11:25:56.378+0000] {processor.py:186} INFO - Started process (PID=201) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:25:56.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:25:56.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.382+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:25:56.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.473+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:56.482+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:25:56.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.635+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.653+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.662+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.672+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.677+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.684+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.690+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.691+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:56.872+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:56.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.873+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_calories_small_trend_pipeline
[2025-07-18T11:25:56.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.874+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:25:56.895+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.523 seconds
[2025-07-18T11:26:27.947+0000] {processor.py:186} INFO - Started process (PID=334) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:27.948+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:26:27.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.951+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:28.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.027+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.036+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:28.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.284+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:28.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.300+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:26:28.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.380 seconds
[2025-07-18T11:26:58.907+0000] {processor.py:186} INFO - Started process (PID=470) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:58.908+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:26:58.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.910+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:59.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.120+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.128+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:26:59.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.224+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:59.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.235+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:26:59.254+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.353 seconds
[2025-07-18T11:27:29.360+0000] {processor.py:186} INFO - Started process (PID=601) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:27:29.361+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:27:29.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.363+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:27:29.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.431+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:29.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:27:29.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.536+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:29.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.546+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:27:29.566+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.211 seconds
[2025-07-18T11:28:00.190+0000] {processor.py:186} INFO - Started process (PID=732) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:00.191+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:28:00.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.193+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:00.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.268+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:00.277+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:00.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.375+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:00.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.385+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:28:00.406+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.222 seconds
[2025-07-18T11:28:30.685+0000] {processor.py:186} INFO - Started process (PID=863) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:30.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:28:30.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:30.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.775+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.784+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:28:30.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.894+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.905+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:28:30.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.250 seconds
[2025-07-18T11:29:01.037+0000] {processor.py:186} INFO - Started process (PID=994) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:01.037+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:29:01.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.040+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:01.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.109+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:01.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:01.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.217+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:01.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.228+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:29:01.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.219 seconds
[2025-07-18T11:29:31.732+0000] {processor.py:186} INFO - Started process (PID=1125) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:31.734+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:29:31.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.736+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:31.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.814+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.823+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:29:31.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:29:31.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.242 seconds
[2025-07-18T11:30:02.644+0000] {processor.py:186} INFO - Started process (PID=1251) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:02.645+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:30:02.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.647+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:02.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.720+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.727+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:02.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.823+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.834+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:30:02.853+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.214 seconds
[2025-07-18T11:30:33.316+0000] {processor.py:186} INFO - Started process (PID=1387) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:33.317+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:30:33.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.320+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:33.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.398+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:33.406+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:30:33.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.517+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:33.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.531+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:30:33.554+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.244 seconds
[2025-07-18T11:31:03.969+0000] {processor.py:186} INFO - Started process (PID=1518) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:03.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:31:03.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.974+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:04.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.072+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:04.080+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:04.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.217+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:04.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.234+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:31:04.262+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.307 seconds
[2025-07-18T11:31:34.691+0000] {processor.py:186} INFO - Started process (PID=1642) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:34.692+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:31:34.696+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:34.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.781+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:34.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:31:34.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:34.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.922+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:31:34.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.271 seconds
[2025-07-18T11:32:05.578+0000] {processor.py:186} INFO - Started process (PID=1775) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:32:05.579+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:32:05.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.581+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:32:05.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.657+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:05.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:32:05.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.775+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:05.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:32:05.806+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.234 seconds
[2025-07-18T11:32:35.857+0000] {processor.py:186} INFO - Started process (PID=1909) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:32:35.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:32:35.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.860+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:32:35.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.935+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:35.944+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:32:36.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:36.042+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:36.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:36.052+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:32:36.071+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.220 seconds
[2025-07-18T11:33:06.304+0000] {processor.py:186} INFO - Started process (PID=2040) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:33:06.305+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:33:06.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.308+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:33:06.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.383+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:06.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:33:06.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.499+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:06.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.510+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:33:06.531+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.232 seconds
[2025-07-18T11:33:37.247+0000] {processor.py:186} INFO - Started process (PID=2174) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:33:37.248+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:33:37.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.251+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:33:37.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.609+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:37.618+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:33:37.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.716+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:37.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.727+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:33:37.894+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.654 seconds
[2025-07-18T11:34:08.333+0000] {processor.py:186} INFO - Started process (PID=2328) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:34:08.334+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:34:08.336+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.336+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:34:08.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.743+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:08.751+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:34:08.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.862+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:09.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.011+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:34:09.033+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.708 seconds
[2025-07-18T11:34:39.317+0000] {processor.py:186} INFO - Started process (PID=2480) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:34:39.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:34:39.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.322+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:34:39.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.713+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:39.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:34:39.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.858+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:40.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.052+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:34:40.070+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.762 seconds
[2025-07-18T11:35:10.503+0000] {processor.py:186} INFO - Started process (PID=2633) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:35:10.505+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:35:10.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.508+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:35:10.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.949+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:10.958+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:35:11.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:11.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:11.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:11.290+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:35:11.308+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.811 seconds
[2025-07-18T11:35:41.428+0000] {processor.py:186} INFO - Started process (PID=2786) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:35:41.430+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:35:41.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.432+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:35:41.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.825+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:41.835+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:35:42.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.113+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:42.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:35:42.144+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.723 seconds
[2025-07-18T11:36:12.737+0000] {processor.py:186} INFO - Started process (PID=2939) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:36:12.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:36:12.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.741+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:36:13.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.109+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:13.119+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:36:13.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.406+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:13.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.417+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:36:13.438+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.708 seconds
[2025-07-18T11:36:43.610+0000] {processor.py:186} INFO - Started process (PID=3092) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:36:43.612+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:36:43.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.615+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:36:43.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.964+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:43.974+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:36:44.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.210+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:44.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.221+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:36:44.241+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.637 seconds
[2025-07-18T11:37:14.503+0000] {processor.py:186} INFO - Started process (PID=3246) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:37:14.504+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:37:14.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:37:14.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.871+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:14.880+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:37:15.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.135+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:15.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.145+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:37:15.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.669 seconds
[2025-07-18T11:37:45.648+0000] {processor.py:186} INFO - Started process (PID=3399) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:37:45.649+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:37:45.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.652+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:37:46.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:46.877+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:47.057+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:37:47.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:47.942+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:47.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:47.964+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:37:48.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 2.366 seconds
[2025-07-18T11:38:18.531+0000] {processor.py:186} INFO - Started process (PID=3555) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:38:18.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:38:18.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:18.535+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:38:19.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.038+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:19.046+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:38:19.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.145+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:19.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:38:19.176+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.651 seconds
[2025-07-18T11:38:49.264+0000] {processor.py:186} INFO - Started process (PID=3705) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:38:49.265+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:38:49.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.267+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:38:49.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.840+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:49.848+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:38:49.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.947+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:49.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.955+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:38:49.974+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.716 seconds
[2025-07-18T11:39:20.113+0000] {processor.py:186} INFO - Started process (PID=3858) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:39:20.114+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:39:20.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:39:20.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.701+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:20.707+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:39:20.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.801+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:20.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.810+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:39:20.830+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.724 seconds
[2025-07-18T11:39:51.010+0000] {processor.py:186} INFO - Started process (PID=4011) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:39:51.010+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:39:51.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.012+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:39:51.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.529+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:51.537+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:39:51.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.630+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:51.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.641+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:39:51.661+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.657 seconds
[2025-07-18T11:40:21.874+0000] {processor.py:186} INFO - Started process (PID=4164) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:40:21.875+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:40:21.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:21.877+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:40:22.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:22.405+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:22.412+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:40:22.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:22.506+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:22.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:22.517+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:40:22.539+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.671 seconds
[2025-07-18T11:40:53.407+0000] {processor.py:186} INFO - Started process (PID=4317) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:40:53.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:40:53.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:53.411+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:40:53.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:53.927+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:53.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:40:54.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.028+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:54.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.040+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:40:54.061+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.661 seconds
[2025-07-18T11:41:24.562+0000] {processor.py:186} INFO - Started process (PID=4476) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:41:24.563+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:41:24.566+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:24.566+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:41:25.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.072+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:25.079+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:41:25.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.164+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:25.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.173+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:41:25.192+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.636 seconds
[2025-07-18T11:41:55.356+0000] {processor.py:186} INFO - Started process (PID=4635) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:41:55.357+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:41:55.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:55.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:41:55.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:55.872+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:55.879+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:41:55.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:55.974+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:55.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:55.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:41:56.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.655 seconds
[2025-07-18T11:42:26.560+0000] {processor.py:186} INFO - Started process (PID=4794) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:42:26.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:42:26.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:26.563+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:42:27.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.120+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:27.128+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:42:27.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.230+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:27.241+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.241+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:42:27.260+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.706 seconds
[2025-07-18T11:42:57.384+0000] {processor.py:186} INFO - Started process (PID=4953) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:42:57.385+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:42:57.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:57.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:42:57.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:57.938+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:57.946+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:42:58.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:58.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.055+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:42:58.075+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.697 seconds
[2025-07-18T11:43:28.180+0000] {processor.py:186} INFO - Started process (PID=5118) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:43:28.181+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:43:28.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:43:28.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.754+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:28.762+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:43:28.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:28.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.871+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:43:28.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.715 seconds
[2025-07-18T11:43:59.486+0000] {processor.py:186} INFO - Started process (PID=5277) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:43:59.487+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:43:59.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:59.489+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:44:00.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:00.032+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:00.041+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:44:00.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:00.137+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:00.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:00.148+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:44:00.166+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.687 seconds
[2025-07-18T11:44:30.423+0000] {processor.py:186} INFO - Started process (PID=5436) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:44:30.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:44:30.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:30.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:44:31.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.029+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:31.036+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:44:31.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.144+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:31.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:44:31.178+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.762 seconds
[2025-07-18T11:45:01.306+0000] {processor.py:186} INFO - Started process (PID=5595) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:45:01.306+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:45:01.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:01.309+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:45:01.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:01.897+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:01.904+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:45:02.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:02.015+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:02.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:02.026+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:45:02.048+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.749 seconds
[2025-07-18T11:45:32.763+0000] {processor.py:186} INFO - Started process (PID=5754) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:45:32.764+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:45:32.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:32.767+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:45:33.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:33.326+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:33.333+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:45:33.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:33.438+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:33.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:33.448+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:45:33.467+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.710 seconds
[2025-07-18T11:46:03.826+0000] {processor.py:186} INFO - Started process (PID=5913) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:46:03.827+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:46:03.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:03.830+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:46:04.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:04.376+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:04.384+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:46:04.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:04.478+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:04.488+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:04.488+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:46:04.508+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.689 seconds
[2025-07-18T11:46:34.642+0000] {processor.py:186} INFO - Started process (PID=6072) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:46:34.643+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:46:34.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:34.646+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:46:35.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:35.166+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:35.173+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:46:35.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:35.270+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:35.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:35.280+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:46:35.302+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.667 seconds
[2025-07-18T11:47:05.659+0000] {processor.py:186} INFO - Started process (PID=6231) to work on /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:47:05.660+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py for tasks to queue
[2025-07-18T11:47:05.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:05.663+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:47:06.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:06.162+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:06.170+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_small_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py
[2025-07-18T11:47:06.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:06.278+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:06.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:06.290+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_small_trend_pipeline to None, run_after=None
[2025-07-18T11:47:06.306+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py took 0.654 seconds
