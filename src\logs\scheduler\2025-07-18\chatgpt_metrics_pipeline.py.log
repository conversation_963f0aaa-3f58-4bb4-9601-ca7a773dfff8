[2025-07-18T11:26:00.003+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:00.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:26:00.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.008+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:00.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.084+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:00.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:00.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.347+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.357+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.363+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.371+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.378+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.384+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.392+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.393+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:00.404+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:00.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.404+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_metrics_pipeline
[2025-07-18T11:26:00.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.405+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:26:00.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.425 seconds
[2025-07-18T11:26:31.032+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:31.033+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:26:31.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:31.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.257+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:31.265+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:31.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.364+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:31.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.374+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:26:31.394+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.369 seconds
[2025-07-18T11:27:02.155+0000] {processor.py:186} INFO - Started process (PID=538) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:02.156+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:27:02.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.159+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:02.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.245+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.254+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:02.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.374+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:27:02.403+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.255 seconds
[2025-07-18T11:27:32.803+0000] {processor.py:186} INFO - Started process (PID=669) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:32.804+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:27:32.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:32.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.884+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.893+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:32.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.989+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.003+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:27:33.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.229 seconds
[2025-07-18T11:28:03.328+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:03.329+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:28:03.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.331+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:03.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.405+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:03.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:03.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.510+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:03.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.522+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:28:03.541+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.218 seconds
[2025-07-18T11:28:33.921+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:33.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:28:33.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:33.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.997+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.005+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:34.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.107+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.119+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:28:34.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.225 seconds
[2025-07-18T11:29:04.709+0000] {processor.py:186} INFO - Started process (PID=1062) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:04.710+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:29:04.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.712+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:04.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.799+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:04.808+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:04.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:04.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.921+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:29:04.943+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.241 seconds
[2025-07-18T11:29:35.747+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:35.748+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:29:35.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.751+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:35.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.830+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.837+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:35.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.950+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:35.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.962+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:29:35.983+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.243 seconds
[2025-07-18T11:30:06.259+0000] {processor.py:186} INFO - Started process (PID=1331) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:06.260+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:30:06.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.262+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:06.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:06.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:06.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.462+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:06.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.474+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:30:06.500+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.247 seconds
[2025-07-18T11:30:37.267+0000] {processor.py:186} INFO - Started process (PID=1457) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:37.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:30:37.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.271+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:37.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.344+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:37.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.460+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.470+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:30:37.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.237 seconds
[2025-07-18T11:31:07.678+0000] {processor.py:186} INFO - Started process (PID=1586) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:07.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:31:07.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:07.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.765+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:07.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.893+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:31:07.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.241 seconds
