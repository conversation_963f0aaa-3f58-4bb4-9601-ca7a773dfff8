[2025-07-18T11:26:00.003+0000] {processor.py:186} INFO - Started process (PID=276) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:00.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:26:00.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.008+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:00.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.084+0000] {cost_tracking.py:58} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:00.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:00.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.347+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.357+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.363+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.371+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.378+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.384+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.392+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_metrics_pipeline
[2025-07-18T11:26:00.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.393+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:00.404+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:00.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.404+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_metrics_pipeline
[2025-07-18T11:26:00.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.405+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:26:00.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.425 seconds
[2025-07-18T11:26:31.032+0000] {processor.py:186} INFO - Started process (PID=409) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:31.033+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:26:31.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:31.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.257+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:31.265+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:26:31.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.364+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:31.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.374+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:26:31.394+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.369 seconds
[2025-07-18T11:27:02.155+0000] {processor.py:186} INFO - Started process (PID=538) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:02.156+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:27:02.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.159+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:02.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.245+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.254+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:02.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.374+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.384+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:27:02.403+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.255 seconds
[2025-07-18T11:27:32.803+0000] {processor.py:186} INFO - Started process (PID=669) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:32.804+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:27:32.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.806+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:32.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.884+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.893+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:27:32.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.989+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.003+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:27:33.025+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.229 seconds
[2025-07-18T11:28:03.328+0000] {processor.py:186} INFO - Started process (PID=800) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:03.329+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:28:03.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.331+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:03.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.405+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:03.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:03.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.510+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:03.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.522+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:28:03.541+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.218 seconds
[2025-07-18T11:28:33.921+0000] {processor.py:186} INFO - Started process (PID=933) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:33.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:28:33.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:33.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.997+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.005+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:28:34.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.107+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.119+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:28:34.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.225 seconds
[2025-07-18T11:29:04.709+0000] {processor.py:186} INFO - Started process (PID=1062) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:04.710+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:29:04.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.712+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:04.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.799+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:04.808+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:04.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:04.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.921+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:29:04.943+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.241 seconds
[2025-07-18T11:29:35.747+0000] {processor.py:186} INFO - Started process (PID=1195) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:35.748+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:29:35.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.751+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:35.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.830+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.837+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:29:35.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.950+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:35.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.962+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:29:35.983+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.243 seconds
[2025-07-18T11:30:06.259+0000] {processor.py:186} INFO - Started process (PID=1331) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:06.260+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:30:06.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.262+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:06.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:06.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:06.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.462+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:06.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.474+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:30:06.500+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.247 seconds
[2025-07-18T11:30:37.267+0000] {processor.py:186} INFO - Started process (PID=1457) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:37.268+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:30:37.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.271+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:37.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.344+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:30:37.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.460+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.470+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:30:37.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.237 seconds
[2025-07-18T11:31:07.678+0000] {processor.py:186} INFO - Started process (PID=1586) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:07.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:31:07.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:07.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.765+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:07.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.893+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:31:07.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.241 seconds
[2025-07-18T11:31:38.364+0000] {processor.py:186} INFO - Started process (PID=1719) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:38.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:31:38.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:38.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.445+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:38.454+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:31:38.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.554+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:38.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.565+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:31:38.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.228 seconds
[2025-07-18T11:32:08.875+0000] {processor.py:186} INFO - Started process (PID=1850) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:32:08.876+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:32:08.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.878+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:32:08.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.959+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:08.968+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:32:09.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.069+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:09.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.079+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:32:09.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.232 seconds
[2025-07-18T11:32:39.471+0000] {processor.py:186} INFO - Started process (PID=1981) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:32:39.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:32:39.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:32:39.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.548+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:39.556+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:32:39.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:39.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.671+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:32:39.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.227 seconds
[2025-07-18T11:33:10.067+0000] {processor.py:186} INFO - Started process (PID=2112) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:33:10.068+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:33:10.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.071+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:33:10.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.166+0000] {cost_tracking.py:76} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:10.175+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:33:10.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.275+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:10.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.287+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:33:10.307+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.247 seconds
[2025-07-18T11:33:41.754+0000] {processor.py:186} INFO - Started process (PID=2259) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:33:41.754+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:33:41.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.757+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:33:42.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.130+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:42.140+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:33:42.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.269+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:42.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.282+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:33:42.306+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.559 seconds
[2025-07-18T11:34:13.284+0000] {processor.py:186} INFO - Started process (PID=2412) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:34:13.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:34:13.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:34:13.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.664+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:13.674+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:34:13.792+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.791+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:13.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.802+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:34:13.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.546 seconds
[2025-07-18T11:34:44.553+0000] {processor.py:186} INFO - Started process (PID=2565) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:34:44.555+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:34:44.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.558+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:34:44.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.931+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:44.941+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:34:45.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.047+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:45.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.059+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:34:45.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.535 seconds
[2025-07-18T11:35:16.166+0000] {processor.py:186} INFO - Started process (PID=2718) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:35:16.168+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:35:16.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.172+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:35:16.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.584+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:16.594+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:35:16.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.702+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:16.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.713+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:35:16.734+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.578 seconds
[2025-07-18T11:35:47.676+0000] {processor.py:186} INFO - Started process (PID=2871) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:35:47.678+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:35:47.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:35:48.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.063+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:48.071+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:35:48.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.183+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:48.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.196+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:35:48.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.559 seconds
[2025-07-18T11:36:18.849+0000] {processor.py:186} INFO - Started process (PID=3029) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:36:18.850+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:36:18.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:36:19.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.265+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:19.277+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:36:19.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.422+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:19.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.436+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:36:19.458+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.616 seconds
[2025-07-18T11:36:50.572+0000] {processor.py:186} INFO - Started process (PID=3182) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:36:50.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:36:50.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.575+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:36:51.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.017+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:51.026+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:36:51.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.166+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:51.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.184+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:36:51.414+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.848 seconds
[2025-07-18T11:37:21.999+0000] {processor.py:186} INFO - Started process (PID=3338) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:37:22.000+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:37:22.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.002+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:37:22.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.393+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:22.402+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:37:22.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.507+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:22.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.697+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:37:22.721+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.728 seconds
[2025-07-18T11:37:53.323+0000] {processor.py:186} INFO - Started process (PID=3491) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:37:53.324+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:37:53.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.326+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:37:53.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.705+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:53.714+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:37:53.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.984+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:53.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.997+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:37:54.018+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.702 seconds
[2025-07-18T11:38:24.702+0000] {processor.py:186} INFO - Started process (PID=3644) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:38:24.703+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:38:24.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:24.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:38:25.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:25.070+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:25.078+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:38:25.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:25.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:25.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:25.378+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:38:25.399+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.703 seconds
[2025-07-18T11:38:55.579+0000] {processor.py:186} INFO - Started process (PID=3797) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:38:55.580+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:38:55.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:55.583+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:38:56.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.074+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:56.082+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:38:56.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.172+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:56.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.183+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:38:56.204+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.631 seconds
[2025-07-18T11:39:26.848+0000] {processor.py:186} INFO - Started process (PID=3950) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:39:26.849+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:39:26.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.851+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:39:27.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:27.380+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:27.388+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:39:27.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:27.485+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:27.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:27.497+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:39:27.517+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.675 seconds
[2025-07-18T11:39:57.695+0000] {processor.py:186} INFO - Started process (PID=4109) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:39:57.696+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:39:57.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:57.698+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:39:58.241+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:58.240+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:58.249+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:39:58.347+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:58.346+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:58.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:58.358+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:39:58.377+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.688 seconds
[2025-07-18T11:40:28.749+0000] {processor.py:186} INFO - Started process (PID=4262) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:40:28.749+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:40:28.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:28.752+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:40:29.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:29.321+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:29.330+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:40:29.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:29.433+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:29.444+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:29.444+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:40:29.467+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.726 seconds
[2025-07-18T11:40:59.719+0000] {processor.py:186} INFO - Started process (PID=4421) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:40:59.720+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:40:59.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:59.722+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:41:00.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:00.235+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:00.243+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:41:00.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:00.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:00.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:00.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:41:00.365+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.653 seconds
[2025-07-18T11:41:30.587+0000] {processor.py:186} INFO - Started process (PID=4580) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:41:30.588+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:41:30.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:30.590+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:41:31.101+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:31.101+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:31.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:41:31.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:31.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:31.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:31.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:41:31.250+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.669 seconds
[2025-07-18T11:42:01.617+0000] {processor.py:186} INFO - Started process (PID=4739) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:42:01.618+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:42:01.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:01.621+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:42:02.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:02.078+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:02.086+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:42:02.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:02.180+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:02.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:02.190+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:42:02.211+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.601 seconds
[2025-07-18T11:42:32.397+0000] {processor.py:186} INFO - Started process (PID=4898) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:42:32.398+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:42:32.401+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.400+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:42:32.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.864+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:32.872+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:42:32.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:32.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.985+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:42:33.005+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.615 seconds
[2025-07-18T11:43:03.385+0000] {processor.py:186} INFO - Started process (PID=5057) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:43:03.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:43:03.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:43:03.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.870+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:03.879+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:43:03.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.972+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:03.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.983+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:43:04.003+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.623 seconds
[2025-07-18T11:43:34.234+0000] {processor.py:186} INFO - Started process (PID=5219) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:43:34.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:43:34.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.238+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:43:34.770+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.770+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:34.777+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:43:34.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.873+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:34.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.885+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:43:34.904+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.677 seconds
[2025-07-18T11:44:05.201+0000] {processor.py:186} INFO - Started process (PID=5378) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:44:05.202+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:44:05.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:05.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:44:05.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:05.724+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:05.733+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:44:05.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:05.827+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:05.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:05.838+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:44:05.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.665 seconds
[2025-07-18T11:44:36.563+0000] {processor.py:186} INFO - Started process (PID=5537) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:44:36.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:44:36.568+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:36.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:44:37.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:37.116+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:37.125+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:44:37.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:37.235+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:37.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:37.246+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:44:37.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.709 seconds
[2025-07-18T11:45:07.666+0000] {processor.py:186} INFO - Started process (PID=5698) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:45:07.666+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:45:07.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:07.668+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:45:08.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:08.158+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:08.166+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:45:08.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:08.261+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:08.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:08.271+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:45:08.292+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.632 seconds
[2025-07-18T11:45:38.836+0000] {processor.py:186} INFO - Started process (PID=5855) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:45:38.837+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:45:38.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:45:39.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:39.334+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:39.341+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:45:39.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:39.432+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:39.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:39.443+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:45:39.462+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.632 seconds
[2025-07-18T11:46:09.622+0000] {processor.py:186} INFO - Started process (PID=6014) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:46:09.623+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:46:09.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:09.627+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:46:10.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:10.252+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:10.259+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:46:10.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:10.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:10.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:10.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:46:10.402+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.790 seconds
[2025-07-18T11:46:41.015+0000] {processor.py:186} INFO - Started process (PID=6175) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:46:41.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:46:41.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:41.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:46:41.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:41.530+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:41.538+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:46:41.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:41.637+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:41.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:41.647+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:46:41.667+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.658 seconds
[2025-07-18T11:47:12.275+0000] {processor.py:186} INFO - Started process (PID=6332) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:47:12.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:47:12.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:12.279+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:47:12.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:12.778+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:12.785+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:47:12.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:12.901+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:12.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:12.914+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:47:12.935+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.668 seconds
[2025-07-18T11:47:43.181+0000] {processor.py:186} INFO - Started process (PID=6491) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:47:43.182+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:47:43.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:43.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:47:43.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:43.692+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:43.699+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:47:43.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:43.807+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:43.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:43.818+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:47:43.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.663 seconds
[2025-07-18T11:48:14.341+0000] {processor.py:186} INFO - Started process (PID=6650) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:48:14.343+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:48:14.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:14.345+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:48:14.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:14.889+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:14.897+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:48:15.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:15.006+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:15.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:15.016+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:48:15.040+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.705 seconds
[2025-07-18T11:48:45.772+0000] {processor.py:186} INFO - Started process (PID=6811) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:48:45.773+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:48:45.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:45.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:48:46.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:46.257+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:46.265+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:48:46.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:46.358+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:46.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:46.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:48:46.393+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.627 seconds
[2025-07-18T11:49:16.815+0000] {processor.py:186} INFO - Started process (PID=6970) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:49:16.816+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:49:16.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:16.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:49:17.336+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:17.336+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:17.346+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:49:17.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:17.466+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:17.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:17.477+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:49:17.499+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.692 seconds
[2025-07-18T11:49:47.789+0000] {processor.py:186} INFO - Started process (PID=7129) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:49:47.790+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:49:47.792+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:47.792+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:49:48.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:48.277+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:48.286+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:49:48.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:48.397+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:48.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:48.409+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:49:48.431+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.649 seconds
[2025-07-18T11:50:18.619+0000] {processor.py:186} INFO - Started process (PID=7286) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:50:18.620+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:50:18.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:18.623+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:50:19.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:19.107+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:19.115+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:50:19.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:19.206+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:19.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:19.216+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:50:19.235+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.622 seconds
[2025-07-18T11:50:49.896+0000] {processor.py:186} INFO - Started process (PID=7445) to work on /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:50:49.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_metrics_pipeline.py for tasks to queue
[2025-07-18T11:50:49.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:49.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:50:50.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:50.388+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:50.396+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_metrics_pipeline' retrieved from /opt/airflow/dags/chatgpt_metrics_pipeline.py
[2025-07-18T11:50:50.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:50.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:50.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:50.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_metrics_pipeline to None, run_after=None
[2025-07-18T11:50:50.529+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_metrics_pipeline.py took 0.640 seconds
