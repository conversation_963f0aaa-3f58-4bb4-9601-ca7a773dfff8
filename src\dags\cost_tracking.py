"""
Централизованная система мониторинга затрат для Biome AI
Безопасный модуль для расчета и отслеживания стоимости операций
"""

import tiktoken
import json
import redis
from datetime import datetime, timezone, timedelta
from typing import Dict, Optional, List, Any
from enum import Enum
import logging
import hashlib
import csv
import pandas as pd
from pathlib import Path

# Логи
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OperationType(str, Enum):
    """Типы операций для мониторинга затрат"""
    CHAT_COMPLETION = "chat_completion"
    RECOMMENDATION_GENERATION = "recommendation_generation"
    RISK_ASSESSMENT = "risk_assessment"
    IMAGE_ANALYSIS = "image_analysis"
    FOOD_ANALYSIS = "food_analysis"
    CHALLENGE_GENERATION = "challenge_generation"
    ONBOARDING = "onboarding"
    GENERAL_CHAT = "general_chat"
    PERPLEXITY_SEARCH = "perplexity_search"

# Маппинг пайплайнов на русские названия согласно требованиям
PIPELINE_RUSSIAN_NAMES = {
    "chatgpt_message_recommendation_pipeline": "ГЕНЕРАЦИЯ РИСКОВ И РЕКОМЕНДАЦИЙ",
    "chatgpt_message_pipeline": "СООБЩЕНИЕ ПО РИСКУ",
    "chatgpt_message_risk_pipeline": "СООБЩЕНИЕ ПО РЕКОМЕНДАЦИИ",
    "chatgpt_metrics_pipeline": "АНАЛИЗ МЕТРИК",
    "chatgpt_classify_pipeline": "КЛАССИФИКАТОР СООБЩЕНИЙ",
    "chatgpt_generate_challenges_pipeline": "ГЕНЕРАЦИЯ ЧЕЛЛЕНДЖЕЙ В РЕЖИМЕ АННОТИРОВАНИЯ",
    "chatgpt_pdf_pipeline": "РЕЖИМ ОБЫЧНОГО ОБЩЕНИЯ В ЧАТЕ",
    "chatgpt_image_pipeline": "АНАЛИЗ ФОТО (ТЕКСТОВЫЙ АНАЛИЗ)",
    "chatgpt_goals_pipeline": "ОПРЕДЕЛЕНИЕ ЦЕЛЕЙ ПОЛЬЗОВАТЕЛЯ",
    "chatgpt_unified_question_pipeline": "ГЕНЕРАЦИЯ ПО РУССКИМ АНКЕТИРОВАНИЯМ",
    "chatgpt_analyze_food_pipeline": "ГЕНЕРАЦИЯ ОТКРЫВАЮЩЕГО СООБЩЕНИЯ ДЛЯ РИСК+РЕКОМЕНДАЦИИ",
    "chatgpt_image_object_pipeline": "ОРГАНИЗАЦИЯ ВОПРОСОВ ДЛЯ ПОСТОНБОРДИНГА",
    "chatgpt_describe_challenge_pipeline": "ГЕНЕРАЦИЯ СООБЩЕНИЙ ПО АНАЛИЗУ ФОТО",
    "perplexity_pipeline": "ИТОГО ЗА ПОЛЬЗОВАТЕЛЬ:"
}

class CostTracker:
    """
    Безопасный трекер затрат с интеграцией Redis
    """
    
    def __init__(self, redis_host: str = "localhost", redis_port: int = 6379, redis_db: int = 0):
        """
        Инициализация трекера затрат
        
        Args:
            redis_host: Хост Redis сервера
            redis_port: Порт Redis сервера  
            redis_db: Номер базы данных Redis
        """
        try:
            self.redis_client = redis.Redis(
                host=redis_host, 
                port=redis_port, 
                db=redis_db,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            # Проверяем соединение
            self.redis_client.ping()
            logger.info("Успешное подключение к Redis")
        except Exception as e:
            logger.error(f"Ошибка подключения к Redis: {e}")
            self.redis_client = None
    
    def analyze_token_usage(
        self, 
        input_prompt: str, 
        output_response: str, 
        model: str = "gpt-4o",
        cost_rate_input: float = 0.03,
        cost_rate_output: float = 0.06,
        tokens_unit: int = 1000
    ) -> Dict[str, Any]:
        """
        Анализирует использование токенов для входного промпта и выходного ответа
        
        Args:
            input_prompt: Входной промпт
            output_response: Выходной ответ
            model: Модель OpenAI
            cost_rate_input: Стоимость за 1000 входных токенов в USD
            cost_rate_output: Стоимость за 1000 выходных токенов в USD
            tokens_unit: Единица измерения токенов для расчета стоимости
            
        Returns:
            Словарь с информацией о токенах и стоимости
        """
        try:
            encoder = tiktoken.encoding_for_model(model)
            tokens_input = len(encoder.encode(input_prompt))
            tokens_output = len(encoder.encode(output_response))
            
            cost_input = (tokens_input / tokens_unit) * cost_rate_input
            cost_output = (tokens_output / tokens_unit) * cost_rate_output
            total_cost = cost_input + cost_output
            
            result = {
                "model": model,
                "tokens_input": tokens_input,
                "tokens_output": tokens_output,
                "total_tokens": tokens_input + tokens_output,
                "cost_input": round(cost_input, 6),
                "cost_output": round(cost_output, 6),
                "total_cost": round(total_cost, 6),
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "cost_rate_input": cost_rate_input,
                "cost_rate_output": cost_rate_output
            }
            
            logger.info(f"Анализ токенов: {tokens_input} входных, {tokens_output} выходных, стоимость: ${total_cost:.6f}")
            return result
            
        except Exception as e:
            logger.error(f"Ошибка анализа токенов: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def calculate_storage_cost(
        self, 
        data_size_mb: float, 
        storage_days: int = 30,
        cost_per_gb_month: float = 0.023
    ) -> Dict[str, Any]:
        """
        Рассчитывает стоимость хранения данных
        
        Args:
            data_size_mb: Размер данных в мегабайтах
            storage_days: Количество дней хранения
            cost_per_gb_month: Стоимость за GB в месяц в USD
            
        Returns:
            Словарь с информацией о стоимости хранения
        """
        try:
            data_size_gb = data_size_mb / 1024
            storage_months = storage_days / 30
            storage_cost = data_size_gb * storage_months * cost_per_gb_month
            
            return {
                "data_size_mb": data_size_mb,
                "data_size_gb": round(data_size_gb, 6),
                "storage_days": storage_days,
                "storage_months": round(storage_months, 6),
                "storage_cost": round(storage_cost, 6),
                "cost_per_gb_month": cost_per_gb_month,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Ошибка расчета стоимости хранения: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def calculate_integration_cost(
        self, 
        api_calls: int,
        cost_per_call: float = 0.001
    ) -> Dict[str, Any]:
        """
        Рассчитывает стоимость интеграций и API вызовов
        
        Args:
            api_calls: Количество API вызовов
            cost_per_call: Стоимость за один вызов в USD
            
        Returns:
            Словарь с информацией о стоимости интеграций
        """
        try:
            integration_cost = api_calls * cost_per_call
            
            return {
                "api_calls": api_calls,
                "cost_per_call": cost_per_call,
                "integration_cost": round(integration_cost, 6),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        except Exception as e:
            logger.error(f"Ошибка расчета стоимости интеграций: {e}")
            return {
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat()
            }

    def track_operation_cost(
        self,
        operation_type: OperationType,
        user_id: Optional[int] = None,
        session_id: Optional[str] = None,
        token_usage: Optional[Dict] = None,
        storage_cost: Optional[Dict] = None,
        integration_cost: Optional[Dict] = None,
        additional_metadata: Optional[Dict] = None
    ) -> bool:
        """
        Безопасно сохраняет информацию о затратах операции в Redis

        Args:
            operation_type: Тип операции
            user_id: ID пользователя (опционально для безопасности)
            session_id: ID сессии
            token_usage: Данные об использовании токенов
            storage_cost: Данные о стоимости хранения
            integration_cost: Данные о стоимости интеграций
            additional_metadata: Дополнительные метаданные

        Returns:
            True если успешно сохранено, False в противном случае
        """
        if not self.redis_client:
            logger.warning("Redis недоступен, пропускаем сохранение метрик")
            return False

        try:
            # Сейф ключ для операции.
            timestamp = datetime.now(timezone.utc)
            operation_id = hashlib.md5(
                f"{operation_type}_{timestamp.isoformat()}_{session_id}".encode()
            ).hexdigest()[:16]

            # Сохраняемые данные
            cost_data = {
                "operation_id": operation_id,
                "operation_type": operation_type.value,
                "timestamp": timestamp.isoformat(),
                "session_id": session_id,
                "token_usage": token_usage or {},
                "storage_cost": storage_cost or {},
                "integration_cost": integration_cost or {},
                "additional_metadata": additional_metadata or {}
            }

            # Добавление user_id только если он есть.
            if user_id:
                cost_data["user_id_hash"] = hashlib.sha256(str(user_id).encode()).hexdigest()[:16]

            # Сохранение в Redis с TTL (30 дней)
            redis_key = f"cost_tracking:{operation_type.value}:{operation_id}"
            self.redis_client.setex(
                redis_key,
                2592000,  # 30 дней в секундах
                json.dumps(cost_data)
            )

            self._update_aggregated_stats(operation_type, cost_data)

            logger.info(f"Сохранены метрики для операции {operation_type.value}: {operation_id}")
            return True

        except Exception as e:
            logger.error(f"Ошибка сохранения метрик: {e}")
            return False

    def _update_aggregated_stats(self, operation_type: OperationType, cost_data: Dict) -> None:
        """
        Обновляет агрегированную статистику по операциям

        Args:
            operation_type: Тип операции
            cost_data: Данные о затратах
        """
        try:
            today = datetime.now(timezone.utc).date().isoformat()
            stats_key = f"cost_stats:daily:{operation_type.value}:{today}"

            current_stats = self.redis_client.get(stats_key)
            if current_stats:
                stats = json.loads(current_stats)
            else:
                stats = {
                    "operation_count": 0,
                    "total_cost": 0.0,
                    "total_tokens": 0,
                    "date": today,
                    "operation_type": operation_type.value
                }

           
            stats["operation_count"] += 1

            if cost_data.get("token_usage"):
                token_data = cost_data["token_usage"]
                stats["total_cost"] += token_data.get("total_cost", 0)
                stats["total_tokens"] += token_data.get("total_tokens", 0)

            if cost_data.get("storage_cost"):
                stats["total_cost"] += cost_data["storage_cost"].get("storage_cost", 0)

            if cost_data.get("integration_cost"):
                stats["total_cost"] += cost_data["integration_cost"].get("integration_cost", 0)

        
            self.redis_client.setex(
                stats_key,
                7776000,  # 90 дней в секундах
                json.dumps(stats)
            )

        except Exception as e:
            logger.error(f"Ошибка обновления агрегированной статистики: {e}")

    def get_operation_stats(
        self,
        operation_type: Optional[OperationType] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """
        Получает статистику по операциям за указанный период

        Args:
            operation_type: Тип операции (если None, то по всем типам)
            days: Количество дней для анализа

        Returns:
            Словарь со статистикой
        """
        if not self.redis_client:
            return {"error": "Redis недоступен"}

        try:
            stats = {}
            current_date = datetime.now(timezone.utc).date()

            
            operation_types = [operation_type] if operation_type else list(OperationType)

            for op_type in operation_types:
                op_stats = {
                    "total_operations": 0,
                    "total_cost": 0.0,
                    "total_tokens": 0,
                    "daily_breakdown": []
                }

                for i in range(days):
                    date = (current_date - timedelta(days=i)).isoformat()
                    stats_key = f"cost_stats:daily:{op_type.value}:{date}"

                    daily_data = self.redis_client.get(stats_key)
                    if daily_data:
                        daily_stats = json.loads(daily_data)
                        op_stats["total_operations"] += daily_stats.get("operation_count", 0)
                        op_stats["total_cost"] += daily_stats.get("total_cost", 0)
                        op_stats["total_tokens"] += daily_stats.get("total_tokens", 0)
                        op_stats["daily_breakdown"].append({
                            "date": date,
                            **daily_stats
                        })

                stats[op_type.value] = op_stats

            return {
                "period_days": days,
                "generated_at": datetime.now(timezone.utc).isoformat(),
                "statistics": stats
            }

        except Exception as e:
            logger.error(f"Ошибка получения статистики: {e}")
            return {"error": str(e)}

# Основные сценарии использования (тест-кейсы)
class UsageScenarios:
    """
    Определяет основные сценарии использования системы и их стоимость
    """

    @staticmethod
    def get_scenario_definitions() -> Dict[str, Dict]:
        """
        Возвращает определения основных сценариев использования
        """
        return {
            "onboarding": {
                "name": "Онбординг пользователя",
                "description": "Полный процесс онбординга нового пользователя",
                "operations": [
                    {"type": OperationType.ONBOARDING, "count": 1},
                    {"type": OperationType.RECOMMENDATION_GENERATION, "count": 3},
                    {"type": OperationType.RISK_ASSESSMENT, "count": 2}
                ],
                "estimated_tokens": 15000,
                "estimated_cost": 0.45
            },
            "daily_interaction": {
                "name": "Ежедневное взаимодействие",
                "description": "Типичное ежедневное использование приложения",
                "operations": [
                    {"type": OperationType.GENERAL_CHAT, "count": 5},
                    {"type": OperationType.FOOD_ANALYSIS, "count": 3},
                    {"type": OperationType.RECOMMENDATION_GENERATION, "count": 1}
                ],
                "estimated_tokens": 8000,
                "estimated_cost": 0.24
            },
            "challenge_creation": {
                "name": "Создание челленджа",
                "description": "Генерация персонализированного челленджа",
                "operations": [
                    {"type": OperationType.CHALLENGE_GENERATION, "count": 1},
                    {"type": OperationType.RECOMMENDATION_GENERATION, "count": 2}
                ],
                "estimated_tokens": 6000,
                "estimated_cost": 0.18
            },
            "risk_analysis": {
                "name": "Анализ рисков",
                "description": "Комплексная оценка рисков для здоровья",
                "operations": [
                    {"type": OperationType.RISK_ASSESSMENT, "count": 3},
                    {"type": OperationType.RECOMMENDATION_GENERATION, "count": 2}
                ],
                "estimated_tokens": 10000,
                "estimated_cost": 0.30
            },
            "image_analysis": {
                "name": "Анализ изображений еды",
                "description": "Анализ фотографий еды и расчет калорий",
                "operations": [
                    {"type": OperationType.IMAGE_ANALYSIS, "count": 1},
                    {"type": OperationType.FOOD_ANALYSIS, "count": 1}
                ],
                "estimated_tokens": 4000,
                "estimated_cost": 0.12
            }
        }

# Глобальный экземпляр трекера затрат
cost_tracker = CostTracker()


def track_openai_cost(response_data=None, response=None, context=None, conf=None):
    """
    Универсальная функция для отслеживания затрат OpenAI

    Args:
        response_data: JSON данные ответа OpenAI (dict)
        response: Объект ответа OpenAI (для совместимости)
        context: Контекст выполнения с dag_id, task_id, user_id
        conf: Конфигурация DAG для извлечения user_id

    Returns:
        dict: Анализ затрат
    """
    if context is None:
        context = {}

    # Извлекаем user_id из конфигурации если не передан в контексте
    if 'user_id' not in context and conf:
        context['user_id'] = conf.get('user_id')

    # Если передан объект response, извлекаем данные
    if response and hasattr(response, 'json'):
        try:
            response_data = response.json()
        except:
            response_data = response
    elif response and hasattr(response, 'usage'):
        # Для стандартного OpenAI клиента
        response_data = {
            'usage': {
                'prompt_tokens': response.usage.prompt_tokens,
                'completion_tokens': response.usage.completion_tokens,
                'total_tokens': response.usage.total_tokens
            }
        }

    if not response_data:
        return {'error': 'No response data provided'}

    # Извлекаем данные об использовании токенов
    usage = response_data.get('usage', {})
    if not usage:
        return {'error': 'No usage data in response'}

    input_tokens = usage.get('prompt_tokens', 0)
    output_tokens = usage.get('completion_tokens', 0)
    total_tokens = usage.get('total_tokens', input_tokens + output_tokens)

    # Рассчитываем стоимость (цены для gpt-4o-mini)
    input_cost = input_tokens * 0.00015 / 1000  # $0.00015 per 1K tokens
    output_cost = output_tokens * 0.0006 / 1000  # $0.0006 per 1K tokens
    total_cost = input_cost + output_cost

    # Создаем данные для сохранения
    token_usage = {
        'tokens_input': input_tokens,
        'tokens_output': output_tokens,
        'total_tokens': total_tokens,
        'cost_input': input_cost,
        'cost_output': output_cost,
        'total_cost': total_cost,
        'model': 'gpt-4o',
        'timestamp': datetime.now(timezone.utc).isoformat()
    }

    # Определяем тип операции на основе dag_id
    dag_id = context.get('dag_id', 'unknown')
    operation_type = OperationType.GENERAL_CHAT  # По умолчанию

    # Маппинг dag_id на operation_type
    dag_to_operation = {
        'chatgpt_message_recommendation_pipeline': OperationType.RECOMMENDATION_GENERATION,
        'chatgpt_message_pipeline': OperationType.GENERAL_CHAT,
        'chatgpt_message_risk_pipeline': OperationType.RISK_ASSESSMENT,
        'chatgpt_image_pipeline': OperationType.IMAGE_ANALYSIS,
        'chatgpt_analyze_food_pipeline': OperationType.FOOD_ANALYSIS,
        'chatgpt_generate_challenges_pipeline': OperationType.CHALLENGE_GENERATION,
        'chatgpt_goals_pipeline': OperationType.ONBOARDING,
        'perplexity_pipeline': OperationType.PERPLEXITY_SEARCH
    }

    if dag_id in dag_to_operation:
        operation_type = dag_to_operation[dag_id]

    # Сохраняем в Redis через CostTracker
    try:
        cost_tracker.track_operation_cost(
            operation_type=operation_type,
            user_id=context.get('user_id'),
            session_id=context.get('session_id', f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
            token_usage=token_usage,
            additional_metadata={
                'dag_id': dag_id,
                'task_id': context.get('task_id', 'unknown')
            }
        )
    except Exception as e:
        print(f"⚠️ Ошибка сохранения данных о затратах: {e}")

    return {
        'input_tokens': input_tokens,
        'output_tokens': output_tokens,
        'total_tokens': total_tokens,
        'input_cost': input_cost,
        'output_cost': output_cost,
        'total_cost': total_cost
    }

def track_openai_request(
    operation_type: OperationType,
    input_prompt: str,
    output_response: str,
    model: str = "gpt-4o",
    user_id: Optional[int] = None,
    session_id: Optional[str] = None,
    additional_metadata: Optional[Dict] = None
) -> Dict[str, Any]:
    """
 функция для отслеживания OpenAI запросов

    Args:
        operation_type: Тип операции
        input_prompt: Входной промпт
        output_response: Выходной ответ
        model: Модель OpenAI
        user_id: ID пользователя
        session_id: ID сессии
        additional_metadata: Дополнительные метаданные

    Returns:
        Результат анализа токенов
    """
   
    token_analysis = cost_tracker.analyze_token_usage(
        input_prompt=input_prompt,
        output_response=output_response,
        model=model
    )

    # Сохраняем метрики
    cost_tracker.track_operation_cost(
        operation_type=operation_type,
        user_id=user_id,
        session_id=session_id,
        token_usage=token_analysis,
        additional_metadata=additional_metadata
    )

    return token_analysis

def get_cost_summary(days: int = 7) -> Dict[str, Any]:
    """
    Получает сводку по затратам за указанный период

    Args:
        days: Количество дней для анализа

    Returns:
        Сводка по затратам
    """
    return cost_tracker.get_operation_stats(days=days)

def export_cost_data_to_csv(
    output_file: str = "cost_tracking_export.csv",
    days: int = 30,
    redis_host: str = "localhost",
    redis_port: int = 6379,
    redis_db: int = 0
) -> str:
    """
    Экспортирует данные cost_tracking в CSV файл с нужными колонками

    Args:
        output_file: Путь к выходному CSV файлу
        days: Количество дней для экспорта данных
        redis_host: Хост Redis сервера
        redis_port: Порт Redis сервера
        redis_db: Номер базы данных Redis

    Returns:
        Путь к созданному CSV файлу
    """
    try:
        redis_client = redis.Redis(
            host=redis_host,
            port=6379,
            db=0,
            decode_responses=True
        )

        pattern = "cost_tracking:*"
        keys = redis_client.keys(pattern)

        export_data = []
        current_date = datetime.now(timezone.utc)
        cutoff_date = current_date - timedelta(days=days)

        for key in keys:
            try:
                data_str = redis_client.get(key)
                if not data_str:
                    continue

                data = json.loads(data_str)

                timestamp_str = data.get('timestamp', '')
                if timestamp_str:
                    timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    if timestamp < cutoff_date:
                        continue

                token_usage = data.get('token_usage', {})
                operation_type = data.get('operation_type', 'unknown')

                # Получаем dag_id из дополнительных метаданных
                additional_metadata = data.get('additional_metadata', {})
                dag_id = additional_metadata.get('dag_id', operation_type)

                # Получаем русское название пайплайна
                pipeline_name = PIPELINE_RUSSIAN_NAMES.get(dag_id, operation_type)

                row = {
                    'Название': pipeline_name,
                    'Tokens (input)': token_usage.get('tokens_input', 0),
                    'Tokens (output)': token_usage.get('tokens_output', 0),
                    'Tokens (Всего)': token_usage.get('total_tokens', 0),
                    'Стоимость (input) USD': token_usage.get('cost_input', 0),
                    'Стоимость (output) USD': token_usage.get('cost_output', 0),
                    'Стоимость (Всего) USD': token_usage.get('total_cost', 0),
                    'Timestamp': timestamp_str
                }

                export_data.append(row)

            except (json.JSONDecodeError, ValueError) as e:
                logger.warning(f"Ошибка обработки ключа {key}: {e}")
                continue

        pipeline_totals = {}
        for row in export_data:
            pipeline = row['Название']
            if pipeline not in pipeline_totals:
                pipeline_totals[pipeline] = {
                    'Название': pipeline,
                    'Tokens (input)': 0,
                    'Tokens (output)': 0,
                    'Tokens (Всего)': 0,
                    'Стоимость (input) USD': 0.0,
                    'Стоимость (output) USD': 0.0,
                    'Стоимость (Всего) USD': 0.0
                }

            pipeline_totals[pipeline]['Tokens (input)'] += row['Tokens (input)']
            pipeline_totals[pipeline]['Tokens (output)'] += row['Tokens (output)']
            pipeline_totals[pipeline]['Tokens (Всего)'] += row['Tokens (Всего)']
            pipeline_totals[pipeline]['Стоимость (input) USD'] += row['Стоимость (input) USD']
            pipeline_totals[pipeline]['Стоимость (output) USD'] += row['Стоимость (output) USD']
            pipeline_totals[pipeline]['Стоимость (Всего) USD'] += row['Стоимость (Всего) USD']

        for pipeline_data in pipeline_totals.values():
            pipeline_data['Стоимость (input) USD'] = round(pipeline_data['Стоимость (input) USD'], 6)
            pipeline_data['Стоимость (output) USD'] = round(pipeline_data['Стоимость (output) USD'], 6)
            pipeline_data['Стоимость (Всего) USD'] = round(pipeline_data['Стоимость (Всего) USD'], 6)

        total_row = {
            'Название': 'ИТОГО ЗА ПОЛЬЗОВАТЕЛЬ:',
            'Tokens (input)': sum(p['Tokens (input)'] for p in pipeline_totals.values()),
            'Tokens (output)': sum(p['Tokens (output)'] for p in pipeline_totals.values()),
            'Tokens (Всего)': sum(p['Tokens (Всего)'] for p in pipeline_totals.values()),
            'Стоимость (input) USD': round(sum(p['Стоимость (input) USD'] for p in pipeline_totals.values()), 6),
            'Стоимость (output) USD': round(sum(p['Стоимость (output) USD'] for p in pipeline_totals.values()), 6),
            'Стоимость (Всего) USD': round(sum(p['Стоимость (Всего) USD'] for p in pipeline_totals.values()), 6)
        }

        # Создаем DataFrame
        final_data = list(pipeline_totals.values()) + [total_row]
        df = pd.DataFrame(final_data)

        # Сохраняем в CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')

        logger.info(f"Данные экспортированы в {output_file}")
        logger.info(f"Экспортировано {len(pipeline_totals)} пайплайнов за {days} дней")

        return output_file

    except Exception as e:
        logger.error(f"Ошибка экспорта данных в CSV: {e}")
        raise

def upload_csv_to_google_sheets(
    csv_file_path: str,
    spreadsheet_id: str = "1u39n_ERZzDQRpq1MWdgkKH9g8eQm15yE-eVX3jligvk",
    sheet_name: str = "BiomCost",
    credentials_file: Optional[str] = "/src/secrets/google_sheet.json"
) -> bool:
    """
    Загружает CSV файл в Google Sheets

    Args:
        csv_file_path: Путь к CSV файлу
        spreadsheet_id: ID Google Sheets документа
        sheet_name: Название листа в документе
        credentials_file: Путь к файлу с учетными данными Google API

    Returns:
        True если успешно загружено, False в противном случае
    """
    try:
        # Импорты для Google Sheets API
        try:
            from google.oauth2.service_account import Credentials
            from googleapiclient.discovery import build
        except ImportError:
            logger.error("Не установлены библиотеки для Google Sheets API. Установите: pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client")
            return False

        # Настройка учетных данных
        if credentials_file:
            credentials = Credentials.from_service_account_file(
                credentials_file,
                scopes=['https://www.googleapis.com/auth/spreadsheets']
            )
        else:
            logger.error("Не указан файл с учетными данными Google API")
            return False

        # Создание сервиса Google Sheets API
        service = build('sheets', 'v4', credentials=credentials)

        # Чтение CSV файла
        df = pd.read_csv(csv_file_path)

        # Подготовка данных для загрузки
        values = [df.columns.tolist()] + df.values.tolist()

        # Очистка существующих данных на листе
        clear_request = {
            'range': f'{sheet_name}!A:Z'
        }
        service.spreadsheets().values().clear(
            spreadsheetId=spreadsheet_id,
            range=clear_request['range']
        ).execute()

        # Загрузка новых данных
        body = {
            'values': values
        }

        result = service.spreadsheets().values().update(
            spreadsheetId=spreadsheet_id,
            range=f'{sheet_name}!A1',
            valueInputOption='RAW',
            body=body
        ).execute()

        logger.info(f"Данные успешно загружены в Google Sheets. Обновлено {result.get('updatedCells', 0)} ячеек")
        return True

    except Exception as e:
        logger.error(f"Ошибка загрузки в Google Sheets: {e}")
        return False

def export_and_upload_cost_data(
    spreadsheet_id: str = "1u39n_ERZzDQRpq1MWdgkKH9g8eQm15yE-eVX3jligvk",
    sheet_name: str = "Cost Tracking",
    days: int = 30,
    credentials_file: Optional[str] = "/src/secrets/google_sheet.json",
    temp_csv_file: str = "temp_cost_export.csv",
    redis_host: str = "localhost",
    redis_port: int = 6379,
    redis_db: int = 0
) -> bool:
    """
    Полный процесс: экспорт данных в CSV и загрузка в Google Sheets

    Args:
        spreadsheet_id: ID Google Sheets документа
        sheet_name: Название листа в документе
        days: Количество дней для экспорта данных
        credentials_file: Путь к файлу с учетными данными Google API
        temp_csv_file: Временный CSV файл
        redis_host: Хост Redis сервера
        redis_port: Порт Redis сервера
        redis_db: Номер базы данных Redis

    Returns:
        True если весь процесс прошел успешно, False в противном случае
    """
    try:
        # Экспорт в CSV
        csv_path = export_cost_data_to_csv(
            output_file=temp_csv_file,
            days=days,
            redis_host=redis_host,
            redis_port=redis_port,
            redis_db=redis_db
        )

        # Загрузка в Google Sheets
        success = upload_csv_to_google_sheets(
            csv_file_path=csv_path,
            spreadsheet_id=spreadsheet_id,
            sheet_name=sheet_name,
            credentials_file=credentials_file
        )

        # Удаление временного файла
        try:
            Path(temp_csv_file).unlink(missing_ok=True)
        except Exception as e:
            logger.warning(f"Не удалось удалить временный файл {temp_csv_file}: {e}")

        return success

    except Exception as e:
        logger.error(f"Ошибка в полном процессе экспорта и загрузки: {e}")
        return False
