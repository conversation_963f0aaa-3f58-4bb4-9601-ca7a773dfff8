[2025-07-18T11:40:56.912+0000] {processor.py:186} INFO - Started process (PID=4368) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:40:56.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:40:56.915+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.915+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:40:57.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.432+0000] {cost_tracking.py:79} ERROR - Ош<PERSON>бка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:57.441+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:40:57.527+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.527+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:cost_tracking_export
[2025-07-18T11:40:57.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.538+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:cost_tracking_export
[2025-07-18T11:40:57.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.547+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:cost_tracking_export
[2025-07-18T11:40:57.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.555+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:cost_tracking_export
[2025-07-18T11:40:57.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.562+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:cost_tracking_export
[2025-07-18T11:40:57.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.568+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:cost_tracking_export
[2025-07-18T11:40:57.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.574+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:cost_tracking_export
[2025-07-18T11:40:57.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.575+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:57.587+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:40:57.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.588+0000] {dag.py:3262} INFO - Creating ORM DAG for cost_tracking_export
[2025-07-18T11:40:57.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.597+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:40:57.620+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.714 seconds
[2025-07-18T11:41:27.713+0000] {processor.py:186} INFO - Started process (PID=4525) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:41:27.714+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:41:27.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.716+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:41:28.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.257+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:28.263+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:41:28.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.286+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:28.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.307+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:41:28.329+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.623 seconds
[2025-07-18T11:41:58.449+0000] {processor.py:186} INFO - Started process (PID=4690) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:41:58.450+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:41:58.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.452+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:41:58.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.960+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:58.966+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:41:58.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.987+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:59.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:59.007+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:41:59.026+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.584 seconds
[2025-07-18T11:42:29.360+0000] {processor.py:186} INFO - Started process (PID=4843) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:42:29.361+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:42:29.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.364+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:42:29.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.847+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:29.853+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:42:29.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.874+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:29.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.895+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:42:29.915+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.561 seconds
[2025-07-18T11:43:00.200+0000] {processor.py:186} INFO - Started process (PID=5002) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:43:00.201+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:43:00.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:43:00.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.713+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:00.719+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:43:00.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.738+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:00.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.756+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:43:00.776+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.583 seconds
[2025-07-18T11:43:30.956+0000] {processor.py:186} INFO - Started process (PID=5161) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:43:30.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:43:30.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:43:31.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.450+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:31.456+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:43:31.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.479+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:31.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.499+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:43:31.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.571 seconds
[2025-07-18T11:44:01.657+0000] {processor.py:186} INFO - Started process (PID=5308) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:44:01.658+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:44:01.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.660+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:44:02.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:02.145+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:02.151+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:44:02.172+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:02.172+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:02.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:02.191+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:44:02.209+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.559 seconds
[2025-07-18T11:44:32.734+0000] {processor.py:186} INFO - Started process (PID=5467) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:44:32.735+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:44:32.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.737+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:44:33.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:33.324+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:33.330+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:44:33.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:33.350+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:33.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:33.371+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:44:33.390+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.663 seconds
[2025-07-18T11:45:03.646+0000] {processor.py:186} INFO - Started process (PID=5626) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:45:03.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:45:03.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:45:04.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:04.160+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:04.170+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:45:04.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:04.196+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:04.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:04.220+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:45:04.241+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.602 seconds
[2025-07-18T11:45:35.034+0000] {processor.py:186} INFO - Started process (PID=5785) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:45:35.035+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:45:35.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.038+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:45:35.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.602+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:35.608+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:45:35.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.632+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:35.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.658+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:45:35.685+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.657 seconds
[2025-07-18T11:46:05.977+0000] {processor.py:186} INFO - Started process (PID=5944) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:46:05.978+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:46:05.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:46:06.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.457+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:06.462+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:46:06.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:06.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.503+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:46:06.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.552 seconds
[2025-07-18T11:46:36.786+0000] {processor.py:186} INFO - Started process (PID=6103) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:46:36.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:46:36.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.790+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:46:37.270+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.270+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:37.276+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:46:37.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.296+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:37.318+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.318+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:46:37.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.560 seconds
[2025-07-18T11:47:08.395+0000] {processor.py:186} INFO - Started process (PID=6262) to work on /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:47:08.396+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/cost_tracking_export_dag.py for tasks to queue
[2025-07-18T11:47:08.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:08.398+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:47:08.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:08.912+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:08.918+0000] {processor.py:925} INFO - DAG(s) 'cost_tracking_export' retrieved from /opt/airflow/dags/cost_tracking_export_dag.py
[2025-07-18T11:47:08.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:08.939+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:08.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:08.963+0000] {dag.py:4180} INFO - Setting next_dagrun for cost_tracking_export to 2025-07-17 09:00:00+00:00, run_after=2025-07-18 09:00:00+00:00
[2025-07-18T11:47:08.990+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/cost_tracking_export_dag.py took 0.601 seconds
