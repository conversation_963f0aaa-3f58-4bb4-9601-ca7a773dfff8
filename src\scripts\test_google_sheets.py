#!/usr/bin/env python3
"""
Тестовый скрипт для проверки подключения к Google Sheets
"""

import sys
import os
from pathlib import Path

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent.parent / "dags"))

def test_google_sheets_connection():
    """Тестирует подключение к Google Sheets"""
    print("🔗 Тестирование подключения к Google Sheets...")
    
    try:
        from google.oauth2.service_account import Credentials
        from googleapiclient.discovery import build
        print("✅ Библиотеки Google API импортированы успешно")
    except ImportError as e:
        print(f"❌ Ошибка импорта библиотек Google API: {e}")
        print("💡 Установите зависимости: pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client")
        return False
    
    # Проверяем файл учетных данных
    credentials_file = "/src/secrets/google_sheet.json"
    if not Path(credentials_file).exists():
        print(f"❌ Файл учетных данных не найден: {credentials_file}")
        return False
    
    print(f"✅ Файл учетных данных найден: {credentials_file}")
    
    try:
        # Загружаем учетные данные
        credentials = Credentials.from_service_account_file(
            credentials_file,
            scopes=['https://www.googleapis.com/auth/spreadsheets']
        )
        print("✅ Учетные данные загружены успешно")
        
        # Создаем сервис
        service = build('sheets', 'v4', credentials=credentials)
        print("✅ Google Sheets API сервис создан")
        
        # Тестируем доступ к таблице
        spreadsheet_id = "1u39n_ERZzDQRpq1MWdgkKH9g8eQm15yE-eVX3jligvk"
        sheet_name = "BiomCost"
        
        # Пробуем получить информацию о таблице
        spreadsheet = service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        print(f"✅ Доступ к таблице получен: {spreadsheet.get('properties', {}).get('title', 'Unknown')}")
        
        # Проверяем существование листа
        sheets = spreadsheet.get('sheets', [])
        sheet_exists = any(sheet['properties']['title'] == sheet_name for sheet in sheets)
        
        if sheet_exists:
            print(f"✅ Лист '{sheet_name}' найден")
        else:
            print(f"⚠️ Лист '{sheet_name}' не найден. Доступные листы:")
            for sheet in sheets:
                print(f"   - {sheet['properties']['title']}")
        
        # Тестируем запись данных
        test_data = [
            ["Тест", "Дата", "Статус"],
            ["Подключение к Google Sheets", "2024-01-01", "✅ Успешно"]
        ]
        
        body = {'values': test_data}
        
        result = service.spreadsheets().values().update(
            spreadsheetId=spreadsheet_id,
            range=f'{sheet_name}!A1:C2',
            valueInputOption='RAW',
            body=body
        ).execute()
        
        print(f"✅ Тестовые данные записаны. Обновлено ячеек: {result.get('updatedCells', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка при работе с Google Sheets: {e}")
        return False

def test_cost_tracking_export():
    """Тестирует экспорт данных cost_tracking"""
    print("\n📊 Тестирование экспорта cost_tracking...")
    
    try:
        from cost_tracking import export_and_upload_cost_data
        
        print("✅ Модуль cost_tracking импортирован")
        
        # Тестируем экспорт с настроенными параметрами
        success = export_and_upload_cost_data(
            days=7  # Экспортируем данные за последние 7 дней
        )
        
        if success:
            print("✅ Экспорт и загрузка в Google Sheets выполнены успешно")
            print("🎯 Проверьте таблицу: https://docs.google.com/spreadsheets/d/1u39n_ERZzDQRpq1MWdgkKH9g8eQm15yE-eVX3jligvk/edit")
            return True
        else:
            print("❌ Ошибка при экспорте данных")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка при тестировании экспорта: {e}")
        return False

def main():
    """Основная функция тестирования"""
    print("🚀 Тестирование интеграции с Google Sheets\n")
    
    tests = [
        ("Подключение к Google Sheets", test_google_sheets_connection),
        ("Экспорт данных cost_tracking", test_cost_tracking_export)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"{'='*60}")
        print(f"🧪 {test_name}")
        print('='*60)
        
        try:
            if test_func():
                passed += 1
                print(f"\n✅ {test_name} - ПРОЙДЕН")
            else:
                failed += 1
                print(f"\n❌ {test_name} - ПРОВАЛЕН")
        except Exception as e:
            failed += 1
            print(f"\n❌ {test_name} - ОШИБКА: {e}")
    
    print(f"\n{'='*60}")
    print(f"📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ")
    print('='*60)
    print(f"✅ Пройдено: {passed}")
    print(f"❌ Провалено: {failed}")
    
    if failed == 0:
        print("\n🎉 Все тесты пройдены! Google Sheets интеграция работает корректно.")
        print("🔗 Ваша таблица: https://docs.google.com/spreadsheets/d/1u39n_ERZzDQRpq1MWdgkKH9g8eQm15yE-eVX3jligvk/edit")
    else:
        print(f"\n⚠️ Обнаружены проблемы в {failed} тестах")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
