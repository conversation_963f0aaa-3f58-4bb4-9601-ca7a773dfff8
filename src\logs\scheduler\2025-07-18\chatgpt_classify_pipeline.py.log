[2025-07-18T11:25:57.836+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:25:57.837+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:25:57.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:25:57.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.934+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:57.941+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:25:58.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.035+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_classify_pipeline
[2025-07-18T11:25:58.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.179+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_classify_pipeline
[2025-07-18T11:25:58.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.188+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_classify_pipeline
[2025-07-18T11:25:58.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.198+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_classify_pipeline
[2025-07-18T11:25:58.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.205+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_classify_pipeline
[2025-07-18T11:25:58.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.212+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_classify_pipeline
[2025-07-18T11:25:58.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.219+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_classify_pipeline
[2025-07-18T11:25:58.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:58.234+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:58.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.235+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_classify_pipeline
[2025-07-18T11:25:58.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.236+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:25:58.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.425 seconds
[2025-07-18T11:26:28.932+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:26:28.933+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:26:28.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.935+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:26:29.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.020+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:29.030+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:26:29.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.323+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:29.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.335+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:26:29.355+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.430 seconds
[2025-07-18T11:27:00.173+0000] {processor.py:186} INFO - Started process (PID=495) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:00.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:27:00.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:00.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.412+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:00.419+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:00.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:27:00.558+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.391 seconds
[2025-07-18T11:27:31.182+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:31.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:27:31.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:31.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.253+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:31.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.361+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.371+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:27:31.391+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.216 seconds
[2025-07-18T11:28:01.729+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:01.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:28:01.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:01.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.804+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.813+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:01.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.931+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.946+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:28:01.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.242 seconds
[2025-07-18T11:28:32.293+0000] {processor.py:186} INFO - Started process (PID=888) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:32.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:28:32.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:32.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.364+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:32.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.474+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:32.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.487+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:28:32.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.224 seconds
[2025-07-18T11:29:02.607+0000] {processor.py:186} INFO - Started process (PID=1019) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:02.608+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:29:02.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.611+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:02.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.680+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.688+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:02.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.795+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:29:02.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.216 seconds
[2025-07-18T11:29:33.350+0000] {processor.py:186} INFO - Started process (PID=1150) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:33.351+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:29:33.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.353+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:33.435+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.435+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.444+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:33.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.558+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.571+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:29:33.591+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.248 seconds
[2025-07-18T11:30:04.440+0000] {processor.py:186} INFO - Started process (PID=1281) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:04.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:30:04.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.443+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:04.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.512+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.521+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:04.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.623+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.636+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:30:04.669+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.235 seconds
[2025-07-18T11:30:35.646+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:35.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:30:35.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.649+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:35.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.724+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:35.735+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:35.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.854+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:35.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.867+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:30:35.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.248 seconds
[2025-07-18T11:31:06.050+0000] {processor.py:186} INFO - Started process (PID=1541) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:06.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:31:06.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.054+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:06.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.129+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.136+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:06.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.244+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:31:06.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.236 seconds
[2025-07-18T11:31:36.658+0000] {processor.py:186} INFO - Started process (PID=1674) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:36.659+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:31:36.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.662+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:36.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.752+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:36.761+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:36.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.871+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:36.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.882+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:31:36.905+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.255 seconds
[2025-07-18T11:32:07.441+0000] {processor.py:186} INFO - Started process (PID=1805) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:32:07.442+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:32:07.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:32:07.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.522+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:07.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:32:07.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.639+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:07.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.651+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:32:07.674+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.241 seconds
[2025-07-18T11:32:37.878+0000] {processor.py:186} INFO - Started process (PID=1936) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:32:37.878+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:32:37.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.881+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:32:37.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.951+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:37.962+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:32:38.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.073+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:38.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.085+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:32:38.106+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.234 seconds
[2025-07-18T11:33:08.395+0000] {processor.py:186} INFO - Started process (PID=2067) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:33:08.396+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:33:08.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.398+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:33:08.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.473+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:08.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:33:08.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:08.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.611+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:33:08.634+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.244 seconds
[2025-07-18T11:33:39.016+0000] {processor.py:186} INFO - Started process (PID=2205) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:33:39.017+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:33:39.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:33:39.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.390+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:39.400+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:33:39.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.503+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:39.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.516+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:33:39.540+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.530 seconds
[2025-07-18T11:34:10.235+0000] {processor.py:186} INFO - Started process (PID=2358) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:34:10.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:34:10.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.240+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:34:10.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.631+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:10.641+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:34:10.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.742+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:10.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.883+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:34:10.904+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.677 seconds
[2025-07-18T11:34:41.637+0000] {processor.py:186} INFO - Started process (PID=2513) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:34:41.639+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:34:41.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:41.641+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:34:42.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.011+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:42.020+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:34:42.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.129+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:42.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.276+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:34:42.297+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.667 seconds
[2025-07-18T11:35:12.971+0000] {processor.py:186} INFO - Started process (PID=2666) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:35:12.972+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:35:12.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.975+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:35:13.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.399+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:13.408+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:35:13.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:13.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.681+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:35:13.699+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.735 seconds
[2025-07-18T11:35:44.024+0000] {processor.py:186} INFO - Started process (PID=2819) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:35:44.025+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:35:44.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:44.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:35:44.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:44.442+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:44.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:35:44.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:44.727+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:44.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:44.740+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:35:44.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.745 seconds
[2025-07-18T11:36:15.054+0000] {processor.py:186} INFO - Started process (PID=2972) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:36:15.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:36:15.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:15.059+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:36:15.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:15.428+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:15.437+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:36:15.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:15.684+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:15.696+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:15.696+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:36:15.718+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.670 seconds
[2025-07-18T11:36:45.838+0000] {processor.py:186} INFO - Started process (PID=3123) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:36:45.839+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:36:45.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.842+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:36:46.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:46.226+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:46.235+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:36:46.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:46.471+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:46.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:46.483+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:36:46.504+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.673 seconds
[2025-07-18T11:37:16.824+0000] {processor.py:186} INFO - Started process (PID=3277) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:37:16.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:37:16.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.829+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:37:17.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:17.227+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:17.235+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:37:17.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:17.504+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:17.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:17.513+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:37:17.532+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.722 seconds
[2025-07-18T11:37:49.439+0000] {processor.py:186} INFO - Started process (PID=3430) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:37:49.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:37:49.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.442+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:37:49.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.815+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:49.823+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:37:50.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.074+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:50.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.086+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:37:50.105+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.674 seconds
[2025-07-18T11:38:20.349+0000] {processor.py:186} INFO - Started process (PID=3583) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:38:20.350+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:38:20.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.353+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:38:20.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.926+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:20.934+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:38:21.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.025+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:21.035+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.035+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:38:21.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.717 seconds
[2025-07-18T11:38:51.434+0000] {processor.py:186} INFO - Started process (PID=3736) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:38:51.434+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:38:51.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:38:51.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.953+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:51.961+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:38:52.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:52.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.069+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:38:52.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.661 seconds
[2025-07-18T11:39:22.313+0000] {processor.py:186} INFO - Started process (PID=3889) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:39:22.315+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:39:22.318+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.317+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:39:22.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.857+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:22.863+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:39:22.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.957+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:22.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.968+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:39:22.988+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.683 seconds
[2025-07-18T11:39:53.095+0000] {processor.py:186} INFO - Started process (PID=4042) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:39:53.096+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:39:53.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.099+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:39:53.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.606+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:53.615+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:39:53.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.715+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:53.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.725+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:39:53.746+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.658 seconds
[2025-07-18T11:40:24.096+0000] {processor.py:186} INFO - Started process (PID=4195) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:40:24.097+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:40:24.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.099+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:40:24.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.642+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:24.650+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:40:24.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.771+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:24.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.782+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:40:24.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.715 seconds
[2025-07-18T11:40:55.419+0000] {processor.py:186} INFO - Started process (PID=4348) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:40:55.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:40:55.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:40:55.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.909+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:55.916+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:40:56.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.006+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:56.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.017+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:40:56.036+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.623 seconds
[2025-07-18T11:41:26.609+0000] {processor.py:186} INFO - Started process (PID=4507) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:41:26.610+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:41:26.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:41:27.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.126+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:27.133+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:41:27.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.238+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:27.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:41:27.269+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.667 seconds
[2025-07-18T11:41:57.414+0000] {processor.py:186} INFO - Started process (PID=4666) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:41:57.415+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:41:57.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.418+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:41:57.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.921+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:57.927+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:41:58.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.027+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:58.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.038+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:41:58.057+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.651 seconds
[2025-07-18T11:42:28.684+0000] {processor.py:186} INFO - Started process (PID=4831) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:42:28.685+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:42:28.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.687+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:42:29.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.173+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:29.180+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:42:29.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.270+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:29.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.281+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:42:29.299+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.621 seconds
[2025-07-18T11:42:59.499+0000] {processor.py:186} INFO - Started process (PID=4990) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:42:59.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:42:59.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.503+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:42:59.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.994+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:00.003+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:43:00.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.105+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:00.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:43:00.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.642 seconds
[2025-07-18T11:43:30.250+0000] {processor.py:186} INFO - Started process (PID=5149) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:43:30.251+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:43:30.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.254+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:43:30.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.762+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:30.770+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:43:30.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.866+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:30.875+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.874+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:43:30.894+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.651 seconds
[2025-07-18T11:44:01.938+0000] {processor.py:186} INFO - Started process (PID=5314) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:44:01.939+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:44:01.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.943+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:44:02.425+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:02.425+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:02.432+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:44:02.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:02.523+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:02.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:02.534+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:44:02.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.623 seconds
[2025-07-18T11:44:32.890+0000] {processor.py:186} INFO - Started process (PID=5472) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:44:32.892+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:44:32.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.894+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:44:33.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:33.436+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:33.443+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:44:33.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:33.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:33.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:33.552+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:44:33.574+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.692 seconds
[2025-07-18T11:45:03.901+0000] {processor.py:186} INFO - Started process (PID=5632) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:45:03.903+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:45:03.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.905+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:45:04.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:04.475+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:04.484+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:45:04.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:04.577+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:04.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:04.587+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:45:04.608+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.712 seconds
[2025-07-18T11:45:35.084+0000] {processor.py:186} INFO - Started process (PID=5790) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:45:35.085+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:45:35.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:45:35.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.684+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:35.693+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:45:35.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.805+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:35.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.813+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:45:35.832+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.756 seconds
[2025-07-18T11:46:06.242+0000] {processor.py:186} INFO - Started process (PID=5950) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:46:06.243+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:46:06.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.245+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:46:06.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.723+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:06.730+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:46:06.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.825+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:06.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.834+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:46:06.855+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.620 seconds
[2025-07-18T11:46:37.102+0000] {processor.py:186} INFO - Started process (PID=6109) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:46:37.103+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:46:37.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:46:37.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.595+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:37.601+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:46:37.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.700+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:37.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.713+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:46:37.733+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.637 seconds
[2025-07-18T11:47:08.449+0000] {processor.py:186} INFO - Started process (PID=6267) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:47:08.450+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:47:08.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:08.452+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:47:09.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:09.010+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:09.018+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:47:09.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:09.119+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:09.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:09.131+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:47:09.152+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.710 seconds
