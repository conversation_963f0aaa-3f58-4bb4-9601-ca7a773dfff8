[2025-07-18T11:25:57.836+0000] {processor.py:186} INFO - Started process (PID=231) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:25:57.837+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:25:57.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:25:57.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.934+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:57.941+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:25:58.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.035+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_classify_pipeline
[2025-07-18T11:25:58.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.179+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_classify_pipeline
[2025-07-18T11:25:58.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.188+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_classify_pipeline
[2025-07-18T11:25:58.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.198+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_classify_pipeline
[2025-07-18T11:25:58.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.205+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_classify_pipeline
[2025-07-18T11:25:58.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.212+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_classify_pipeline
[2025-07-18T11:25:58.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.219+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_classify_pipeline
[2025-07-18T11:25:58.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:58.234+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:58.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.235+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_classify_pipeline
[2025-07-18T11:25:58.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.236+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:25:58.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.425 seconds
[2025-07-18T11:26:28.932+0000] {processor.py:186} INFO - Started process (PID=362) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:26:28.933+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:26:28.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.935+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:26:29.021+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.020+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:29.030+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:26:29.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.323+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:29.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.335+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:26:29.355+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.430 seconds
[2025-07-18T11:27:00.173+0000] {processor.py:186} INFO - Started process (PID=495) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:00.174+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:27:00.176+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.176+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:00.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.412+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:00.419+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:00.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:27:00.558+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.391 seconds
[2025-07-18T11:27:31.182+0000] {processor.py:186} INFO - Started process (PID=626) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:31.183+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:27:31.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.184+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:31.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.253+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:27:31.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.361+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.371+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:27:31.391+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.216 seconds
[2025-07-18T11:28:01.729+0000] {processor.py:186} INFO - Started process (PID=757) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:01.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:28:01.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:01.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.804+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.813+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:01.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.931+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.946+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:28:01.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.242 seconds
[2025-07-18T11:28:32.293+0000] {processor.py:186} INFO - Started process (PID=888) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:32.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:28:32.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.295+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:32.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.364+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.376+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:28:32.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.474+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:32.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.487+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:28:32.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.224 seconds
[2025-07-18T11:29:02.607+0000] {processor.py:186} INFO - Started process (PID=1019) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:02.608+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:29:02.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.611+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:02.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.680+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.688+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:02.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.795+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:29:02.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.216 seconds
[2025-07-18T11:29:33.350+0000] {processor.py:186} INFO - Started process (PID=1150) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:33.351+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:29:33.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.353+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:33.435+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.435+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.444+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:29:33.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.558+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.571+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:29:33.591+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.248 seconds
[2025-07-18T11:30:04.440+0000] {processor.py:186} INFO - Started process (PID=1281) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:04.440+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:30:04.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.443+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:04.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.512+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.521+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:04.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.623+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.636+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:30:04.669+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.235 seconds
[2025-07-18T11:30:35.646+0000] {processor.py:186} INFO - Started process (PID=1412) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:35.647+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:30:35.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.649+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:35.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.724+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:35.735+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:30:35.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.854+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:35.868+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.867+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:30:35.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.248 seconds
[2025-07-18T11:31:06.050+0000] {processor.py:186} INFO - Started process (PID=1541) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:06.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:31:06.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.054+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:06.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.129+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.136+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:06.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.244+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:31:06.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.236 seconds
[2025-07-18T11:31:36.658+0000] {processor.py:186} INFO - Started process (PID=1674) to work on /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:36.659+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_classify_pipeline.py for tasks to queue
[2025-07-18T11:31:36.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.662+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:36.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.752+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:36.761+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_classify_pipeline.py
[2025-07-18T11:31:36.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.871+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:36.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.882+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_classify_pipeline to None, run_after=None
[2025-07-18T11:31:36.905+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_classify_pipeline.py took 0.255 seconds
