[2025-07-18T11:25:56.953+0000] {processor.py:186} INFO - Started process (PID=216) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:25:56.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:25:56.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.957+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:25:57.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.047+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:57.054+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:25:57.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.153+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.164+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.329+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.340+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.348+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.356+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.363+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.364+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:57.375+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:57.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.375+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.376+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:25:57.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.448 seconds
[2025-07-18T11:26:28.360+0000] {processor.py:186} INFO - Started process (PID=347) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:28.361+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:26:28.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.363+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:28.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.445+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:28.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.724+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:28.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.733+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:26:28.754+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.400 seconds
[2025-07-18T11:26:59.298+0000] {processor.py:186} INFO - Started process (PID=480) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:59.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:26:59.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:59.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.534+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.541+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:59.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:59.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.647+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:26:59.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.373 seconds
[2025-07-18T11:27:30.644+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:27:30.646+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:27:30.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:27:30.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.745+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:30.754+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:27:30.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:30.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.872+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:27:30.893+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.257 seconds
[2025-07-18T11:28:01.226+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:01.227+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:28:01.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.229+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:01.298+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.298+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.307+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:01.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.402+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.413+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:28:01.434+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.213 seconds
[2025-07-18T11:28:31.732+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:31.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:28:31.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:31.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.830+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:31.839+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:31.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:31.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.972+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:28:31.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.266 seconds
[2025-07-18T11:29:02.073+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:02.074+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:29:02.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:02.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.156+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.167+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:02.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.275+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.290+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:29:02.312+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.244 seconds
[2025-07-18T11:29:32.771+0000] {processor.py:186} INFO - Started process (PID=1135) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:32.772+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:29:32.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.774+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:32.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.853+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:32.863+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:32.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.978+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:32.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.991+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:29:33.013+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.249 seconds
[2025-07-18T11:30:03.920+0000] {processor.py:186} INFO - Started process (PID=1266) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:03.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:30:03.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:03.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:03.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:03.995+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.002+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:04.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.101+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.114+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:30:04.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.221 seconds
[2025-07-18T11:30:34.354+0000] {processor.py:186} INFO - Started process (PID=1397) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:34.356+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:30:34.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.358+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:34.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.430+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:34.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:34.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:34.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:30:34.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.228 seconds
[2025-07-18T11:31:05.003+0000] {processor.py:186} INFO - Started process (PID=1528) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:05.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:31:05.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:05.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.103+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:05.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:05.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.213+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:05.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:31:05.241+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.244 seconds
[2025-07-18T11:31:36.102+0000] {processor.py:186} INFO - Started process (PID=1659) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:36.104+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:31:36.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:36.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.185+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:36.194+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:36.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:36.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.312+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:31:36.334+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.239 seconds
[2025-07-18T11:32:06.885+0000] {processor.py:186} INFO - Started process (PID=1790) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:32:06.886+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:32:06.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:06.889+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:32:06.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:06.962+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:06.972+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:32:07.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.076+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:07.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.088+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:32:07.112+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.233 seconds
[2025-07-18T11:32:37.334+0000] {processor.py:186} INFO - Started process (PID=1921) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:32:37.335+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:32:37.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.337+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:32:37.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.408+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:37.417+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:32:37.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.513+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:37.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.524+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:32:37.545+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.218 seconds
[2025-07-18T11:33:07.854+0000] {processor.py:186} INFO - Started process (PID=2057) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:33:07.855+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:33:07.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:07.857+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:33:07.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:07.933+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:07.941+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:33:08.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.035+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:08.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.045+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:33:08.065+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.217 seconds
[2025-07-18T11:33:38.434+0000] {processor.py:186} INFO - Started process (PID=2193) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:33:38.435+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:33:38.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:33:38.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.795+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:38.804+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:33:38.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.913+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:38.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.927+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:33:38.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.527 seconds
[2025-07-18T11:34:09.414+0000] {processor.py:186} INFO - Started process (PID=2346) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:34:09.415+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:34:09.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.418+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:34:09.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.829+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:09.838+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:34:09.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.962+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:10.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:34:10.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.732 seconds
[2025-07-18T11:34:40.870+0000] {processor.py:186} INFO - Started process (PID=2499) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:34:40.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:34:40.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.873+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:34:41.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:41.252+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:41.263+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:34:41.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:41.371+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:41.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:41.555+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:34:41.575+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.713 seconds
[2025-07-18T11:35:12.108+0000] {processor.py:186} INFO - Started process (PID=2654) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:35:12.108+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:35:12.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.111+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:35:12.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.556+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:12.565+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:35:12.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.884+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:12.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.894+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:35:12.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.811 seconds
[2025-07-18T11:35:43.227+0000] {processor.py:186} INFO - Started process (PID=2807) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:35:43.228+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:35:43.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:43.231+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:35:43.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:43.642+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:43.650+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:35:43.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:43.940+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:43.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:43.954+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:35:43.978+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.757 seconds
[2025-07-18T11:36:14.290+0000] {processor.py:186} INFO - Started process (PID=2960) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:36:14.291+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:36:14.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:36:14.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.673+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:14.684+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:36:14.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.961+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:14.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.971+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:36:14.993+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.710 seconds
[2025-07-18T11:36:45.069+0000] {processor.py:186} INFO - Started process (PID=3111) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:36:45.070+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:36:45.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.073+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:36:45.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.463+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:45.474+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:36:45.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.740+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:45.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.750+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:36:45.772+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.710 seconds
[2025-07-18T11:37:16.044+0000] {processor.py:186} INFO - Started process (PID=3265) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:37:16.045+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:37:16.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.048+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:37:16.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.454+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:16.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:37:16.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.720+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:16.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.730+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:37:16.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.717 seconds
[2025-07-18T11:37:48.678+0000] {processor.py:186} INFO - Started process (PID=3418) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:37:48.680+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:37:48.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:37:49.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.108+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:49.115+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:37:49.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.361+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:49.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.373+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:37:49.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.722 seconds
[2025-07-18T11:38:19.629+0000] {processor.py:186} INFO - Started process (PID=3571) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:38:19.630+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:38:19.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.632+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:38:20.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.134+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:20.140+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:38:20.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.250+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:20.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.260+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:38:20.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.657 seconds
[2025-07-18T11:38:50.718+0000] {processor.py:186} INFO - Started process (PID=3724) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:38:50.720+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:38:50.723+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.723+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:38:51.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.241+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:51.251+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:38:51.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.346+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:51.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.356+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:38:51.373+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.661 seconds
[2025-07-18T11:39:21.593+0000] {processor.py:186} INFO - Started process (PID=3877) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:39:21.594+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:39:21.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:39:22.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.106+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:22.114+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:39:22.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.216+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:22.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.225+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:39:22.247+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.661 seconds
[2025-07-18T11:39:52.387+0000] {processor.py:186} INFO - Started process (PID=4030) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:39:52.388+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:39:52.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.391+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:39:52.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.900+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:52.907+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:39:53.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.003+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:53.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.013+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:39:53.034+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.653 seconds
[2025-07-18T11:40:23.351+0000] {processor.py:186} INFO - Started process (PID=4183) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:40:23.352+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:40:23.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.355+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:40:23.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.897+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:23.904+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:40:23.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.998+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:24.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.009+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:40:24.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.686 seconds
[2025-07-18T11:40:54.488+0000] {processor.py:186} INFO - Started process (PID=4330) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:40:54.489+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:40:54.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.491+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:40:54.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.955+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:54.962+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:40:55.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:55.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:40:55.083+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.600 seconds
[2025-07-18T11:41:25.625+0000] {processor.py:186} INFO - Started process (PID=4489) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:41:25.627+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:41:25.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.630+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:41:26.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.111+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:26.118+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:41:26.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.213+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:26.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.224+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:41:26.244+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.629 seconds
[2025-07-18T11:41:56.449+0000] {processor.py:186} INFO - Started process (PID=4648) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:41:56.450+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:41:56.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:56.452+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:41:56.926+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:56.926+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:56.935+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:41:57.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.027+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:57.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.037+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:41:57.056+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.614 seconds
[2025-07-18T11:42:27.607+0000] {processor.py:186} INFO - Started process (PID=4807) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:42:27.608+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:42:27.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.610+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:42:28.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.112+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:28.121+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:42:28.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.219+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:28.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.228+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:42:28.248+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.648 seconds
[2025-07-18T11:42:58.416+0000] {processor.py:186} INFO - Started process (PID=4972) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:42:58.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:42:58.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.420+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:42:58.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.903+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:58.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:42:58.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.994+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:59.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.003+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:42:59.020+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.610 seconds
[2025-07-18T11:43:29.214+0000] {processor.py:186} INFO - Started process (PID=5131) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:43:29.215+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:43:29.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.218+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:43:29.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.683+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:29.690+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:43:29.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.777+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:29.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.785+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:43:29.803+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.596 seconds
[2025-07-18T11:44:00.549+0000] {processor.py:186} INFO - Started process (PID=5290) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:44:00.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:44:00.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:00.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:44:01.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.079+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:01.086+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:44:01.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.177+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:01.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:44:01.203+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.660 seconds
[2025-07-18T11:44:31.489+0000] {processor.py:186} INFO - Started process (PID=5449) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:44:31.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:44:31.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.493+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:44:31.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.973+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:31.979+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:44:32.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.070+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:32.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.081+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:44:32.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.618 seconds
[2025-07-18T11:45:02.347+0000] {processor.py:186} INFO - Started process (PID=5608) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:45:02.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:45:02.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:02.352+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:45:02.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:02.927+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:02.933+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:45:03.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:03.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.049+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:45:03.070+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.729 seconds
[2025-07-18T11:45:33.548+0000] {processor.py:186} INFO - Started process (PID=5766) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:45:33.549+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:45:33.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:33.551+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:45:34.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.118+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:34.126+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:45:34.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.227+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:34.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.235+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:45:34.255+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.713 seconds
[2025-07-18T11:46:04.843+0000] {processor.py:186} INFO - Started process (PID=5926) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:46:04.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:46:04.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:04.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:46:05.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.381+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:05.388+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:46:05.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.480+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:05.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.489+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:46:05.511+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.673 seconds
[2025-07-18T11:46:35.673+0000] {processor.py:186} INFO - Started process (PID=6085) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:46:35.674+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:46:35.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:35.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:46:36.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.196+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:36.206+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:46:36.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.309+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:36.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.321+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:46:36.341+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.675 seconds
[2025-07-18T11:47:07.144+0000] {processor.py:186} INFO - Started process (PID=6246) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:47:07.145+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:47:07.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.147+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:47:07.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.613+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:07.620+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:47:07.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.717+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:07.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.731+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:47:07.751+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.614 seconds
