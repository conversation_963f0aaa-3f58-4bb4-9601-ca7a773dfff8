[2025-07-18T11:25:56.953+0000] {processor.py:186} INFO - Started process (PID=216) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:25:56.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:25:56.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.957+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:25:57.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.047+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:57.054+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:25:57.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.153+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.164+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.329+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.340+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.348+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.356+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.363+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.364+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:57.375+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:57.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.375+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_chat_open_pipeline
[2025-07-18T11:25:57.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.376+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:25:57.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.448 seconds
[2025-07-18T11:26:28.360+0000] {processor.py:186} INFO - Started process (PID=347) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:28.361+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:26:28.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.363+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:28.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.445+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:28.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.724+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:28.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.733+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:26:28.754+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.400 seconds
[2025-07-18T11:26:59.298+0000] {processor.py:186} INFO - Started process (PID=480) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:59.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:26:59.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:59.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.534+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.541+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:26:59.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:59.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.647+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:26:59.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.373 seconds
[2025-07-18T11:27:30.644+0000] {processor.py:186} INFO - Started process (PID=611) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:27:30.646+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:27:30.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:27:30.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.745+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:30.754+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:27:30.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.860+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:30.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.872+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:27:30.893+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.257 seconds
[2025-07-18T11:28:01.226+0000] {processor.py:186} INFO - Started process (PID=742) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:01.227+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:28:01.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.229+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:01.298+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.298+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.307+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:01.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.402+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.413+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:28:01.434+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.213 seconds
[2025-07-18T11:28:31.732+0000] {processor.py:186} INFO - Started process (PID=873) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:31.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:28:31.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:31.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.830+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:31.839+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:28:31.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:31.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.972+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:28:31.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.266 seconds
[2025-07-18T11:29:02.073+0000] {processor.py:186} INFO - Started process (PID=1004) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:02.074+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:29:02.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:02.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.156+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.167+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:02.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.275+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.290+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:29:02.312+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.244 seconds
[2025-07-18T11:29:32.771+0000] {processor.py:186} INFO - Started process (PID=1135) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:32.772+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:29:32.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.774+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:32.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.853+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:32.863+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:29:32.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.978+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:32.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.991+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:29:33.013+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.249 seconds
[2025-07-18T11:30:03.920+0000] {processor.py:186} INFO - Started process (PID=1266) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:03.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:30:03.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:03.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:03.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:03.995+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.002+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:04.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.101+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.114+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:30:04.135+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.221 seconds
[2025-07-18T11:30:34.354+0000] {processor.py:186} INFO - Started process (PID=1397) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:34.356+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:30:34.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.358+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:34.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.430+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:34.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:30:34.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:34.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.557+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:30:34.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.228 seconds
[2025-07-18T11:31:05.003+0000] {processor.py:186} INFO - Started process (PID=1528) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:05.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:31:05.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:05.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.103+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:05.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:05.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.213+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:05.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:31:05.241+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.244 seconds
[2025-07-18T11:31:36.102+0000] {processor.py:186} INFO - Started process (PID=1659) to work on /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:36.104+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_pipeline.py for tasks to queue
[2025-07-18T11:31:36.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:36.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.185+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:36.194+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_pipeline.py
[2025-07-18T11:31:36.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:36.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.312+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_pipeline to None, run_after=None
[2025-07-18T11:31:36.334+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_pipeline.py took 0.239 seconds
