from run_chatgpt_pipeline import run_dag_and_get_xcom

# Для message pipeline
async def run_chatgpt_message_pipeline(conf):
    DAG_ID = "chatgpt_message_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для chat_open_pipeline
async def run_chatgpt_chat_open_pipeline(conf):
    DAG_ID = "chatgpt_chat_open_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_generate_challenges_pipeline(conf):
    DAG_ID = "chatgpt_generate_challenges_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_recount_calories_pipeline(conf):
    DAG_ID = "chatgpt_recount_calories_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_clarify_pipeline(conf):
    DAG_ID = "chatgpt_clarify_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для chat_open_rec_risk_pipeline
async def run_chatgpt_chat_open_rec_risk_pipeline(conf):
    DAG_ID = "chatgpt_chat_open_rec_risk_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для goals pipeline
async def run_chatgpt_goals_pipeline(conf):
    DAG_ID = "chatgpt_goals_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_describe_challenge_pipeline(conf):
    DAG_ID = "chatgpt_describe_challenge_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для message_recommendation_pipeline
async def run_chatgpt_message_recommendation_pipeline(conf):
    DAG_ID = "chatgpt_message_recommendation_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для message_risk_pipeline
async def run_chatgpt_message_risk_pipeline(conf):
    DAG_ID = "chatgpt_message_risk_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для metrics pipeline
async def run_chatgpt_metrics_pipeline(conf):
    DAG_ID = "chatgpt_metrics_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для weights pipeline
async def run_chatgpt_weights_pipeline(conf):
    DAG_ID = "chatgpt_weights_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для perplexity pipeline
async def run_perplexity_pipeline(conf):
    DAG_ID = "perplexity_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для chatgpt image pipeline
async def run_chatgpt_image_pipeline(conf):
    DAG_ID = "chatgpt_image_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для chatgpt image object pipeline
async def run_chatgpt_image_object_pipeline(conf):
    DAG_ID = "chatgpt_image_object_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для chatgpt classify pipeline
async def run_chatgpt_classify_pipeline(conf):
    DAG_ID = "chatgpt_classify_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

# Для chatgpt classify pipeline
async def run_chatgpt_pdf_pipeline(conf):
    DAG_ID = "chatgpt_pdf_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_analyze_food_pipeline(conf):
    DAG_ID = "chatgpt_analyze_food_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_calories_achievements_pipeline(conf):
    DAG_ID = "chatgpt_calories_achievements_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_calories_small_trend_pipeline(conf):
    DAG_ID = "chatgpt_calories_small_trend_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_calories_big_trend_pipeline(conf):
    DAG_ID = "chatgpt_calories_big_trend_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_unified_question_pipeline(conf):
    DAG_ID = "chatgpt_unified_question_pipeline"
    XCOM_TASK_ID = "generate_openai_response"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_challenge_ask_pipeline(conf):
    DAG_ID = "chatgpt_challenge_ask_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)

async def run_chatgpt_challenge_classify_pipeline(conf):
    DAG_ID = "chatgpt_challenge_classify_pipeline"
    XCOM_TASK_ID = "get_json_metrics"
    return await run_dag_and_get_xcom(DAG_ID, conf, XCOM_TASK_ID)