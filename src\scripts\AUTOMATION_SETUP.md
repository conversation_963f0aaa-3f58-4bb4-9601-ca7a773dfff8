# Настройка автоматизации экспорта данных cost_tracking

## 1. Настройка переменных в Airflow

Для работы автоматического экспорта необходимо настроить следующие переменные в Airflow Admin UI:

### Обязательные переменные:

1. **COST_TRACKING_SPREADSHEET_ID**
   - Значение: ID вашего Google Sheets документа
   - Пример: `1ABC123DEF456GHI789JKL`

2. **GOOGLE_SHEETS_CREDENTIALS_PATH**
   - Значение: Полный путь к файлу с учетными данными Google API
   - Пример: `/opt/airflow/credentials/google_sheets_credentials.json`

### Опциональные переменные:

3. **COST_TRACKING_SHEET_NAME**
   - Значение: Название листа в Google Sheets
   - По умолчанию: `Cost Tracking`

4. **COST_TRACKING_EXPORT_DAYS**
   - Значение: Количество дней для экспорта данных
   - По умолчанию: `30`

## 2. Настройка переменных через Airflow UI

1. Откройте Airflow Web UI
2. Перейдите в Admin > Variables
3. Нажмите "Add a new record"
4. Добавьте каждую переменную:
   - Key: название переменной
   - Val: значение переменной

## 3. Настройка переменных через CLI

```bash
# Установка обязательных переменных
airflow variables set COST_TRACKING_SPREADSHEET_ID "YOUR_SPREADSHEET_ID"
airflow variables set GOOGLE_SHEETS_CREDENTIALS_PATH "/path/to/credentials.json"

# Установка опциональных переменных
airflow variables set COST_TRACKING_SHEET_NAME "Cost Tracking"
airflow variables set COST_TRACKING_EXPORT_DAYS "30"
```

## 4. Размещение файла с учетными данными

1. Скопируйте файл `google_sheets_credentials.json` в контейнер Airflow:
   ```bash
   # Создайте директорию для учетных данных
   mkdir -p /opt/airflow/credentials
   
   # Скопируйте файл
   cp google_sheets_credentials.json /opt/airflow/credentials/
   
   # Установите правильные права доступа
   chmod 600 /opt/airflow/credentials/google_sheets_credentials.json
   ```

2. Для Docker Compose добавьте volume в `docker-compose.yml`:
   ```yaml
   services:
     airflow-webserver:
       volumes:
         - ./credentials:/opt/airflow/credentials:ro
   ```

## 5. Активация DAG

1. Откройте Airflow Web UI
2. Найдите DAG `cost_tracking_export`
3. Включите его, переключив тумблер в положение "On"

## 6. Расписание выполнения

По умолчанию DAG настроен на выполнение:
- **Ежедневно в 9:00 утра** (cron: `0 9 * * *`)

Для изменения расписания отредактируйте параметр `schedule_interval` в файле `cost_tracking_export_dag.py`:

```python
# Примеры расписаний:
schedule_interval='0 9 * * *'     # Ежедневно в 9:00
schedule_interval='0 9 * * 1'     # Каждый понедельник в 9:00
schedule_interval='0 9 1 * *'     # 1 числа каждого месяца в 9:00
schedule_interval='@daily'        # Ежедневно в полночь
schedule_interval='@weekly'       # Еженедельно
schedule_interval=None            # Только ручной запуск
```

## 7. Ручной запуск

Для ручного запуска экспорта:

1. В Airflow Web UI найдите DAG `cost_tracking_export`
2. Нажмите на название DAG
3. Нажмите кнопку "Trigger DAG"

Или через CLI:
```bash
airflow dags trigger cost_tracking_export
```

## 8. Мониторинг выполнения

1. **Логи выполнения**: Airflow Web UI > DAGs > cost_tracking_export > Graph View > кликните на задачу > View Log
2. **История запусков**: Airflow Web UI > DAGs > cost_tracking_export > Tree View
3. **Статус последнего запуска**: Отображается в списке DAGs

## 9. Уведомления

Для настройки уведомлений о результатах экспорта можно:

1. **Email уведомления**: Настроить SMTP в `airflow.cfg`
2. **Slack уведомления**: Добавить Slack webhook в задачу `send_notification`
3. **Telegram уведомления**: Интегрировать Telegram Bot API

## 10. Troubleshooting

### Проблема: "Variable COST_TRACKING_SPREADSHEET_ID does not exist"
**Решение**: Убедитесь, что все переменные настроены в Airflow Variables

### Проблема: "No such file or directory: credentials.json"
**Решение**: Проверьте путь к файлу учетных данных и права доступа

### Проблема: "The caller does not have permission"
**Решение**: Убедитесь, что Service Account добавлен в Google Sheets с правами Editor

### Проблема: "Redis connection failed"
**Решение**: Проверьте доступность Redis сервера и правильность настроек подключения

## 11. Безопасность

- Файл с учетными данными должен иметь права доступа 600
- Не храните учетные данные в репозитории
- Регулярно ротируйте ключи Service Account
- Используйте отдельный Service Account только для этой задачи
