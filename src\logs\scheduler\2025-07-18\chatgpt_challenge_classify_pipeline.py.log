[2025-07-18T11:25:56.872+0000] {processor.py:186} INFO - Started process (PID=211) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:25:56.874+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:25:56.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.876+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:25:56.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.982+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:56.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:25:57.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.094+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.106+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.259+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.269+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.277+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.285+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.293+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.294+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:57.305+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:57.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.306+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:25:57.322+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.458 seconds
[2025-07-18T11:26:28.045+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:28.046+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:26:28.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.049+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:28.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.127+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:28.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:28.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:26:28.416+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.378 seconds
[2025-07-18T11:26:58.915+0000] {processor.py:186} INFO - Started process (PID=473) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:58.916+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:26:58.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:59.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.129+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:59.241+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.241+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:59.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:26:59.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.360 seconds
[2025-07-18T11:27:29.368+0000] {processor.py:186} INFO - Started process (PID=604) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:27:29.369+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:27:29.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.371+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:27:29.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.448+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:29.456+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:27:29.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.557+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:29.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:27:29.590+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.228 seconds
[2025-07-18T11:28:00.197+0000] {processor.py:186} INFO - Started process (PID=735) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:00.198+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:28:00.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.200+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:00.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.278+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:00.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:00.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.381+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:00.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.391+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:28:00.411+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.219 seconds
[2025-07-18T11:28:30.697+0000] {processor.py:186} INFO - Started process (PID=866) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:30.698+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:28:30.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:30.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.784+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:30.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.897+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.910+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:28:30.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.241 seconds
[2025-07-18T11:29:01.044+0000] {processor.py:186} INFO - Started process (PID=997) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:01.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:29:01.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.047+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:01.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.118+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:01.127+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:01.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.223+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:01.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.234+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:29:01.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.218 seconds
[2025-07-18T11:29:31.739+0000] {processor.py:186} INFO - Started process (PID=1128) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:31.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:29:31.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:31.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.822+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.832+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:31.939+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.939+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.952+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:29:31.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.244 seconds
[2025-07-18T11:30:02.725+0000] {processor.py:186} INFO - Started process (PID=1259) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:02.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:30:02.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:02.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.804+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.813+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:02.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.910+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.921+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:30:02.941+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.222 seconds
[2025-07-18T11:30:33.325+0000] {processor.py:186} INFO - Started process (PID=1390) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:33.326+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:30:33.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.328+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:33.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.407+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:33.415+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:33.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:33.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:30:33.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.240 seconds
[2025-07-18T11:31:03.981+0000] {processor.py:186} INFO - Started process (PID=1521) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:03.982+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:31:03.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:04.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:04.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:04.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:04.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.252+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:31:04.272+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.299 seconds
[2025-07-18T11:31:35.003+0000] {processor.py:186} INFO - Started process (PID=1652) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:35.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:31:35.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:35.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:35.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:35.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:35.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:31:35.235+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.240 seconds
