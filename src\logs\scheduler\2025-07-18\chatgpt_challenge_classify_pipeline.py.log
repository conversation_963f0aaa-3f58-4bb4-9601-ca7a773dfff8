[2025-07-18T11:25:56.872+0000] {processor.py:186} INFO - Started process (PID=211) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:25:56.874+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:25:56.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.876+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:25:56.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.982+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:56.992+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:25:57.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.094+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.106+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.259+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.269+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.277+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.285+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.293+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.294+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:57.305+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:57.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.306+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_challenge_classify_pipeline
[2025-07-18T11:25:57.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:25:57.322+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.458 seconds
[2025-07-18T11:26:28.045+0000] {processor.py:186} INFO - Started process (PID=342) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:28.046+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:26:28.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.049+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:28.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.127+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:28.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.389+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:28.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:26:28.416+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.378 seconds
[2025-07-18T11:26:58.915+0000] {processor.py:186} INFO - Started process (PID=473) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:58.916+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:26:58.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:59.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.129+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.137+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:26:59.241+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.241+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:59.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.251+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:26:59.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.360 seconds
[2025-07-18T11:27:29.368+0000] {processor.py:186} INFO - Started process (PID=604) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:27:29.369+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:27:29.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.371+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:27:29.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.448+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:29.456+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:27:29.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.557+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:29.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.570+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:27:29.590+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.228 seconds
[2025-07-18T11:28:00.197+0000] {processor.py:186} INFO - Started process (PID=735) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:00.198+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:28:00.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.200+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:00.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.278+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:00.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:00.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.381+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:00.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.391+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:28:00.411+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.219 seconds
[2025-07-18T11:28:30.697+0000] {processor.py:186} INFO - Started process (PID=866) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:30.698+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:28:30.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:30.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.784+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.793+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:28:30.898+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.897+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.910+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:28:30.932+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.241 seconds
[2025-07-18T11:29:01.044+0000] {processor.py:186} INFO - Started process (PID=997) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:01.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:29:01.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.047+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:01.118+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.118+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:01.127+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:01.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.223+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:01.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:01.234+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:29:01.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.218 seconds
[2025-07-18T11:29:31.739+0000] {processor.py:186} INFO - Started process (PID=1128) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:31.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:29:31.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:31.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.822+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.832+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:29:31.939+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.939+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.952+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:29:31.976+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.244 seconds
[2025-07-18T11:30:02.725+0000] {processor.py:186} INFO - Started process (PID=1259) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:02.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:30:02.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:02.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.804+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.813+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:02.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.910+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.921+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:30:02.941+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.222 seconds
[2025-07-18T11:30:33.325+0000] {processor.py:186} INFO - Started process (PID=1390) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:33.326+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:30:33.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.328+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:33.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.407+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:33.415+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:30:33.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:33.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:30:33.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.240 seconds
[2025-07-18T11:31:03.981+0000] {processor.py:186} INFO - Started process (PID=1521) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:03.982+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:31:03.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:04.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:04.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:04.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:04.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:04.252+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:31:04.272+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.299 seconds
[2025-07-18T11:31:35.003+0000] {processor.py:186} INFO - Started process (PID=1652) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:35.004+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:31:35.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:35.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.087+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:35.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:31:35.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.203+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:35.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.213+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:31:35.235+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.240 seconds
[2025-07-18T11:32:05.671+0000] {processor.py:186} INFO - Started process (PID=1783) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:32:05.672+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:32:05.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:32:05.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.755+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:05.765+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:32:05.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.866+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:05.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:32:05.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.235 seconds
[2025-07-18T11:32:36.024+0000] {processor.py:186} INFO - Started process (PID=1914) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:32:36.026+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:32:36.029+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:36.029+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:32:36.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:36.112+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:36.121+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:32:36.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:36.233+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:36.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:36.243+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:32:36.263+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.245 seconds
[2025-07-18T11:33:06.569+0000] {processor.py:186} INFO - Started process (PID=2047) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:33:06.570+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:33:06.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.572+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:33:06.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.642+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:06.653+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:33:06.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.750+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:06.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.760+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:33:06.781+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.219 seconds
[2025-07-18T11:33:37.798+0000] {processor.py:186} INFO - Started process (PID=2181) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:33:37.800+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:33:37.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:33:38.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.197+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:38.206+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:33:38.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.322+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:38.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.340+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:33:38.365+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.573 seconds
[2025-07-18T11:34:08.636+0000] {processor.py:186} INFO - Started process (PID=2334) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:34:08.638+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:34:08.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.641+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:34:09.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.035+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:09.045+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:34:09.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:09.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.313+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:34:09.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.710 seconds
[2025-07-18T11:34:40.094+0000] {processor.py:186} INFO - Started process (PID=2489) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:34:40.095+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:34:40.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.098+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:34:40.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.505+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:40.512+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:34:40.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.619+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:40.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.782+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:34:40.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.717 seconds
[2025-07-18T11:35:11.322+0000] {processor.py:186} INFO - Started process (PID=2640) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:35:11.323+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:35:11.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:11.325+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:35:11.711+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:11.711+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:11.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:35:12.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.004+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:12.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.016+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:35:12.039+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.725 seconds
[2025-07-18T11:35:42.170+0000] {processor.py:186} INFO - Started process (PID=2795) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:35:42.171+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:35:42.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.174+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:35:42.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.548+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:42.558+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:35:42.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.838+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:42.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.847+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:35:42.869+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.705 seconds
[2025-07-18T11:36:13.536+0000] {processor.py:186} INFO - Started process (PID=2948) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:36:13.537+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:36:13.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.540+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:36:13.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.917+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:13.928+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:36:14.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.194+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:14.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:36:14.227+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.701 seconds
[2025-07-18T11:36:44.302+0000] {processor.py:186} INFO - Started process (PID=3101) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:36:44.303+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:36:44.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.304+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:36:44.681+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.681+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:44.691+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:36:44.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.973+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:44.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.986+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:36:45.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.709 seconds
[2025-07-18T11:37:15.220+0000] {processor.py:186} INFO - Started process (PID=3255) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:37:15.221+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:37:15.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:37:15.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.638+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:15.650+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:37:15.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.916+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:15.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.928+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:37:15.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.741 seconds
[2025-07-18T11:37:47.472+0000] {processor.py:186} INFO - Started process (PID=3406) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:37:47.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:37:47.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:47.484+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:37:48.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.275+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:48.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:37:48.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:48.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.609+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:37:48.629+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 1.175 seconds
[2025-07-18T11:38:18.884+0000] {processor.py:186} INFO - Started process (PID=3559) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:38:18.885+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:38:18.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:18.888+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:38:19.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.431+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:19.439+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:38:19.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.533+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:19.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:38:19.563+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.686 seconds
[2025-07-18T11:38:49.945+0000] {processor.py:186} INFO - Started process (PID=3712) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:38:49.946+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:38:49.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.948+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:38:50.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.520+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:50.528+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:38:50.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.629+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:50.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:38:50.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.719 seconds
[2025-07-18T11:39:20.891+0000] {processor.py:186} INFO - Started process (PID=3867) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:39:20.892+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:39:20.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.894+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:39:21.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.391+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:21.400+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:39:21.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:21.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.513+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:39:21.530+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.646 seconds
[2025-07-18T11:39:51.701+0000] {processor.py:186} INFO - Started process (PID=4020) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:39:51.702+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:39:51.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:39:52.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.191+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:52.200+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:39:52.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:52.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.304+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:39:52.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.631 seconds
[2025-07-18T11:40:22.599+0000] {processor.py:186} INFO - Started process (PID=4173) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:40:22.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:40:22.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:22.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:40:23.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.138+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:23.146+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:40:23.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.249+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:23.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.261+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:40:23.284+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.690 seconds
[2025-07-18T11:40:54.107+0000] {processor.py:186} INFO - Started process (PID=4326) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:40:54.108+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:40:54.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.110+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:40:54.571+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.571+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:54.582+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:40:54.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:54.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.677+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:40:54.694+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.592 seconds
[2025-07-18T11:41:25.240+0000] {processor.py:186} INFO - Started process (PID=4485) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:41:25.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:41:25.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.243+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:41:25.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.721+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:25.728+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:41:25.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.819+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:25.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.828+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:41:25.845+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.610 seconds
[2025-07-18T11:41:56.058+0000] {processor.py:186} INFO - Started process (PID=4644) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:41:56.059+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:41:56.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:56.061+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:41:56.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:56.546+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:56.553+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:41:56.647+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:56.646+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:56.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:56.656+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:41:56.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.624 seconds
[2025-07-18T11:42:27.302+0000] {processor.py:186} INFO - Started process (PID=4803) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:42:27.303+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:42:27.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.305+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:42:27.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.785+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:27.795+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:42:27.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.889+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:27.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.900+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:42:27.919+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.623 seconds
[2025-07-18T11:42:58.133+0000] {processor.py:186} INFO - Started process (PID=4968) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:42:58.134+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:42:58.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.136+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:42:58.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.632+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:58.642+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:42:58.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.739+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:58.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.749+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:42:58.769+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.643 seconds
[2025-07-18T11:43:28.940+0000] {processor.py:186} INFO - Started process (PID=5127) to work on /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:43:28.941+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py for tasks to queue
[2025-07-18T11:43:28.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.943+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:43:29.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.395+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:29.403+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_challenge_classify_pipeline' retrieved from /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py
[2025-07-18T11:43:29.488+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.487+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:29.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.497+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_challenge_classify_pipeline to None, run_after=None
[2025-07-18T11:43:29.518+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_classify_pipeline.py took 0.583 seconds
