[2025-07-18T11:25:58.441+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:25:58.442+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:25:58.444+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.444+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:25:58.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.521+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:58.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:25:58.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.808+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_goals_pipeline
[2025-07-18T11:25:58.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.821+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_goals_pipeline
[2025-07-18T11:25:58.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.829+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_goals_pipeline
[2025-07-18T11:25:58.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.838+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_goals_pipeline
[2025-07-18T11:25:58.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.844+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_goals_pipeline
[2025-07-18T11:25:58.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.850+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_goals_pipeline
[2025-07-18T11:25:58.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.858+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_goals_pipeline
[2025-07-18T11:25:58.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.859+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:58.870+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:58.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.870+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_goals_pipeline
[2025-07-18T11:25:58.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.871+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:25:58.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.453 seconds
[2025-07-18T11:26:29.784+0000] {processor.py:186} INFO - Started process (PID=377) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:26:29.785+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:26:29.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.787+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:26:29.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.877+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:29.887+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:26:30.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.107+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:26:30.136+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.358 seconds
[2025-07-18T11:27:00.974+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:00.975+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:27:00.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.977+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:01.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.204+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.213+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:01.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:01.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.320+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:27:01.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.373 seconds
[2025-07-18T11:27:31.702+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:31.703+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:27:31.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:31.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.777+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.786+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:31.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.889+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.900+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:27:31.922+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.226 seconds
[2025-07-18T11:28:02.274+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:02.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:28:02.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:02.347+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.347+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.356+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:02.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.461+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.472+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:28:02.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.225 seconds
[2025-07-18T11:28:32.837+0000] {processor.py:186} INFO - Started process (PID=903) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:32.838+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:28:32.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:32.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.920+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.929+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:33.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.050+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:28:33.073+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.243 seconds
[2025-07-18T11:29:03.134+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:03.135+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:29:03.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.137+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:03.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.205+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:03.214+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:03.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.308+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:03.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.319+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:29:03.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.211 seconds
[2025-07-18T11:29:33.674+0000] {processor.py:186} INFO - Started process (PID=1163) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:33.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:29:33.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:33.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.749+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.756+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:33.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.858+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.869+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:29:33.890+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.222 seconds
[2025-07-18T11:30:05.043+0000] {processor.py:186} INFO - Started process (PID=1296) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:05.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:30:05.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.046+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:05.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.128+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.138+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:05.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.249+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.262+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:30:05.283+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.246 seconds
[2025-07-18T11:30:36.190+0000] {processor.py:186} INFO - Started process (PID=1427) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:36.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:30:36.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:36.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.262+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.272+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:36.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.377+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.391+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:30:36.414+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.231 seconds
[2025-07-18T11:31:06.584+0000] {processor.py:186} INFO - Started process (PID=1556) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:06.585+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:31:06.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.587+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:06.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.657+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:06.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.773+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.782+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:31:06.803+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.225 seconds
