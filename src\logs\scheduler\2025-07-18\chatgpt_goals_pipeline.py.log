[2025-07-18T11:25:58.441+0000] {processor.py:186} INFO - Started process (PID=246) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:25:58.442+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:25:58.444+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.444+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:25:58.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.521+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:58.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:25:58.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.808+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_goals_pipeline
[2025-07-18T11:25:58.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.821+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_goals_pipeline
[2025-07-18T11:25:58.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.829+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_goals_pipeline
[2025-07-18T11:25:58.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.838+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_goals_pipeline
[2025-07-18T11:25:58.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.844+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_goals_pipeline
[2025-07-18T11:25:58.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.850+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_goals_pipeline
[2025-07-18T11:25:58.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.858+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_goals_pipeline
[2025-07-18T11:25:58.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.859+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:58.870+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:58.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.870+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_goals_pipeline
[2025-07-18T11:25:58.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.871+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:25:58.889+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.453 seconds
[2025-07-18T11:26:29.784+0000] {processor.py:186} INFO - Started process (PID=377) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:26:29.785+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:26:29.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.787+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:26:29.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.877+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:29.887+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:26:30.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.107+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:26:30.136+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.358 seconds
[2025-07-18T11:27:00.974+0000] {processor.py:186} INFO - Started process (PID=510) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:00.975+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:27:00.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.977+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:01.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.204+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.213+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:01.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:01.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.320+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:27:01.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.373 seconds
[2025-07-18T11:27:31.702+0000] {processor.py:186} INFO - Started process (PID=641) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:31.703+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:27:31.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:31.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.777+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.786+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:27:31.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.889+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.900+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:27:31.922+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.226 seconds
[2025-07-18T11:28:02.274+0000] {processor.py:186} INFO - Started process (PID=772) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:02.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:28:02.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:02.347+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.347+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.356+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:02.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.461+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.472+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:28:02.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.225 seconds
[2025-07-18T11:28:32.837+0000] {processor.py:186} INFO - Started process (PID=903) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:32.838+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:28:32.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.840+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:32.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.920+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.929+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:28:33.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.036+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.050+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:28:33.073+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.243 seconds
[2025-07-18T11:29:03.134+0000] {processor.py:186} INFO - Started process (PID=1034) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:03.135+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:29:03.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.137+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:03.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.205+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:03.214+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:03.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.308+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:03.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.319+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:29:03.339+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.211 seconds
[2025-07-18T11:29:33.674+0000] {processor.py:186} INFO - Started process (PID=1163) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:33.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:29:33.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:33.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.749+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.756+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:29:33.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.858+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.869+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:29:33.890+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.222 seconds
[2025-07-18T11:30:05.043+0000] {processor.py:186} INFO - Started process (PID=1296) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:05.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:30:05.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.046+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:05.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.128+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.138+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:05.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.249+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.262+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:30:05.283+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.246 seconds
[2025-07-18T11:30:36.190+0000] {processor.py:186} INFO - Started process (PID=1427) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:36.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:30:36.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:36.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.262+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.272+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:30:36.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.377+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.391+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:30:36.414+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.231 seconds
[2025-07-18T11:31:06.584+0000] {processor.py:186} INFO - Started process (PID=1556) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:06.585+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:31:06.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.587+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:06.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.657+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:06.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.773+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.782+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:31:06.803+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.225 seconds
[2025-07-18T11:31:37.264+0000] {processor.py:186} INFO - Started process (PID=1689) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:37.265+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:31:37.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.267+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:37.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.343+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:37.352+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:31:37.464+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.463+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:37.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.475+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:31:37.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.240 seconds
[2025-07-18T11:32:07.754+0000] {processor.py:186} INFO - Started process (PID=1818) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:32:07.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:32:07.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.758+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:32:07.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.839+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:07.851+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:32:07.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.966+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:07.980+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.979+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:32:07.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.252 seconds
[2025-07-18T11:32:38.420+0000] {processor.py:186} INFO - Started process (PID=1951) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:32:38.421+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:32:38.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:32:38.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.498+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:38.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:32:38.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.603+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:38.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.613+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:32:38.632+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.218 seconds
[2025-07-18T11:33:08.936+0000] {processor.py:186} INFO - Started process (PID=2082) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:33:08.937+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:33:08.939+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.939+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:33:09.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.014+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:09.023+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:33:09.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.116+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:09.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:33:09.145+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.215 seconds
[2025-07-18T11:33:39.900+0000] {processor.py:186} INFO - Started process (PID=2223) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:33:39.902+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:33:39.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.904+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:33:40.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.338+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:40.346+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:33:40.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.454+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:40.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.465+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:33:40.491+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.598 seconds
[2025-07-18T11:34:11.322+0000] {processor.py:186} INFO - Started process (PID=2376) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:34:11.323+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:34:11.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:11.326+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:34:11.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:11.718+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:11.726+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:34:12.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.011+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:12.027+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.026+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:34:12.050+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.735 seconds
[2025-07-18T11:34:42.641+0000] {processor.py:186} INFO - Started process (PID=2529) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:34:42.642+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:34:42.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.644+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:34:43.034+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.033+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:43.043+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:34:43.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:43.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.321+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:34:43.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.705 seconds
[2025-07-18T11:35:13.943+0000] {processor.py:186} INFO - Started process (PID=2682) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:35:13.948+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:35:13.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.953+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:35:14.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.350+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:14.360+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:35:14.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.662+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:14.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.672+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:35:14.696+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.759 seconds
[2025-07-18T11:35:45.629+0000] {processor.py:186} INFO - Started process (PID=2835) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:35:45.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:35:45.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:35:46.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.044+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:46.053+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:35:46.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.328+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:46.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.341+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:35:46.364+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.743 seconds
[2025-07-18T11:36:16.526+0000] {processor.py:186} INFO - Started process (PID=2988) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:36:16.527+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:36:16.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.531+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:36:17.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.007+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:17.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:36:17.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:17.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.320+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:36:17.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.821 seconds
[2025-07-18T11:36:47.947+0000] {processor.py:186} INFO - Started process (PID=3143) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:36:47.952+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:36:47.954+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:47.954+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:36:48.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:48.341+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:48.350+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:36:48.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:48.697+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:48.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:48.711+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:36:48.737+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.798 seconds
[2025-07-18T11:37:18.989+0000] {processor.py:186} INFO - Started process (PID=3297) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:37:18.990+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:37:18.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:18.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:37:19.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:19.373+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:19.383+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:37:19.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:19.654+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:19.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:19.664+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:37:19.685+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.702 seconds
[2025-07-18T11:37:50.417+0000] {processor.py:186} INFO - Started process (PID=3448) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:37:50.418+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:37:50.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.421+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:37:50.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.785+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:50.794+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:37:51.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:51.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.073+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:37:51.093+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.684 seconds
[2025-07-18T11:38:21.518+0000] {processor.py:186} INFO - Started process (PID=3601) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:38:21.519+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:38:21.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.522+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:38:21.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.896+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:21.906+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:38:22.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.157+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:22.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.169+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:38:22.192+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.681 seconds
[2025-07-18T11:38:52.810+0000] {processor.py:186} INFO - Started process (PID=3756) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:38:52.811+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:38:52.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.814+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:38:53.340+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:53.339+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:53.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:38:53.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:53.445+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:53.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:53.455+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:38:53.476+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.672 seconds
[2025-07-18T11:39:23.901+0000] {processor.py:186} INFO - Started process (PID=3909) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:39:23.902+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:39:23.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.905+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:39:24.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:24.497+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:24.506+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:39:24.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:24.623+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:24.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:24.635+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:39:24.655+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.761 seconds
[2025-07-18T11:39:54.838+0000] {processor.py:186} INFO - Started process (PID=4060) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:39:54.839+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:39:54.842+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.841+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:39:55.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:55.371+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:55.380+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:39:55.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:55.483+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:55.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:55.495+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:39:55.515+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.684 seconds
[2025-07-18T11:40:25.885+0000] {processor.py:186} INFO - Started process (PID=4215) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:40:25.886+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:40:25.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.888+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:40:26.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:26.407+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:26.415+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:40:26.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:26.518+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:26.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:26.529+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:40:26.551+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.672 seconds
[2025-07-18T11:40:56.920+0000] {processor.py:186} INFO - Started process (PID=4371) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:40:56.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:40:56.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.923+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:40:57.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.428+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:57.437+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:40:57.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.530+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:57.540+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.540+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:40:57.560+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.647 seconds
[2025-07-18T11:41:28.090+0000] {processor.py:186} INFO - Started process (PID=4537) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:41:28.092+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:41:28.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.095+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:41:28.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.587+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:28.595+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:41:28.692+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.692+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:28.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:41:28.724+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.641 seconds
[2025-07-18T11:41:58.808+0000] {processor.py:186} INFO - Started process (PID=4696) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:41:58.810+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:41:58.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.812+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:41:59.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:59.304+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:59.311+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:41:59.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:59.406+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:59.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:59.414+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:41:59.432+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.629 seconds
[2025-07-18T11:42:29.970+0000] {processor.py:186} INFO - Started process (PID=4855) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:42:29.971+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:42:29.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.974+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:42:30.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.445+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:30.453+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:42:30.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:30.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.577+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:42:30.601+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.638 seconds
[2025-07-18T11:43:00.840+0000] {processor.py:186} INFO - Started process (PID=5014) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:43:00.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:43:00.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.845+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:43:01.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.357+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:01.366+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:43:01.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.480+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:01.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.491+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:43:01.513+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.680 seconds
[2025-07-18T11:43:31.582+0000] {processor.py:186} INFO - Started process (PID=5173) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:43:31.583+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:43:31.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.586+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:43:32.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.126+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:32.134+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:43:32.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.239+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:32.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.248+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:43:32.268+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.692 seconds
[2025-07-18T11:44:02.907+0000] {processor.py:186} INFO - Started process (PID=5332) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:44:02.908+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:44:02.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:02.910+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:44:03.425+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:03.425+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:03.441+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:44:03.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:03.548+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:03.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:03.559+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:44:03.578+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.677 seconds
[2025-07-18T11:44:34.275+0000] {processor.py:186} INFO - Started process (PID=5491) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:44:34.276+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:44:34.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:34.278+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:44:34.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:34.750+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:34.758+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:44:34.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:34.849+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:34.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:34.859+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:44:34.879+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.610 seconds
[2025-07-18T11:45:05.033+0000] {processor.py:186} INFO - Started process (PID=5650) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:45:05.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:45:05.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:05.037+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:45:05.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:05.534+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:05.541+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:45:05.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:05.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:05.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:05.653+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:45:05.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.649 seconds
[2025-07-18T11:45:36.499+0000] {processor.py:186} INFO - Started process (PID=5809) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:45:36.500+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:45:36.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:36.502+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:45:37.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:37.048+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:37.056+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:45:37.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:37.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:37.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:37.174+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:45:37.195+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.703 seconds
[2025-07-18T11:46:07.406+0000] {processor.py:186} INFO - Started process (PID=5973) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:46:07.408+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:46:07.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:07.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:46:07.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:07.915+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:07.924+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:46:08.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:08.028+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:08.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:08.041+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:46:08.064+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.664 seconds
[2025-07-18T11:46:38.178+0000] {processor.py:186} INFO - Started process (PID=6132) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:46:38.179+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:46:38.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:38.181+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:46:38.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:38.652+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:38.659+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:46:38.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:38.747+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:38.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:38.760+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:46:38.781+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.609 seconds
[2025-07-18T11:47:09.697+0000] {processor.py:186} INFO - Started process (PID=6291) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:47:09.698+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:47:09.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:09.701+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:47:10.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:10.217+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:10.225+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:47:10.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:10.333+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:10.343+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:10.343+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:47:10.367+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.677 seconds
[2025-07-18T11:47:40.630+0000] {processor.py:186} INFO - Started process (PID=6449) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:47:40.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:47:40.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:40.633+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:47:41.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:41.146+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:41.152+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:47:41.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:41.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:41.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:41.253+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:47:41.271+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.648 seconds
[2025-07-18T11:48:11.514+0000] {processor.py:186} INFO - Started process (PID=6608) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:48:11.515+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:48:11.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:11.518+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:48:12.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:12.074+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:12.083+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:48:12.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:12.188+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:12.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:12.198+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:48:12.215+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.707 seconds
[2025-07-18T11:48:42.806+0000] {processor.py:186} INFO - Started process (PID=6767) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:48:42.806+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:48:42.810+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:42.810+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:48:43.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:43.340+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:43.348+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:48:43.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:43.453+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:43.466+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:43.465+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:48:43.485+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.686 seconds
[2025-07-18T11:49:13.822+0000] {processor.py:186} INFO - Started process (PID=6926) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:49:13.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:49:13.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:13.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:49:14.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:14.347+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:14.355+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:49:14.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:14.459+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:14.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:14.468+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:49:14.486+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.671 seconds
[2025-07-18T11:49:44.726+0000] {processor.py:186} INFO - Started process (PID=7085) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:49:44.728+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:49:44.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:44.731+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:49:45.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:45.307+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:45.314+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:49:45.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:45.417+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:45.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:45.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:49:45.451+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.734 seconds
[2025-07-18T11:50:15.930+0000] {processor.py:186} INFO - Started process (PID=7244) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:50:15.931+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:50:15.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:15.933+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:50:16.471+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:16.471+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:16.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:50:16.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:16.579+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:16.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:16.589+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:50:16.609+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.686 seconds
[2025-07-18T11:50:47.356+0000] {processor.py:186} INFO - Started process (PID=7403) to work on /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:50:47.357+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_goals_pipeline.py for tasks to queue
[2025-07-18T11:50:47.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:47.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:50:47.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:47.830+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:47.841+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_goals_pipeline' retrieved from /opt/airflow/dags/chatgpt_goals_pipeline.py
[2025-07-18T11:50:47.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:47.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:47.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:47.941+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_goals_pipeline to None, run_after=None
[2025-07-18T11:50:47.959+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_goals_pipeline.py took 0.609 seconds
