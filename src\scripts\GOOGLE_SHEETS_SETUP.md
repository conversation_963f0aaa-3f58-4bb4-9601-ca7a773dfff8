# Настройка Google Sheets API для экспорта данных cost_tracking

## 1. Создание проекта в Google Cloud Console

1. Перейдите в [Google Cloud Console](https://console.cloud.google.com/)
2. Создайте новый проект или выберите существующий
3. Включите Google Sheets API:
   - Перейдите в "APIs & Services" > "Library"
   - Найдите "Google Sheets API"
   - Нажмите "Enable"

## 2. Создание Service Account

1. Перейдите в "APIs & Services" > "Credentials"
2. Нажмите "Create Credentials" > "Service Account"
3. Заполните информацию о Service Account:
   - Name: `biome-ai-cost-tracking`
   - Description: `Service account for cost tracking data export`
4. Нажмите "Create and Continue"
5. Пропустите шаги с ролями (можно настроить позже)
6. Нажмите "Done"

## 3. Создание ключа для Service Account

1. В списке Service Accounts найдите созданный аккаунт
2. Нажмите на него
3. Перейдите на вкладку "Keys"
4. Нажмите "Add Key" > "Create new key"
5. Выберите формат JSON
6. Скачайте файл и сохраните его в безопасном месте
7. Переименуйте файл в `google_sheets_credentials.json`

## 4. Настройка Google Sheets документа

1. Создайте новый Google Sheets документ
2. Скопируйте ID документа из URL (часть между `/d/` и `/edit`)
   - Пример: `https://docs.google.com/spreadsheets/d/1ABC123DEF456/edit`
   - ID: `1ABC123DEF456`
3. Поделитесь документом с Service Account:
   - Откройте файл `google_sheets_credentials.json`
   - Найдите поле `client_email`
   - Скопируйте email адрес
   - В Google Sheets нажмите "Share"
   - Добавьте email Service Account с правами "Editor"

## 5. Установка зависимостей

```bash
pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client pandas
```

## 6. Использование

### Экспорт только в CSV:
```bash
python src/scripts/cost_tracking_export.py --mode csv --output cost_data.csv --days 30
```

### Загрузка CSV в Google Sheets:
```bash
python src/scripts/cost_tracking_export.py --mode sheets \
  --output cost_data.csv \
  --spreadsheet-id "YOUR_SPREADSHEET_ID" \
  --credentials "path/to/google_sheets_credentials.json"
```

### Полный процесс (экспорт + загрузка):
```bash
python src/scripts/cost_tracking_export.py --mode full \
  --spreadsheet-id "YOUR_SPREADSHEET_ID" \
  --credentials "path/to/google_sheets_credentials.json" \
  --days 30
```

## 7. Автоматизация

Для автоматического экспорта можно создать cron job:

```bash
# Ежедневный экспорт в 9:00 утра
0 9 * * * /usr/bin/python3 /path/to/src/scripts/cost_tracking_export.py --mode full --spreadsheet-id "YOUR_ID" --credentials "/path/to/credentials.json"
```

## 8. Структура данных в Google Sheets

Данные будут экспортированы в следующем формате:

| Название | Tokens (input) | Tokens (output) | Tokens (Всего) | Стоимость (input) USD | Стоимость (output) USD | Стоимость (Всего) USD |
|----------|----------------|-----------------|----------------|----------------------|----------------------|---------------------|
| ГЕНЕРАЦИЯ РИСКОВ И РЕКОМЕНДАЦИЙ | 3843 | 2934 | 6777 | 0.009607 | 0.02934 | 0.038947 |
| ... | ... | ... | ... | ... | ... | ... |
| ИТОГО ЗА ПОЛЬЗОВАТЕЛЬ: | 216028 | 17923 | 233951 | 0.54 | 0.18 | 0.72 |

## Безопасность

- Никогда не коммитьте файл с учетными данными в репозиторий
- Храните `google_sheets_credentials.json` в безопасном месте
- Используйте переменные окружения для путей к файлам в продакшене
- Регулярно ротируйте ключи Service Account
