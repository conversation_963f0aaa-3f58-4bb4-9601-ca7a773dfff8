[2025-07-18T11:25:58.307+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:25:58.308+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:25:58.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.310+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:25:58.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.397+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:58.406+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:25:58.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.508+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.659+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.667+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.680+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.688+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.696+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.705+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.707+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:58.718+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:58.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.719+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:25:58.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.443 seconds
[2025-07-18T11:26:29.399+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:26:29.401+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:26:29.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:26:29.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.492+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:29.501+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:26:29.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.759+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:29.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.769+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:26:29.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.396 seconds
[2025-07-18T11:27:00.604+0000] {processor.py:186} INFO - Started process (PID=505) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:00.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:27:00.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:00.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.806+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:00.811+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:00.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.900+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.910+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:27:00.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.329 seconds
[2025-07-18T11:27:31.446+0000] {processor.py:186} INFO - Started process (PID=636) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:31.447+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:27:31.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.449+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:31.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.523+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:31.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.628+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:27:31.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.218 seconds
[2025-07-18T11:28:02.005+0000] {processor.py:186} INFO - Started process (PID=767) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:02.006+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:28:02.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:02.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:02.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.205+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:28:02.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.227 seconds
[2025-07-18T11:28:32.551+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:32.552+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:28:32.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.555+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:32.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.633+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.641+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:32.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:32.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.764+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:28:32.786+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.241 seconds
[2025-07-18T11:29:02.870+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:02.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:29:02.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:02.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.959+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:03.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:03.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:29:03.084+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.221 seconds
[2025-07-18T11:29:33.644+0000] {processor.py:186} INFO - Started process (PID=1160) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:33.645+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:29:33.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:33.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.728+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.735+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:33.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:29:33.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.234 seconds
[2025-07-18T11:30:04.732+0000] {processor.py:186} INFO - Started process (PID=1291) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:04.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:30:04.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.738+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:04.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.842+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:04.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:30:04.989+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.266 seconds
[2025-07-18T11:30:35.931+0000] {processor.py:186} INFO - Started process (PID=1422) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:35.932+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:30:35.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.934+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:36.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.008+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.015+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:36.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.119+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.130+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:30:36.149+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.225 seconds
[2025-07-18T11:31:06.574+0000] {processor.py:186} INFO - Started process (PID=1553) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:06.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:31:06.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.577+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:06.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.654+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.663+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:06.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:31:06.802+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.234 seconds
[2025-07-18T11:31:36.969+0000] {processor.py:186} INFO - Started process (PID=1682) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:36.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:31:36.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:37.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.051+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:37.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:37.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.186+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:37.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.199+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:31:37.224+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.262 seconds
[2025-07-18T11:32:07.725+0000] {processor.py:186} INFO - Started process (PID=1815) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:32:07.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:32:07.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.729+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:32:07.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.803+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:07.811+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:32:07.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.921+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:07.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.932+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:32:07.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.234 seconds
[2025-07-18T11:32:38.159+0000] {processor.py:186} INFO - Started process (PID=1946) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:32:38.160+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:32:38.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.162+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:32:38.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.234+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:38.245+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:32:38.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:38.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.358+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:32:38.378+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.225 seconds
[2025-07-18T11:33:08.685+0000] {processor.py:186} INFO - Started process (PID=2077) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:33:08.686+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:33:08.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.689+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:33:08.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.764+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:08.772+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:33:08.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.866+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:08.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:33:08.895+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.217 seconds
[2025-07-18T11:33:39.587+0000] {processor.py:186} INFO - Started process (PID=2217) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:33:39.588+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:33:39.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.590+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:33:39.986+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.986+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:40.002+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:33:40.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.121+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:40.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.307+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:33:40.324+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.743 seconds
[2025-07-18T11:34:10.976+0000] {processor.py:186} INFO - Started process (PID=2370) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:34:10.977+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:34:10.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.980+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:34:11.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:11.369+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:11.378+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:34:11.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:11.492+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:11.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:11.651+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:34:11.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.706 seconds
[2025-07-18T11:34:42.365+0000] {processor.py:186} INFO - Started process (PID=2523) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:34:42.367+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:34:42.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.369+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:34:42.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.747+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:42.760+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:34:43.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.013+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:43.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.024+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:34:43.043+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.684 seconds
[2025-07-18T11:35:13.770+0000] {processor.py:186} INFO - Started process (PID=2676) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:35:13.772+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:35:13.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:35:14.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.167+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:14.178+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:35:14.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.446+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:14.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.457+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:35:14.483+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.721 seconds
[2025-07-18T11:35:45.062+0000] {processor.py:186} INFO - Started process (PID=2829) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:35:45.063+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:35:45.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.066+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:35:45.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.510+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:45.520+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:35:45.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.795+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:45.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.808+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:35:45.831+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.776 seconds
[2025-07-18T11:36:16.086+0000] {processor.py:186} INFO - Started process (PID=2982) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:36:16.087+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:36:16.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.089+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:36:16.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.470+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:16.480+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:36:16.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.783+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:16.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.807+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:36:16.842+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.763 seconds
[2025-07-18T11:36:47.194+0000] {processor.py:186} INFO - Started process (PID=3135) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:36:47.195+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:36:47.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:47.198+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:36:47.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:47.574+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:47.584+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:36:47.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:47.848+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:47.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:47.858+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:36:47.879+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.691 seconds
[2025-07-18T11:37:18.237+0000] {processor.py:186} INFO - Started process (PID=3289) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:37:18.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:37:18.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:18.247+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:37:18.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:18.662+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:18.669+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:37:18.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:18.899+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:18.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:18.909+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:37:18.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.707 seconds
[2025-07-18T11:37:50.155+0000] {processor.py:186} INFO - Started process (PID=3442) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:37:50.157+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:37:50.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.159+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:37:50.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.561+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:50.570+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:37:50.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.808+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:50.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.817+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:37:50.838+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.689 seconds
[2025-07-18T11:38:21.123+0000] {processor.py:186} INFO - Started process (PID=3595) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:38:21.123+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:38:21.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.126+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:38:21.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.494+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:21.502+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:38:21.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.755+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:21.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.766+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:38:21.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.668 seconds
[2025-07-18T11:38:52.147+0000] {processor.py:186} INFO - Started process (PID=3748) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:38:52.148+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:38:52.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.150+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:38:52.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.629+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:52.637+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:38:52.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.726+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:52.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.735+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:38:52.753+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.612 seconds
[2025-07-18T11:39:23.078+0000] {processor.py:186} INFO - Started process (PID=3901) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:39:23.079+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:39:23.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.082+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:39:23.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.661+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:23.668+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:39:23.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.777+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:23.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.788+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:39:23.818+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.751 seconds
[2025-07-18T11:39:54.202+0000] {processor.py:186} INFO - Started process (PID=4056) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:39:54.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:39:54.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:39:54.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.694+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:54.702+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:39:54.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.797+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:54.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.808+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:39:54.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.628 seconds
[2025-07-18T11:40:25.204+0000] {processor.py:186} INFO - Started process (PID=4209) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:40:25.205+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:40:25.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.207+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:40:25.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.691+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:25.700+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:40:25.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.795+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:25.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.805+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:40:25.824+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.628 seconds
[2025-07-18T11:40:56.094+0000] {processor.py:186} INFO - Started process (PID=4360) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:40:56.095+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:40:56.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.097+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:40:56.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.559+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:56.566+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:40:56.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.653+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:56.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.665+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:40:56.683+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.595 seconds
[2025-07-18T11:41:27.330+0000] {processor.py:186} INFO - Started process (PID=4519) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:41:27.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:41:27.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:41:27.887+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.887+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:27.893+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:41:27.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.997+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:28.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.007+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:41:28.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.706 seconds
[2025-07-18T11:41:58.113+0000] {processor.py:186} INFO - Started process (PID=4684) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:41:58.114+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:41:58.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:41:58.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.615+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:58.622+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:41:58.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.721+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:58.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.731+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:41:58.751+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.644 seconds
[2025-07-18T11:42:29.644+0000] {processor.py:186} INFO - Started process (PID=4849) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:42:29.645+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:42:29.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:42:30.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.153+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:30.160+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:42:30.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.249+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:30.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:42:30.275+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.637 seconds
[2025-07-18T11:43:00.441+0000] {processor.py:186} INFO - Started process (PID=5008) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:43:00.442+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:43:00.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:43:00.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.964+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:00.971+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:43:01.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.083+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:01.094+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.094+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:43:01.114+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.680 seconds
[2025-07-18T11:43:31.302+0000] {processor.py:186} INFO - Started process (PID=5167) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:43:31.303+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:43:31.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.305+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:43:31.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.786+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:31.794+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:43:31.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.900+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:31.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.909+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:43:31.929+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.633 seconds
