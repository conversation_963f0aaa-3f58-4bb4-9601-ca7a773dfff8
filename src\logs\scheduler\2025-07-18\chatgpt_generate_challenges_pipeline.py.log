[2025-07-18T11:25:58.307+0000] {processor.py:186} INFO - Started process (PID=241) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:25:58.308+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:25:58.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.310+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:25:58.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.397+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:58.406+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:25:58.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.508+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.659+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.667+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.680+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.688+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.697+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.696+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.705+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.707+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:58.718+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:58.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.719+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_generate_challenges_pipeline
[2025-07-18T11:25:58.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:25:58.744+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.443 seconds
[2025-07-18T11:26:29.399+0000] {processor.py:186} INFO - Started process (PID=372) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:26:29.401+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:26:29.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.403+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:26:29.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.492+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:29.501+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:26:29.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.759+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:29.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.769+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:26:29.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.396 seconds
[2025-07-18T11:27:00.604+0000] {processor.py:186} INFO - Started process (PID=505) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:00.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:27:00.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:00.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.806+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:00.811+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:00.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.900+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.910+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:27:00.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.329 seconds
[2025-07-18T11:27:31.446+0000] {processor.py:186} INFO - Started process (PID=636) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:31.447+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:27:31.449+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.449+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:31.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.523+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:27:31.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.628+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:27:31.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.218 seconds
[2025-07-18T11:28:02.005+0000] {processor.py:186} INFO - Started process (PID=767) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:02.006+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:28:02.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.009+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:02.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.090+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.099+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:02.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.205+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:28:02.226+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.227 seconds
[2025-07-18T11:28:32.551+0000] {processor.py:186} INFO - Started process (PID=898) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:32.552+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:28:32.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.555+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:32.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.633+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.641+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:28:32.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:32.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.764+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:28:32.786+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.241 seconds
[2025-07-18T11:29:02.870+0000] {processor.py:186} INFO - Started process (PID=1029) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:02.871+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:29:02.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:02.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.959+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:03.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.053+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:03.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.064+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:29:03.084+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.221 seconds
[2025-07-18T11:29:33.644+0000] {processor.py:186} INFO - Started process (PID=1160) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:33.645+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:29:33.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.648+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:33.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.728+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.735+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:29:33.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:29:33.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.234 seconds
[2025-07-18T11:30:04.732+0000] {processor.py:186} INFO - Started process (PID=1291) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:04.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:30:04.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.738+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:04.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.842+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:04.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:30:04.989+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.266 seconds
[2025-07-18T11:30:35.931+0000] {processor.py:186} INFO - Started process (PID=1422) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:35.932+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:30:35.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.934+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:36.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.008+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.015+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:30:36.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.119+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.130+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:30:36.149+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.225 seconds
[2025-07-18T11:31:06.574+0000] {processor.py:186} INFO - Started process (PID=1553) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:06.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:31:06.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.577+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:06.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.654+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.663+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:06.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:31:06.802+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.234 seconds
[2025-07-18T11:31:36.969+0000] {processor.py:186} INFO - Started process (PID=1682) to work on /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:36.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py for tasks to queue
[2025-07-18T11:31:36.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:37.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.051+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:37.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_generate_challenges_pipeline' retrieved from /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py
[2025-07-18T11:31:37.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.186+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:37.200+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.199+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_generate_challenges_pipeline to None, run_after=None
[2025-07-18T11:31:37.224+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_generate_challenges_pipeline.py took 0.262 seconds
