#!/usr/bin/env python3
"""
Скрипт для экспорта данных cost_tracking в CSV и загрузки в Google Sheets
"""

import sys
import os
import argparse
from pathlib import Path

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent.parent / "dags"))

from cost_tracking import (
    export_cost_data_to_csv,
    upload_csv_to_google_sheets,
    export_and_upload_cost_data
)

def main():
    parser = argparse.ArgumentParser(description="Экспорт данных cost_tracking")
    parser.add_argument("--mode", choices=["csv", "sheets", "full"], default="csv",
                       help="Режим работы: csv - только экспорт в CSV, sheets - загрузка CSV в Google Sheets, full - полный процесс")
    parser.add_argument("--output", default="cost_tracking_export.csv",
                       help="Путь к выходному CSV файлу")
    parser.add_argument("--days", type=int, default=30,
                       help="Количество дней для экспорта данных")
    parser.add_argument("--spreadsheet-id", 
                       help="ID Google Sheets документа")
    parser.add_argument("--sheet-name", default="Cost Tracking",
                       help="Название листа в Google Sheets")
    parser.add_argument("--credentials", 
                       help="Путь к файлу с учетными данными Google API")
    parser.add_argument("--redis-host", default="localhost",
                       help="Хост Redis сервера")
    parser.add_argument("--redis-port", type=int, default=6379,
                       help="Порт Redis сервера")
    parser.add_argument("--redis-db", type=int, default=0,
                       help="Номер базы данных Redis")
    
    args = parser.parse_args()
    
    try:
        if args.mode == "csv":
            # Только экспорт в CSV
            csv_path = export_cost_data_to_csv(
                output_file=args.output,
                days=args.days,
                redis_host=args.redis_host,
                redis_port=args.redis_port,
                redis_db=args.redis_db
            )
            print(f"✅ Данные экспортированы в CSV: {csv_path}")
            
        elif args.mode == "sheets":
            # Загрузка существующего CSV в Google Sheets
            if not args.spreadsheet_id:
                print("❌ Для режима 'sheets' необходимо указать --spreadsheet-id")
                sys.exit(1)
            if not args.credentials:
                print("❌ Для режима 'sheets' необходимо указать --credentials")
                sys.exit(1)
            if not Path(args.output).exists():
                print(f"❌ CSV файл не найден: {args.output}")
                sys.exit(1)
                
            success = upload_csv_to_google_sheets(
                csv_file_path=args.output,
                spreadsheet_id=args.spreadsheet_id,
                sheet_name=args.sheet_name,
                credentials_file=args.credentials
            )
            
            if success:
                print("✅ Данные успешно загружены в Google Sheets")
            else:
                print("❌ Ошибка загрузки в Google Sheets")
                sys.exit(1)
                
        elif args.mode == "full":
            # Полный процесс: экспорт + загрузка
            if not args.spreadsheet_id:
                print("❌ Для режима 'full' необходимо указать --spreadsheet-id")
                sys.exit(1)
            if not args.credentials:
                print("❌ Для режима 'full' необходимо указать --credentials")
                sys.exit(1)
                
            success = export_and_upload_cost_data(
                spreadsheet_id=args.spreadsheet_id,
                sheet_name=args.sheet_name,
                days=args.days,
                credentials_file=args.credentials,
                temp_csv_file=args.output,
                redis_host=args.redis_host,
                redis_port=args.redis_port,
                redis_db=args.redis_db
            )
            
            if success:
                print("✅ Полный процесс экспорта и загрузки завершен успешно")
            else:
                print("❌ Ошибка в процессе экспорта и загрузки")
                sys.exit(1)
                
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
