[2025-07-18T11:25:57.957+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:25:57.958+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:25:57.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:25:58.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.042+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:58.051+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:25:58.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.150+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.320+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.328+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.338+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.345+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.352+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.359+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:58.371+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:58.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.371+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:25:58.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.441 seconds
[2025-07-18T11:26:29.320+0000] {processor.py:186} INFO - Started process (PID=367) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:26:29.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:26:29.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:26:29.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.410+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:29.418+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:26:29.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.700+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:29.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.709+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:26:29.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.416 seconds
[2025-07-18T11:27:00.202+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:00.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:27:00.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:00.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.433+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:00.441+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:00.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.551+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:27:00.573+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.377 seconds
[2025-07-18T11:27:31.191+0000] {processor.py:186} INFO - Started process (PID=629) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:31.193+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:27:31.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.195+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:31.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.271+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.280+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:31.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.383+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.394+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:27:31.413+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.227 seconds
[2025-07-18T11:28:01.738+0000] {processor.py:186} INFO - Started process (PID=760) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:01.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:28:01.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:01.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.819+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.830+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:01.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.940+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.951+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:28:01.970+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.238 seconds
[2025-07-18T11:28:32.302+0000] {processor.py:186} INFO - Started process (PID=891) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:32.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:28:32.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:32.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.378+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.387+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:32.488+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.488+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:32.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:28:32.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.223 seconds
[2025-07-18T11:29:02.617+0000] {processor.py:186} INFO - Started process (PID=1022) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:02.618+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:29:02.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.620+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:02.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.690+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.699+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:02.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.794+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.806+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:29:02.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.217 seconds
[2025-07-18T11:29:33.358+0000] {processor.py:186} INFO - Started process (PID=1153) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:33.359+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:29:33.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.362+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:33.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.443+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:33.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:29:33.611+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.259 seconds
[2025-07-18T11:30:04.447+0000] {processor.py:186} INFO - Started process (PID=1284) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:04.448+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:30:04.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.451+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:04.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.523+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.532+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:04.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:30:04.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.244 seconds
[2025-07-18T11:30:35.656+0000] {processor.py:186} INFO - Started process (PID=1415) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:35.657+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:30:35.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.659+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:35.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.736+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:35.746+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:35.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.864+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:35.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.878+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:30:35.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.250 seconds
[2025-07-18T11:31:06.307+0000] {processor.py:186} INFO - Started process (PID=1548) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:06.308+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:31:06.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.310+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:06.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.385+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:06.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.494+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.503+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:31:06.524+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.222 seconds
[2025-07-18T11:31:36.962+0000] {processor.py:186} INFO - Started process (PID=1679) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:36.963+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:31:36.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.965+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:37.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.042+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:37.051+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:37.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.170+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:37.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.185+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:31:37.213+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.258 seconds
