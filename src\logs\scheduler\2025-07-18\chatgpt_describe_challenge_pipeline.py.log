[2025-07-18T11:25:57.957+0000] {processor.py:186} INFO - Started process (PID=236) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:25:57.958+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:25:57.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:25:58.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.042+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON><PERSON><PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:58.051+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:25:58.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.150+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.320+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.328+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.338+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.345+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.352+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.359+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:58.371+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:58.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.371+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_describe_challenge_pipeline
[2025-07-18T11:25:58.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.372+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:25:58.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.441 seconds
[2025-07-18T11:26:29.320+0000] {processor.py:186} INFO - Started process (PID=367) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:26:29.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:26:29.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:26:29.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.410+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:29.418+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:26:29.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.700+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:29.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.709+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:26:29.728+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.416 seconds
[2025-07-18T11:27:00.202+0000] {processor.py:186} INFO - Started process (PID=498) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:00.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:27:00.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.206+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:00.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.433+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:00.441+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:00.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.541+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.551+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:27:00.573+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.377 seconds
[2025-07-18T11:27:31.191+0000] {processor.py:186} INFO - Started process (PID=629) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:31.193+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:27:31.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.195+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:31.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.271+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.280+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:27:31.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.383+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.394+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:27:31.413+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.227 seconds
[2025-07-18T11:28:01.738+0000] {processor.py:186} INFO - Started process (PID=760) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:01.738+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:28:01.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:01.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.819+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.830+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:01.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.940+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.951+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:28:01.970+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.238 seconds
[2025-07-18T11:28:32.302+0000] {processor.py:186} INFO - Started process (PID=891) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:32.304+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:28:32.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.306+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:32.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.378+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.387+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:28:32.488+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.488+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:32.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:28:32.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.223 seconds
[2025-07-18T11:29:02.617+0000] {processor.py:186} INFO - Started process (PID=1022) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:02.618+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:29:02.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.620+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:02.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.690+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.699+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:02.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.794+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.806+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:29:02.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.217 seconds
[2025-07-18T11:29:33.358+0000] {processor.py:186} INFO - Started process (PID=1153) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:33.359+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:29:33.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.362+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:33.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.443+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.452+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:29:33.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:29:33.611+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.259 seconds
[2025-07-18T11:30:04.447+0000] {processor.py:186} INFO - Started process (PID=1284) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:04.448+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:30:04.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.451+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:04.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.523+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.532+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:04.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.642+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.658+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:30:04.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.244 seconds
[2025-07-18T11:30:35.656+0000] {processor.py:186} INFO - Started process (PID=1415) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:35.657+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:30:35.659+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.659+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:35.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.736+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:35.746+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:30:35.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.864+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:35.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.878+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:30:35.899+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.250 seconds
[2025-07-18T11:31:06.307+0000] {processor.py:186} INFO - Started process (PID=1548) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:06.308+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:31:06.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.310+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:06.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.385+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:06.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.494+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.504+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.503+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:31:06.524+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.222 seconds
[2025-07-18T11:31:36.962+0000] {processor.py:186} INFO - Started process (PID=1679) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:36.963+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:31:36.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.965+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:37.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.042+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:37.051+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:31:37.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.170+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:37.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.185+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:31:37.213+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.258 seconds
[2025-07-18T11:32:07.450+0000] {processor.py:186} INFO - Started process (PID=1808) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:32:07.452+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:32:07.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.454+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:32:07.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.531+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:07.542+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:32:07.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.660+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:07.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.674+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:32:07.697+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.253 seconds
[2025-07-18T11:32:37.886+0000] {processor.py:186} INFO - Started process (PID=1939) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:32:37.887+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:32:37.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.889+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:32:37.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.963+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:37.974+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:32:38.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.083+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:38.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.095+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:32:38.115+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.236 seconds
[2025-07-18T11:33:08.404+0000] {processor.py:186} INFO - Started process (PID=2070) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:33:08.405+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:33:08.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.407+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:33:08.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.483+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:08.494+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:33:08.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.599+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:08.610+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.610+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:33:08.632+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.233 seconds
[2025-07-18T11:33:39.151+0000] {processor.py:186} INFO - Started process (PID=2211) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:33:39.152+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:33:39.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.155+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:33:39.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.519+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:39.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:33:39.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.636+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:39.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.816+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:33:39.837+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.693 seconds
[2025-07-18T11:34:10.599+0000] {processor.py:186} INFO - Started process (PID=2364) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:34:10.600+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:34:10.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.602+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:34:10.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.968+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:10.977+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:34:11.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:11.089+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:11.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:11.254+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:34:11.271+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.681 seconds
[2025-07-18T11:34:41.929+0000] {processor.py:186} INFO - Started process (PID=2517) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:34:41.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:34:41.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:41.933+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:34:42.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.320+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:42.329+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:34:42.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.559+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:42.568+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:42.568+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:34:42.588+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.666 seconds
[2025-07-18T11:35:13.155+0000] {processor.py:186} INFO - Started process (PID=2670) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:35:13.157+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:35:13.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.159+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:35:13.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.575+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:13.586+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:35:13.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.863+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:13.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:13.873+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:35:13.894+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.747 seconds
[2025-07-18T11:35:44.813+0000] {processor.py:186} INFO - Started process (PID=2825) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:35:44.814+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:35:44.818+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:44.817+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:35:45.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.247+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:45.256+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:35:45.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.544+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:45.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.555+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:35:45.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.771 seconds
[2025-07-18T11:36:15.787+0000] {processor.py:186} INFO - Started process (PID=2978) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:36:15.788+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:36:15.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:15.790+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:36:16.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.149+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:16.158+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:36:16.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:16.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.441+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:36:16.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.680 seconds
[2025-07-18T11:36:46.564+0000] {processor.py:186} INFO - Started process (PID=3131) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:36:46.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:36:46.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:46.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:36:46.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:46.974+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:46.984+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:36:47.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:47.250+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:47.259+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:47.259+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:36:47.278+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.722 seconds
[2025-07-18T11:37:17.596+0000] {processor.py:186} INFO - Started process (PID=3285) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:37:17.597+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:37:17.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:17.599+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:37:17.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:17.984+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:17.996+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:37:18.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:18.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:18.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:18.394+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:37:18.415+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.826 seconds
[2025-07-18T11:37:49.690+0000] {processor.py:186} INFO - Started process (PID=3436) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:37:49.691+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:37:49.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.694+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:37:50.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.057+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:50.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:37:50.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:50.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.308+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:37:50.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.647 seconds
[2025-07-18T11:38:20.783+0000] {processor.py:186} INFO - Started process (PID=3589) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:38:20.785+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:38:20.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.787+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:38:21.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.160+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:21.170+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:38:21.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.420+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:21.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.429+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:38:21.450+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.673 seconds
[2025-07-18T11:38:51.524+0000] {processor.py:186} INFO - Started process (PID=3741) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:38:51.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:38:51.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.528+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:38:52.050+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.049+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:52.058+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:38:52.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.150+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:52.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.161+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:38:52.178+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.660 seconds
[2025-07-18T11:39:22.570+0000] {processor.py:186} INFO - Started process (PID=3895) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:39:22.572+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:39:22.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.574+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:39:23.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.138+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:23.145+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:39:23.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.246+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:23.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.258+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:39:23.280+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.717 seconds
[2025-07-18T11:39:53.520+0000] {processor.py:186} INFO - Started process (PID=4048) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:39:53.521+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:39:53.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.524+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:39:54.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.021+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:54.029+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:39:54.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.116+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:54.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.126+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:39:54.145+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.631 seconds
[2025-07-18T11:40:24.494+0000] {processor.py:186} INFO - Started process (PID=4201) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:40:24.495+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:40:24.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.497+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:40:25.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.011+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:25.018+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:40:25.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.111+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:25.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.122+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:40:25.143+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.656 seconds
[2025-07-18T11:40:55.787+0000] {processor.py:186} INFO - Started process (PID=4354) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:40:55.788+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:40:55.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.790+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:40:56.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.274+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:56.281+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:40:56.371+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.371+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:56.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:56.380+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:40:56.401+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.620 seconds
[2025-07-18T11:41:26.984+0000] {processor.py:186} INFO - Started process (PID=4513) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:41:26.985+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:41:26.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.988+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:41:27.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.499+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:27.506+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:41:27.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.615+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:27.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:27.627+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:41:27.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.670 seconds
[2025-07-18T11:41:57.783+0000] {processor.py:186} INFO - Started process (PID=4672) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:41:57.784+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:41:57.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.787+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:41:58.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.266+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:58.272+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:41:58.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.359+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:58.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:58.368+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:41:58.387+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.612 seconds
[2025-07-18T11:42:28.999+0000] {processor.py:186} INFO - Started process (PID=4837) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:42:29.000+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:42:29.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.003+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:42:29.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.474+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:29.481+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:42:29.568+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.568+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:29.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:29.578+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:42:29.597+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.605 seconds
[2025-07-18T11:42:59.769+0000] {processor.py:186} INFO - Started process (PID=4996) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:42:59.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:42:59.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:43:00.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.254+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:00.261+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:43:00.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.351+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:00.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:00.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:43:00.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.617 seconds
[2025-07-18T11:43:30.607+0000] {processor.py:186} INFO - Started process (PID=5155) to work on /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:43:30.609+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py for tasks to queue
[2025-07-18T11:43:30.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.612+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:43:31.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.108+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:31.116+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_describe_challenge_pipeline' retrieved from /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py
[2025-07-18T11:43:31.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.213+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:31.222+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.221+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_describe_challenge_pipeline to None, run_after=None
[2025-07-18T11:43:31.240+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_describe_challenge_pipeline.py took 0.643 seconds
