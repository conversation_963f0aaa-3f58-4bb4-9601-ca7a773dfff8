[2025-07-18T11:26:00.465+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:00.466+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:26:00.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.468+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:00.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.552+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:00.559+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:00.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.825+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.841+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.857+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.868+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.876+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.883+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.890+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.891+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:00.902+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:00.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.903+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.904+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:26:00.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.460 seconds
[2025-07-18T11:26:31.535+0000] {processor.py:186} INFO - Started process (PID=422) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:31.536+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:26:31.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.537+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:31.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.782+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:31.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:31.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.876+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:31.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.889+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:26:31.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.381 seconds
[2025-07-18T11:27:02.491+0000] {processor.py:186} INFO - Started process (PID=553) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:02.493+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:27:02.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.495+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:02.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.604+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.612+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:02.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.712+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.723+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:27:02.742+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.260 seconds
[2025-07-18T11:27:33.437+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:33.438+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:27:33.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.440+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:33.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.522+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:33.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:33.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.643+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:27:33.678+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.248 seconds
[2025-07-18T11:28:03.927+0000] {processor.py:186} INFO - Started process (PID=817) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:03.928+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:28:03.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.930+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:04.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.007+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:04.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:04.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:04.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.142+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:28:04.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.245 seconds
[2025-07-18T11:28:34.284+0000] {processor.py:186} INFO - Started process (PID=948) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:34.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:28:34.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:34.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.363+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.371+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:34.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.482+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.494+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:28:34.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.242 seconds
[2025-07-18T11:29:05.055+0000] {processor.py:186} INFO - Started process (PID=1077) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:05.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:29:05.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.058+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:05.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.133+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:05.142+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:05.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.240+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:05.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:29:05.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.218 seconds
[2025-07-18T11:29:36.029+0000] {processor.py:186} INFO - Started process (PID=1208) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:36.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:29:36.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:36.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.119+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:36.126+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:36.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.234+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:36.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.245+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:29:36.265+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.243 seconds
[2025-07-18T11:30:06.564+0000] {processor.py:186} INFO - Started process (PID=1341) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:06.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:30:06.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:06.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.641+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:06.649+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:06.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.750+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:06.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.760+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:30:06.780+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.223 seconds
[2025-07-18T11:30:37.559+0000] {processor.py:186} INFO - Started process (PID=1470) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:37.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:30:37.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:37.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.644+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.652+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:37.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.765+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.777+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:30:37.796+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.243 seconds
[2025-07-18T11:31:08.307+0000] {processor.py:186} INFO - Started process (PID=1603) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:08.308+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:31:08.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.309+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:08.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.381+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:08.390+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:08.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.491+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:08.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.503+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:31:08.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.223 seconds
