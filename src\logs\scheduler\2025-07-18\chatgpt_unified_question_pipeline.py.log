[2025-07-18T11:26:00.465+0000] {processor.py:186} INFO - Started process (PID=291) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:00.466+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:26:00.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.468+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:00.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.552+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:00.559+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:00.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.825+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.841+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.857+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.868+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.876+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.883+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.890+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.891+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:00.902+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:00.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.903+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_unified_question_pipeline
[2025-07-18T11:26:00.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.904+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:26:00.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.460 seconds
[2025-07-18T11:26:31.535+0000] {processor.py:186} INFO - Started process (PID=422) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:31.536+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:26:31.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.537+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:31.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.782+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:31.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:26:31.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.876+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:31.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.889+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:26:31.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.381 seconds
[2025-07-18T11:27:02.491+0000] {processor.py:186} INFO - Started process (PID=553) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:02.493+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:27:02.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.495+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:02.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.604+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.612+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:02.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.712+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.723+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:27:02.742+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.260 seconds
[2025-07-18T11:27:33.437+0000] {processor.py:186} INFO - Started process (PID=686) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:33.438+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:27:33.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.440+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:33.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.522+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:33.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:27:33.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.643+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:27:33.678+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.248 seconds
[2025-07-18T11:28:03.927+0000] {processor.py:186} INFO - Started process (PID=817) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:03.928+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:28:03.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.930+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:04.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.007+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:04.017+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:04.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.128+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:04.142+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.142+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:28:04.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.245 seconds
[2025-07-18T11:28:34.284+0000] {processor.py:186} INFO - Started process (PID=948) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:34.285+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:28:34.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.287+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:34.364+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.363+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.371+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:28:34.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.482+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.494+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:28:34.521+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.242 seconds
[2025-07-18T11:29:05.055+0000] {processor.py:186} INFO - Started process (PID=1077) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:05.056+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:29:05.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.058+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:05.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.133+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:05.142+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:05.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.240+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:05.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.249+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:29:05.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.218 seconds
[2025-07-18T11:29:36.029+0000] {processor.py:186} INFO - Started process (PID=1208) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:36.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:29:36.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:36.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.119+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:36.126+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:29:36.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.234+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:36.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.245+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:29:36.265+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.243 seconds
[2025-07-18T11:30:06.564+0000] {processor.py:186} INFO - Started process (PID=1341) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:06.565+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:30:06.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.567+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:06.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.641+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:06.649+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:06.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.750+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:06.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.760+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:30:06.780+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.223 seconds
[2025-07-18T11:30:37.559+0000] {processor.py:186} INFO - Started process (PID=1470) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:37.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:30:37.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:37.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.644+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.652+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:30:37.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.765+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.777+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:30:37.796+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.243 seconds
[2025-07-18T11:31:08.307+0000] {processor.py:186} INFO - Started process (PID=1603) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:08.308+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:31:08.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.309+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:08.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.381+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:08.390+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:08.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.491+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:08.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.503+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:31:08.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.223 seconds
[2025-07-18T11:31:38.724+0000] {processor.py:186} INFO - Started process (PID=1732) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:38.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:31:38.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:38.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.804+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:38.812+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:31:38.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.915+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:38.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.928+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:31:38.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.234 seconds
[2025-07-18T11:32:09.243+0000] {processor.py:186} INFO - Started process (PID=1863) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:32:09.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:32:09.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.246+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:32:09.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.324+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:09.332+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:32:09.444+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.444+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:09.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.457+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:32:09.481+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.244 seconds
[2025-07-18T11:32:39.732+0000] {processor.py:186} INFO - Started process (PID=1994) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:32:39.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:32:39.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.736+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:32:39.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.813+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:39.822+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:32:39.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.936+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:39.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.946+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:32:39.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.241 seconds
[2025-07-18T11:33:10.423+0000] {processor.py:186} INFO - Started process (PID=2125) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:33:10.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:33:10.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:33:10.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.501+0000] {cost_tracking.py:76} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:10.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:33:10.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.614+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:10.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.626+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:33:10.646+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.228 seconds
[2025-07-18T11:33:42.350+0000] {processor.py:186} INFO - Started process (PID=2275) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:33:42.351+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:33:42.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.354+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:33:42.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.738+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:42.748+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:33:42.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.873+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:42.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.895+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:33:42.918+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.574 seconds
[2025-07-18T11:34:13.879+0000] {processor.py:186} INFO - Started process (PID=2429) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:34:13.880+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:34:13.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.883+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:34:14.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.230+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:14.238+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:34:14.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.345+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:14.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.356+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:34:14.378+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.505 seconds
[2025-07-18T11:34:45.145+0000] {processor.py:186} INFO - Started process (PID=2582) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:34:45.147+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:34:45.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.150+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:34:45.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.518+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:45.526+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:34:45.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.637+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:45.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.648+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:34:45.667+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.529 seconds
[2025-07-18T11:35:16.786+0000] {processor.py:186} INFO - Started process (PID=2734) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:35:16.787+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:35:16.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.790+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:35:17.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.188+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:17.199+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:35:17.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.322+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:17.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.333+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:35:17.354+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.574 seconds
[2025-07-18T11:35:48.296+0000] {processor.py:186} INFO - Started process (PID=2888) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:35:48.297+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:35:48.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:35:48.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.676+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:48.685+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:35:48.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.806+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:48.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.821+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:35:48.841+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.552 seconds
[2025-07-18T11:36:19.518+0000] {processor.py:186} INFO - Started process (PID=3041) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:36:19.520+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:36:19.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.523+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:36:19.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.953+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:19.961+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:36:20.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:20.081+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:20.095+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:20.095+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:36:20.121+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.610 seconds
[2025-07-18T11:36:51.464+0000] {processor.py:186} INFO - Started process (PID=3194) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:36:51.465+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:36:51.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.469+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:36:51.887+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.887+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:51.897+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:36:52.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:52.022+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:52.037+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:52.036+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:36:52.220+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.765 seconds
[2025-07-18T11:37:22.774+0000] {processor.py:186} INFO - Started process (PID=3350) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:37:22.775+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:37:22.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.777+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:37:23.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:23.152+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:23.161+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:37:23.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:23.267+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:23.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:23.444+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:37:23.467+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.700 seconds
[2025-07-18T11:37:54.079+0000] {processor.py:186} INFO - Started process (PID=3503) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:37:54.080+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:37:54.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:54.083+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:37:54.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:54.468+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:54.477+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:37:54.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:54.719+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:54.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:54.732+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:37:54.756+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.684 seconds
[2025-07-18T11:38:25.443+0000] {processor.py:186} INFO - Started process (PID=3656) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:38:25.444+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:38:25.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:25.447+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:38:25.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:25.784+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:25.795+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:38:26.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.016+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:26.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.026+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:38:26.046+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.610 seconds
[2025-07-18T11:38:56.261+0000] {processor.py:186} INFO - Started process (PID=3809) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:38:56.262+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:38:56.265+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.265+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:38:56.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.775+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:56.782+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:38:56.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.879+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:56.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.890+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:38:56.908+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.653 seconds
[2025-07-18T11:39:27.560+0000] {processor.py:186} INFO - Started process (PID=3968) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:39:27.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:39:27.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:27.564+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:39:28.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.051+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:28.059+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:39:28.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.148+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:28.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.157+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:39:28.176+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.622 seconds
[2025-07-18T11:39:58.574+0000] {processor.py:186} INFO - Started process (PID=4126) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:39:58.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:39:58.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:58.579+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:39:59.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.040+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:59.047+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:39:59.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.136+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:59.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:39:59.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.597 seconds
[2025-07-18T11:40:29.666+0000] {processor.py:186} INFO - Started process (PID=4279) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:40:29.668+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:40:29.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:29.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:40:30.166+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:30.166+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:30.173+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:40:30.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:30.288+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:30.298+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:30.298+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:40:30.320+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.659 seconds
[2025-07-18T11:41:00.469+0000] {processor.py:186} INFO - Started process (PID=4436) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:41:00.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:41:00.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:00.473+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:41:00.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:00.956+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:00.964+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:41:01.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:01.059+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:01.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:01.067+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:41:01.090+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.629 seconds
[2025-07-18T11:41:31.444+0000] {processor.py:186} INFO - Started process (PID=4597) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:41:31.445+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:41:31.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:31.448+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:41:31.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:31.952+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:31.962+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:41:32.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:32.058+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:32.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:32.067+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:41:32.088+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.650 seconds
[2025-07-18T11:42:02.440+0000] {processor.py:186} INFO - Started process (PID=4754) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:42:02.441+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:42:02.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:02.443+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:42:02.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:02.901+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:02.909+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:42:03.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:03.005+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:03.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:03.015+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:42:03.032+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.599 seconds
[2025-07-18T11:42:33.213+0000] {processor.py:186} INFO - Started process (PID=4913) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:42:33.214+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:42:33.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:33.217+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:42:33.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:33.712+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:33.719+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:42:33.813+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:33.812+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:33.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:33.822+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:42:33.841+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.635 seconds
[2025-07-18T11:43:04.064+0000] {processor.py:186} INFO - Started process (PID=5072) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:43:04.065+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:43:04.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:04.067+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:43:04.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:04.524+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:04.532+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:43:04.620+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:04.620+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:04.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:04.630+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:43:04.648+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.590 seconds
[2025-07-18T11:43:34.952+0000] {processor.py:186} INFO - Started process (PID=5231) to work on /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:43:34.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_unified_question_pipeline.py for tasks to queue
[2025-07-18T11:43:34.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:43:35.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:35.427+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:35.433+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_unified_question_pipeline' retrieved from /opt/airflow/dags/chatgpt_unified_question_pipeline.py
[2025-07-18T11:43:35.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:35.521+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:35.531+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:35.531+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_unified_question_pipeline to None, run_after=None
[2025-07-18T11:43:35.549+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_unified_question_pipeline.py took 0.603 seconds
