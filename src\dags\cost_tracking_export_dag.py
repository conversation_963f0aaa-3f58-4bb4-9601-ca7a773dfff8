"""
DAG для автоматического экспорта данных cost_tracking в Google Sheets
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.decorators import task
from airflow.models import Variable
import logging

from cost_tracking import export_and_upload_cost_data

default_args = {
    'owner': 'Artur An',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 1,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'cost_tracking_export',
    default_args=default_args,
    description='Автоматический экспорт данных cost_tracking в Google Sheets',
    schedule_interval='0 9 * * *',  # Ежедневно в 9:00
    catchup=False,
    tags=['cost_tracking', 'export', 'google_sheets']
)

@task(dag=dag)
def export_cost_tracking_data(**context):
    """
    Экспортирует данные cost_tracking в Google Sheets
    """
    logger = logging.getLogger(__name__)
    
    try:
        # Используем настроенные значения по умолчанию
        spreadsheet_id = Variable.get("COST_TRACKING_SPREADSHEET_ID", default_var="1u39n_ERZzDQRpq1MWdgkKH9g8eQm15yE-eVX3jligvk")
        credentials_path = Variable.get("GOOGLE_SHEETS_CREDENTIALS_PATH", default_var="/src/secrets/google_sheet.json")
        sheet_name = Variable.get("COST_TRACKING_SHEET_NAME", default_var="BiomCost")
        export_days = int(Variable.get("COST_TRACKING_EXPORT_DAYS", default_var="30"))
        
        logger.info(f"Начинаем экспорт данных за {export_days} дней")
        logger.info(f"Spreadsheet ID: {spreadsheet_id}")
        logger.info(f"Sheet name: {sheet_name}")
        
        success = export_and_upload_cost_data(
            spreadsheet_id=spreadsheet_id,
            sheet_name=sheet_name,
            days=export_days,
            credentials_file=credentials_path,
            temp_csv_file=f"temp_cost_export_{context['ds']}.csv",
            redis_host="redis",  
            redis_port=6379,
            redis_db=0
        )
        
        if success:
            logger.info("✅ Экспорт данных cost_tracking завершен успешно")
            return {
                "status": "success",
                "message": f"Данные за {export_days} дней экспортированы в Google Sheets",
                "spreadsheet_id": spreadsheet_id,
                "sheet_name": sheet_name,
                "export_date": context['ds']
            }
        else:
            raise Exception("Ошибка при экспорте данных")
            
    except Exception as e:
        logger.error(f"❌ Ошибка экспорта данных cost_tracking: {e}")
        raise

@task(dag=dag)
def send_notification(export_result, **context):
    """
    Отправляет уведомление о результате экспорта
    """
    logger = logging.getLogger(__name__)
    
    if export_result["status"] == "success":
        logger.info(f"📊 Отчет по затратам обновлен: {export_result['message']}")
        # Здесь можно добавить отправку уведомлений в Slack, Telegram и т.д.
    else:
        logger.error(f"❌ Ошибка в экспорте отчета: {export_result}")

# Определяем последовательность выполнения задач
export_result = export_cost_tracking_data()
send_notification(export_result)
