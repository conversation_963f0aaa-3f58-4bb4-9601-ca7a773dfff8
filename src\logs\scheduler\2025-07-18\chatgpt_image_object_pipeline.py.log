[2025-07-18T11:25:58.798+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:25:58.799+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:25:58.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:25:58.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.897+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:58.905+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:25:59.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.151+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.162+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.170+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.180+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.188+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.196+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.206+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.207+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:59.217+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:59.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.218+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_image_object_pipeline
[2025-07-18T11:25:59.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:25:59.239+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.447 seconds
[2025-07-18T11:26:29.844+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:26:29.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:26:29.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:26:29.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.928+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.081+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:26:30.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.177+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:26:30.204+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.367 seconds
[2025-07-18T11:27:01.398+0000] {processor.py:186} INFO - Started process (PID=515) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:01.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:27:01.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:01.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.623+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.632+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:01.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.730+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:01.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:27:01.763+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.370 seconds
[2025-07-18T11:27:31.973+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:31.974+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:27:31.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.976+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:32.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.057+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.067+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:32.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.175+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.188+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:27:32.210+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
[2025-07-18T11:28:02.286+0000] {processor.py:186} INFO - Started process (PID=775) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:02.286+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:28:02.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:02.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.360+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:02.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.468+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.480+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:28:02.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.220 seconds
[2025-07-18T11:28:32.844+0000] {processor.py:186} INFO - Started process (PID=906) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:32.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:28:32.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.923+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.931+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:33.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.038+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.051+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:28:33.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.233 seconds
[2025-07-18T11:29:03.142+0000] {processor.py:186} INFO - Started process (PID=1037) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:03.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:29:03.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:03.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.214+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:03.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:03.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:03.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.331+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:29:03.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.214 seconds
[2025-07-18T11:29:33.912+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:33.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:29:33.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.916+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:33.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.990+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.999+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:34.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.101+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:34.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.112+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:29:34.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.226 seconds
[2025-07-18T11:30:05.050+0000] {processor.py:186} INFO - Started process (PID=1299) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:05.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:30:05.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.053+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:05.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.134+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.143+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:05.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.257+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.269+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:30:05.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.247 seconds
[2025-07-18T11:30:36.200+0000] {processor.py:186} INFO - Started process (PID=1430) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:36.201+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:30:36.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:36.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.273+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.282+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:36.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.399+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:30:36.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.227 seconds
[2025-07-18T11:31:06.842+0000] {processor.py:186} INFO - Started process (PID=1563) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:06.843+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:31:06.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.845+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:06.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.918+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:07.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:31:07.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.227 seconds
