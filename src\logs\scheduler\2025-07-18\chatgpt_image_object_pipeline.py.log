[2025-07-18T11:25:58.798+0000] {processor.py:186} INFO - Started process (PID=251) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:25:58.799+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:25:58.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:25:58.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.897+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:58.905+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:25:59.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.151+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.162+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.170+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.180+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.188+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.196+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.206+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_image_object_pipeline
[2025-07-18T11:25:59.207+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.207+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:59.217+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:59.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.218+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_image_object_pipeline
[2025-07-18T11:25:59.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:25:59.239+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.447 seconds
[2025-07-18T11:26:29.844+0000] {processor.py:186} INFO - Started process (PID=382) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:26:29.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:26:29.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:26:29.928+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.928+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.081+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:26:30.177+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.177+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:26:30.204+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.367 seconds
[2025-07-18T11:27:01.398+0000] {processor.py:186} INFO - Started process (PID=515) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:01.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:27:01.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:01.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.623+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.632+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:01.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.730+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:01.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.741+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:27:01.763+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.370 seconds
[2025-07-18T11:27:31.973+0000] {processor.py:186} INFO - Started process (PID=646) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:31.974+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:27:31.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.976+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:32.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.057+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.067+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:27:32.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.175+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.188+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:27:32.210+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.242 seconds
[2025-07-18T11:28:02.286+0000] {processor.py:186} INFO - Started process (PID=775) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:02.286+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:28:02.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:02.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.360+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:02.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.468+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.480+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:28:02.498+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.220 seconds
[2025-07-18T11:28:32.844+0000] {processor.py:186} INFO - Started process (PID=906) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:32.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:28:32.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.847+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:32.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.923+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.931+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:28:33.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.038+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.051+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:28:33.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.233 seconds
[2025-07-18T11:29:03.142+0000] {processor.py:186} INFO - Started process (PID=1037) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:03.143+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:29:03.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:03.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.214+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:03.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:03.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:03.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.331+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:29:03.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.214 seconds
[2025-07-18T11:29:33.912+0000] {processor.py:186} INFO - Started process (PID=1170) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:33.913+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:29:33.916+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.916+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:33.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.990+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.999+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:29:34.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.101+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:34.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.112+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:29:34.133+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.226 seconds
[2025-07-18T11:30:05.050+0000] {processor.py:186} INFO - Started process (PID=1299) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:05.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:30:05.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.053+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:05.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.134+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.143+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:05.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.257+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.269+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:30:05.291+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.247 seconds
[2025-07-18T11:30:36.200+0000] {processor.py:186} INFO - Started process (PID=1430) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:36.201+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:30:36.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.203+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:36.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.273+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.282+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:30:36.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.384+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.399+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:30:36.421+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.227 seconds
[2025-07-18T11:31:06.842+0000] {processor.py:186} INFO - Started process (PID=1563) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:06.843+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:31:06.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.845+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:06.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.918+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.926+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:07.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.031+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.042+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:31:07.063+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.227 seconds
[2025-07-18T11:31:37.560+0000] {processor.py:186} INFO - Started process (PID=1694) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:37.561+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:31:37.564+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.563+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:37.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.643+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:37.656+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:31:37.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.773+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:37.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.785+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:31:37.806+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.253 seconds
[2025-07-18T11:32:08.011+0000] {processor.py:186} INFO - Started process (PID=1823) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:32:08.013+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:32:08.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:32:08.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.104+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:08.113+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:32:08.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:08.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:32:08.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.250 seconds
[2025-07-18T11:32:38.683+0000] {processor.py:186} INFO - Started process (PID=1956) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:32:38.684+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:32:38.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.686+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:32:38.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.762+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:38.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:32:38.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.878+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:38.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.890+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:32:38.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.233 seconds
[2025-07-18T11:33:09.198+0000] {processor.py:186} INFO - Started process (PID=2087) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:33:09.199+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:33:09.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.201+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:33:09.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.274+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:09.285+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:33:09.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.429+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:09.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:33:09.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.267 seconds
[2025-07-18T11:33:40.371+0000] {processor.py:186} INFO - Started process (PID=2229) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:33:40.372+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:33:40.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:33:40.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.742+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:40.753+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:33:40.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.865+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:40.876+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.876+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:33:40.895+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.531 seconds
[2025-07-18T11:34:11.731+0000] {processor.py:186} INFO - Started process (PID=2382) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:34:11.732+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:34:11.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:11.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:34:12.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.126+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:12.134+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:34:12.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.245+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:12.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.257+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:34:12.279+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.555 seconds
[2025-07-18T11:34:43.104+0000] {processor.py:186} INFO - Started process (PID=2535) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:34:43.106+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:34:43.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:34:43.480+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.480+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:43.490+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:34:43.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.590+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:43.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.600+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:34:43.621+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.524 seconds
[2025-07-18T11:35:14.534+0000] {processor.py:186} INFO - Started process (PID=2688) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:35:14.535+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:35:14.539+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.538+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:35:14.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.971+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:14.980+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:35:15.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.155+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:15.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:35:15.220+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.694 seconds
[2025-07-18T11:35:45.900+0000] {processor.py:186} INFO - Started process (PID=2841) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:35:45.901+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:35:45.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:45.904+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:35:46.318+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.318+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:46.331+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:35:46.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.472+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:46.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.490+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:35:46.512+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.619 seconds
[2025-07-18T11:36:16.913+0000] {processor.py:186} INFO - Started process (PID=2994) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:36:16.914+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:36:16.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:16.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:36:17.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.352+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:17.360+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:36:17.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.637+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:17.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.648+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:36:17.671+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.766 seconds
[2025-07-18T11:36:47.955+0000] {processor.py:186} INFO - Started process (PID=3146) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:36:47.956+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:36:47.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:47.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:36:48.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:48.344+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:48.352+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:36:48.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:48.677+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:48.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:48.692+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:36:48.720+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.772 seconds
[2025-07-18T11:37:18.996+0000] {processor.py:186} INFO - Started process (PID=3300) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:37:18.997+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:37:19.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:19.000+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:37:19.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:19.388+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:19.397+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:37:19.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:19.661+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:19.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:19.671+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:37:19.692+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.702 seconds
[2025-07-18T11:37:50.890+0000] {processor.py:186} INFO - Started process (PID=3454) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:37:50.891+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:37:50.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:50.893+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:37:51.315+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.315+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:51.322+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:37:51.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.586+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:51.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.595+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:37:51.617+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.734 seconds
[2025-07-18T11:38:21.847+0000] {processor.py:186} INFO - Started process (PID=3607) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:38:21.848+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:38:21.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:21.851+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:38:22.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.213+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:22.222+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:38:22.465+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.464+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:22.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.475+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:38:22.496+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.655 seconds
[2025-07-18T11:38:52.820+0000] {processor.py:186} INFO - Started process (PID=3759) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:38:52.821+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:38:52.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:52.823+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:38:53.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:53.333+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:53.342+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:38:53.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:53.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:53.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:53.450+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:38:53.471+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.657 seconds
[2025-07-18T11:39:23.913+0000] {processor.py:186} INFO - Started process (PID=3912) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:39:23.914+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:39:23.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:23.917+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:39:24.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:24.527+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:24.534+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:39:24.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:24.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:24.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:24.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:39:24.678+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.771 seconds
[2025-07-18T11:39:54.871+0000] {processor.py:186} INFO - Started process (PID=4065) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:39:54.872+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:39:54.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:54.874+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:39:55.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:55.404+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:55.413+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:39:55.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:55.511+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:55.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:55.522+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:39:55.541+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.676 seconds
[2025-07-18T11:40:25.895+0000] {processor.py:186} INFO - Started process (PID=4218) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:40:25.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:40:25.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:25.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:40:26.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:26.421+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:26.429+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:40:26.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:26.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:26.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:26.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:40:26.559+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.671 seconds
[2025-07-18T11:40:57.605+0000] {processor.py:186} INFO - Started process (PID=4378) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:40:57.606+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:40:57.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:40:58.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.102+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:58.111+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:40:58.210+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:58.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.220+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:40:58.241+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.643 seconds
[2025-07-18T11:41:28.394+0000] {processor.py:186} INFO - Started process (PID=4543) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:41:28.395+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:41:28.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.397+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:41:28.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.908+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:28.919+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:41:29.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.014+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:29.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.024+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:41:29.045+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.659 seconds
[2025-07-18T11:41:59.491+0000] {processor.py:186} INFO - Started process (PID=4704) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:41:59.492+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:41:59.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:59.495+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:42:00.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.015+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:00.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:42:00.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.121+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:00.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.132+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:42:00.153+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.667 seconds
[2025-07-18T11:42:30.331+0000] {processor.py:186} INFO - Started process (PID=4861) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:42:30.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:42:30.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.335+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:42:30.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.839+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:30.847+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:42:30.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:30.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.947+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:42:30.965+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.640 seconds
[2025-07-18T11:43:01.188+0000] {processor.py:186} INFO - Started process (PID=5020) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:43:01.190+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:43:01.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.192+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:43:01.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.728+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:01.735+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:43:01.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.887+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:01.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.897+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:43:01.913+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.732 seconds
[2025-07-18T11:43:31.992+0000] {processor.py:186} INFO - Started process (PID=5179) to work on /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:43:31.994+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_object_pipeline.py for tasks to queue
[2025-07-18T11:43:31.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:31.997+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:43:32.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.484+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:32.491+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_object_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_object_pipeline.py
[2025-07-18T11:43:32.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.586+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:32.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.597+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_object_pipeline to None, run_after=None
[2025-07-18T11:43:32.619+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_object_pipeline.py took 0.635 seconds
