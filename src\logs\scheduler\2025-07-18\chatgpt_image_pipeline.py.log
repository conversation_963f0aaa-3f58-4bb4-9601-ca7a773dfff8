[2025-07-18T11:25:58.936+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:25:58.937+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:25:58.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.939+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:25:59.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.020+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:59.030+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:25:59.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.279+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_image_pipeline
[2025-07-18T11:25:59.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.289+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_image_pipeline
[2025-07-18T11:25:59.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.296+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_image_pipeline
[2025-07-18T11:25:59.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.304+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_image_pipeline
[2025-07-18T11:25:59.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.310+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_image_pipeline
[2025-07-18T11:25:59.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.315+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_image_pipeline
[2025-07-18T11:25:59.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.322+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_image_pipeline
[2025-07-18T11:25:59.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.323+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:59.334+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:59.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.335+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_image_pipeline
[2025-07-18T11:25:59.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.335+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:25:59.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.422 seconds
[2025-07-18T11:26:30.176+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:26:30.177+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:26:30.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.180+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:26:30.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.385+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:26:30.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.491+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.501+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:26:30.524+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.353 seconds
[2025-07-18T11:27:01.561+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:01.562+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:27:01.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.565+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:01.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.656+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.665+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:01.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.790+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:01.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.801+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:27:01.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.271 seconds
[2025-07-18T11:27:31.980+0000] {processor.py:186} INFO - Started process (PID=649) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:31.981+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:27:31.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.983+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:32.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.069+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.079+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:32.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.188+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.202+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:27:32.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.248 seconds
[2025-07-18T11:28:02.546+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:02.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:28:02.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:02.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.621+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.629+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:02.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.721+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.731+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:28:02.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.210 seconds
[2025-07-18T11:28:33.112+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:33.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:28:33.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.114+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:33.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.198+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:33.208+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:33.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.309+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.321+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:28:33.342+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.236 seconds
[2025-07-18T11:29:03.392+0000] {processor.py:186} INFO - Started process (PID=1044) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:03.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:29:03.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.397+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:03.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.474+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:03.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:03.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.579+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:03.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.588+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:29:03.607+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.222 seconds
[2025-07-18T11:29:33.931+0000] {processor.py:186} INFO - Started process (PID=1173) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:33.932+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:29:33.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.934+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:34.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.014+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:34.023+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:34.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.126+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:34.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.137+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:29:34.156+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.232 seconds
[2025-07-18T11:30:05.322+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:05.323+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:30:05.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.325+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:05.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.411+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.420+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:05.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.534+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:30:05.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.240 seconds
[2025-07-18T11:30:36.456+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:36.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:30:36.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:36.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.532+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.542+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:36.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.640+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.650+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:30:36.670+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.220 seconds
[2025-07-18T11:31:07.114+0000] {processor.py:186} INFO - Started process (PID=1568) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:07.115+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:31:07.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:07.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.195+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.205+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:07.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.302+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.311+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:31:07.330+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.222 seconds
