[2025-07-18T11:25:58.936+0000] {processor.py:186} INFO - Started process (PID=256) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:25:58.937+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:25:58.940+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:58.939+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:25:59.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.020+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:59.030+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:25:59.279+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.279+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_image_pipeline
[2025-07-18T11:25:59.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.289+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_image_pipeline
[2025-07-18T11:25:59.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.296+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_image_pipeline
[2025-07-18T11:25:59.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.304+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_image_pipeline
[2025-07-18T11:25:59.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.310+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_image_pipeline
[2025-07-18T11:25:59.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.315+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_image_pipeline
[2025-07-18T11:25:59.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.322+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_image_pipeline
[2025-07-18T11:25:59.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.323+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:59.334+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:59.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.335+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_image_pipeline
[2025-07-18T11:25:59.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.335+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:25:59.351+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.422 seconds
[2025-07-18T11:26:30.176+0000] {processor.py:186} INFO - Started process (PID=387) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:26:30.177+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:26:30.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.180+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:26:30.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.385+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:26:30.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.491+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.501+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:26:30.524+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.353 seconds
[2025-07-18T11:27:01.561+0000] {processor.py:186} INFO - Started process (PID=518) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:01.562+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:27:01.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.565+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:01.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.656+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.665+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:01.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.790+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:01.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.801+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:27:01.825+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.271 seconds
[2025-07-18T11:27:31.980+0000] {processor.py:186} INFO - Started process (PID=649) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:31.981+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:27:31.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.983+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:32.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.069+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.079+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:27:32.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.188+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.202+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.202+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:27:32.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.248 seconds
[2025-07-18T11:28:02.546+0000] {processor.py:186} INFO - Started process (PID=782) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:02.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:28:02.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.549+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:02.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.621+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.629+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:02.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.721+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.731+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:28:02.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.210 seconds
[2025-07-18T11:28:33.112+0000] {processor.py:186} INFO - Started process (PID=913) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:33.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:28:33.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.114+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:33.198+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.198+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:33.208+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:28:33.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.309+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.321+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:28:33.342+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.236 seconds
[2025-07-18T11:29:03.392+0000] {processor.py:186} INFO - Started process (PID=1044) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:03.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:29:03.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.397+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:03.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.474+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:03.483+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:03.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.579+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:03.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:03.588+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:29:03.607+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.222 seconds
[2025-07-18T11:29:33.931+0000] {processor.py:186} INFO - Started process (PID=1173) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:33.932+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:29:33.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.934+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:34.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.014+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:34.023+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:29:34.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.126+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:34.138+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:34.137+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:29:34.156+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.232 seconds
[2025-07-18T11:30:05.322+0000] {processor.py:186} INFO - Started process (PID=1306) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:05.323+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:30:05.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.325+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:05.412+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.411+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.420+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:05.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.534+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:30:05.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.240 seconds
[2025-07-18T11:30:36.456+0000] {processor.py:186} INFO - Started process (PID=1437) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:36.457+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:30:36.460+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.459+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:36.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.532+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.542+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:30:36.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.640+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.650+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:30:36.670+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.220 seconds
[2025-07-18T11:31:07.114+0000] {processor.py:186} INFO - Started process (PID=1568) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:07.115+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:31:07.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:07.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.195+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.205+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:07.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.302+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.311+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:31:07.330+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.222 seconds
[2025-07-18T11:31:37.569+0000] {processor.py:186} INFO - Started process (PID=1697) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:37.570+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:31:37.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.573+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:37.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.653+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:37.662+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:31:37.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.779+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:37.792+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.791+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:31:37.812+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.250 seconds
[2025-07-18T11:32:08.043+0000] {processor.py:186} INFO - Started process (PID=1828) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:32:08.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:32:08.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.048+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:32:08.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.136+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:08.145+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:32:08.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.258+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:08.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.273+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:32:08.293+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.256 seconds
[2025-07-18T11:32:38.691+0000] {processor.py:186} INFO - Started process (PID=1959) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:32:38.692+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:32:38.695+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.695+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:32:38.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.768+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:38.779+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:32:38.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.878+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:38.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.890+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:32:38.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.224 seconds
[2025-07-18T11:33:09.208+0000] {processor.py:186} INFO - Started process (PID=2090) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:33:09.209+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:33:09.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.211+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:33:09.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.294+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:09.304+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:33:09.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.441+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:09.453+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.452+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:33:09.473+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.272 seconds
[2025-07-18T11:33:40.541+0000] {processor.py:186} INFO - Started process (PID=2235) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:33:40.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:33:40.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.545+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:33:40.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.934+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:40.942+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:33:41.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.074+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:41.092+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.092+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:33:41.118+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.583 seconds
[2025-07-18T11:34:12.121+0000] {processor.py:186} INFO - Started process (PID=2388) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:34:12.122+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:34:12.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.125+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:34:12.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.490+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:12.498+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:34:12.601+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.600+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:12.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.611+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:34:12.632+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.519 seconds
[2025-07-18T11:34:43.405+0000] {processor.py:186} INFO - Started process (PID=2541) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:34:43.406+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:34:43.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.408+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:34:43.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.794+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:43.803+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:34:43.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.905+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:43.917+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.916+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:34:43.940+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.542 seconds
[2025-07-18T11:35:14.771+0000] {processor.py:186} INFO - Started process (PID=2694) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:35:14.773+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:35:14.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:14.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:35:15.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.303+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:15.312+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:35:15.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.427+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:15.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:35:15.462+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.699 seconds
[2025-07-18T11:35:46.422+0000] {processor.py:186} INFO - Started process (PID=2847) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:35:46.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:35:46.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.428+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:35:46.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.823+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:46.831+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:35:46.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.932+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:46.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.943+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:35:46.966+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.554 seconds
[2025-07-18T11:36:17.408+0000] {processor.py:186} INFO - Started process (PID=3000) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:36:17.410+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:36:17.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.413+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:36:17.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.835+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:17.843+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:36:17.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.945+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:17.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.959+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:36:17.979+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.578 seconds
[2025-07-18T11:36:48.791+0000] {processor.py:186} INFO - Started process (PID=3155) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:36:48.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:36:48.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:48.797+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:36:49.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:49.194+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:49.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:36:49.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:49.315+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:49.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:49.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:36:49.354+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.574 seconds
[2025-07-18T11:37:19.735+0000] {processor.py:186} INFO - Started process (PID=3309) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:37:19.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:37:19.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:19.739+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:37:20.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:20.078+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:20.087+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:37:20.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:20.398+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:20.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:20.407+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:37:20.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.697 seconds
[2025-07-18T11:37:51.155+0000] {processor.py:186} INFO - Started process (PID=3460) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:37:51.156+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:37:51.159+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.159+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:37:51.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.557+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:51.573+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:37:51.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.857+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:51.870+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.870+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:37:51.892+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.745 seconds
[2025-07-18T11:38:22.258+0000] {processor.py:186} INFO - Started process (PID=3613) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:38:22.259+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:38:22.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.262+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:38:22.633+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.632+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:22.642+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:38:22.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.865+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:22.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:38:22.898+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.646 seconds
[2025-07-18T11:38:53.524+0000] {processor.py:186} INFO - Started process (PID=3768) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:38:53.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:38:53.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:53.528+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:38:53.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:53.990+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:53.998+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:38:54.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.087+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:54.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.098+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:38:54.116+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.598 seconds
[2025-07-18T11:39:24.705+0000] {processor.py:186} INFO - Started process (PID=3921) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:39:24.706+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:39:24.710+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:24.710+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:39:25.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:25.189+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:25.197+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:39:25.285+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:25.285+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:25.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:25.295+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:39:25.313+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.615 seconds
[2025-07-18T11:39:55.575+0000] {processor.py:186} INFO - Started process (PID=4074) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:39:55.576+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:39:55.579+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:55.579+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:39:56.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.040+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:56.048+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:39:56.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.143+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:56.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.152+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:39:56.173+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.604 seconds
[2025-07-18T11:40:26.604+0000] {processor.py:186} INFO - Started process (PID=4227) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:40:26.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:40:26.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:26.607+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:40:27.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.102+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:27.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:40:27.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:27.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.227+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:40:27.246+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.648 seconds
[2025-07-18T11:40:57.679+0000] {processor.py:186} INFO - Started process (PID=4383) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:40:57.680+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:40:57.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:57.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:40:58.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.157+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:58.165+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:40:58.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:58.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.271+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:40:58.287+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.615 seconds
[2025-07-18T11:41:28.775+0000] {processor.py:186} INFO - Started process (PID=4549) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:41:28.776+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:41:28.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:28.778+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:41:29.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.275+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:29.284+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:41:29.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.381+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:29.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.390+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:41:29.409+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.640 seconds
[2025-07-18T11:41:59.503+0000] {processor.py:186} INFO - Started process (PID=4707) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:41:59.504+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:41:59.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:59.507+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:42:00.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.020+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:00.027+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:42:00.128+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.127+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:00.139+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.139+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:42:00.158+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.662 seconds
[2025-07-18T11:42:30.651+0000] {processor.py:186} INFO - Started process (PID=4867) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:42:30.653+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:42:30.656+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:30.655+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:42:31.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.148+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:31.156+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:42:31.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.247+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:31.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.256+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:42:31.277+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.632 seconds
[2025-07-18T11:43:01.574+0000] {processor.py:186} INFO - Started process (PID=5026) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:43:01.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:43:01.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.577+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:43:02.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.121+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:02.128+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:43:02.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.222+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:02.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.231+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:43:02.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.682 seconds
[2025-07-18T11:43:32.326+0000] {processor.py:186} INFO - Started process (PID=5185) to work on /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:43:32.328+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_image_pipeline.py for tasks to queue
[2025-07-18T11:43:32.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.331+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:43:32.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.815+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:32.822+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_image_pipeline' retrieved from /opt/airflow/dags/chatgpt_image_pipeline.py
[2025-07-18T11:43:32.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:32.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.921+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_image_pipeline to None, run_after=None
[2025-07-18T11:43:32.942+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_image_pipeline.py took 0.622 seconds
