[2025-07-18T11:25:55.736+0000] {processor.py:186} INFO - Started process (PID=188) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:25:55.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:25:55.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:55.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:25:55.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:55.843+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:55.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:25:56.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.067+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.081+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.090+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.100+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.108+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.117+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.125+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.126+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:56.141+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:56.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.142+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:25:56.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.437 seconds
[2025-07-18T11:26:27.007+0000] {processor.py:186} INFO - Started process (PID=319) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:27.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:26:27.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.011+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:27.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.107+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:27.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:27.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.432+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:27.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.445+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:26:27.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.468 seconds
[2025-07-18T11:26:57.888+0000] {processor.py:186} INFO - Started process (PID=450) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:57.889+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:26:57.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:57.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:58.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.159+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:58.169+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:58.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:58.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.307+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:26:58.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.451 seconds
[2025-07-18T11:27:28.730+0000] {processor.py:186} INFO - Started process (PID=581) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:28.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:27:28.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:28.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.829+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:28.838+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:28.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.953+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:28.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.965+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:27:28.985+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.263 seconds
[2025-07-18T11:27:59.549+0000] {processor.py:186} INFO - Started process (PID=712) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:59.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:27:59.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.553+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:59.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.637+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:59.649+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:59.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.775+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:59.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:27:59.809+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.265 seconds
[2025-07-18T11:28:30.000+0000] {processor.py:186} INFO - Started process (PID=843) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:28:30.001+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:28:30.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:28:30.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.086+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.096+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:28:30.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.216+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.226+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:28:30.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.259 seconds
[2025-07-18T11:29:00.405+0000] {processor.py:186} INFO - Started process (PID=974) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:00.406+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:29:00.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.409+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:00.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.489+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:00.500+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:00.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.612+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:00.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.625+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:29:00.646+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.248 seconds
[2025-07-18T11:29:31.092+0000] {processor.py:186} INFO - Started process (PID=1105) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:31.093+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:29:31.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.097+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:31.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.179+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.191+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:31.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.300+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.311+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:29:31.334+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.248 seconds
[2025-07-18T11:30:02.071+0000] {processor.py:186} INFO - Started process (PID=1236) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:02.072+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:30:02.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:02.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.164+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.176+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:02.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.314+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:30:02.337+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.272 seconds
[2025-07-18T11:30:32.625+0000] {processor.py:186} INFO - Started process (PID=1367) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:32.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:30:32.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.628+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:32.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:32.748+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:32.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:32.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.880+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:30:32.902+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.284 seconds
[2025-07-18T11:31:03.120+0000] {processor.py:186} INFO - Started process (PID=1498) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:03.121+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:31:03.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.125+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:03.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.249+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:03.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:03.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:03.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.431+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:31:03.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.352 seconds
[2025-07-18T11:31:34.352+0000] {processor.py:186} INFO - Started process (PID=1629) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:34.354+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:31:34.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.356+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:34.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.439+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:34.451+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:34.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.592+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:34.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:31:34.630+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.283 seconds
[2025-07-18T11:32:04.996+0000] {processor.py:186} INFO - Started process (PID=1760) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:32:04.997+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:32:05.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:04.999+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:32:05.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.085+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:05.095+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:32:05.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.209+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:05.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:32:05.245+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.256 seconds
[2025-07-18T11:32:35.473+0000] {processor.py:186} INFO - Started process (PID=1891) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:32:35.473+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:32:35.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.475+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:32:35.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.553+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:35.564+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:32:35.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.678+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:35.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.691+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:32:35.713+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.246 seconds
[2025-07-18T11:33:05.919+0000] {processor.py:186} INFO - Started process (PID=2022) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:33:05.920+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:33:05.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:05.922+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:33:06.000+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.000+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:06.010+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:33:06.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.135+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:06.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.146+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:33:06.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.254 seconds
[2025-07-18T11:33:36.503+0000] {processor.py:186} INFO - Started process (PID=2154) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:33:36.504+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:33:36.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:36.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:33:36.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:36.937+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:36.949+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:33:37.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.065+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:37.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.078+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:33:37.100+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.604 seconds
[2025-07-18T11:34:07.183+0000] {processor.py:186} INFO - Started process (PID=2307) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:34:07.184+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:34:07.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.188+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:34:07.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.618+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:07.628+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:34:07.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.744+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:07.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.757+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:34:07.780+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.606 seconds
[2025-07-18T11:34:38.416+0000] {processor.py:186} INFO - Started process (PID=2460) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:34:38.418+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:34:38.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:38.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:34:38.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:38.872+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:38.881+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:34:38.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:38.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:39.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.007+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:34:39.033+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.625 seconds
[2025-07-18T11:35:09.690+0000] {processor.py:186} INFO - Started process (PID=2613) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:35:09.691+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:35:09.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:09.693+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:35:10.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.047+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:10.057+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:35:10.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.172+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:10.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:35:10.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.689 seconds
[2025-07-18T11:35:40.531+0000] {processor.py:186} INFO - Started process (PID=2766) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:35:40.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:35:40.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:40.536+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:35:40.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:40.933+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:40.942+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:35:41.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.080+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:41.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.269+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:35:41.293+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.770 seconds
[2025-07-18T11:36:11.831+0000] {processor.py:186} INFO - Started process (PID=2919) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:36:11.832+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:36:11.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:11.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:36:12.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.229+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:12.241+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:36:12.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.368+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:12.567+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.567+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:36:12.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.762 seconds
[2025-07-18T11:36:42.750+0000] {processor.py:186} INFO - Started process (PID=3072) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:36:42.752+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:36:42.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:42.754+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:36:43.130+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.130+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:43.141+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:36:43.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.254+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:43.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.433+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:36:43.453+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.710 seconds
[2025-07-18T11:37:13.679+0000] {processor.py:186} INFO - Started process (PID=3226) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:37:13.680+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:37:13.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:13.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:37:14.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.055+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:14.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:37:14.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.345+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:14.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.356+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:37:14.376+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.703 seconds
[2025-07-18T11:37:44.758+0000] {processor.py:186} INFO - Started process (PID=3379) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:37:44.759+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:37:44.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:44.761+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:37:45.152+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.152+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:45.163+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:37:45.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.446+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:45.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.457+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:37:45.480+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.732 seconds
[2025-07-18T11:38:15.957+0000] {processor.py:186} INFO - Started process (PID=3532) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:38:15.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:38:15.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:15.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:38:16.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:16.355+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:16.368+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:38:16.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:16.655+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:16.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:16.664+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:38:16.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.736 seconds
[2025-07-18T11:38:47.372+0000] {processor.py:186} INFO - Started process (PID=3685) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:38:47.373+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:38:47.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:47.376+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:38:47.770+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:47.769+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:47.780+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:38:48.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:48.080+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:48.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:48.091+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:38:48.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.742 seconds
[2025-07-18T11:39:18.284+0000] {processor.py:186} INFO - Started process (PID=3838) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:39:18.286+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:39:18.288+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:18.288+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:39:18.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:18.663+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:18.673+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:39:18.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:18.952+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:18.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:18.962+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:39:18.983+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.707 seconds
[2025-07-18T11:39:49.225+0000] {processor.py:186} INFO - Started process (PID=3991) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:39:49.226+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:39:49.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.228+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:39:49.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.606+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:49.616+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:39:49.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.891+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:49.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.901+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:39:49.922+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.704 seconds
[2025-07-18T11:40:20.039+0000] {processor.py:186} INFO - Started process (PID=4144) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:40:20.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:40:20.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:40:20.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.657+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:20.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:40:20.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.767+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:20.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.780+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:40:20.800+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.767 seconds
[2025-07-18T11:40:51.662+0000] {processor.py:186} INFO - Started process (PID=4297) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:40:51.663+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:40:51.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:51.665+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:40:52.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:52.196+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:52.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:40:52.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:52.296+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:52.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:52.307+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:40:52.325+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.669 seconds
[2025-07-18T11:41:22.769+0000] {processor.py:186} INFO - Started process (PID=4456) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:41:22.771+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:41:22.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:22.773+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:41:23.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:23.337+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:23.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:41:23.444+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:23.443+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:23.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:23.454+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:41:23.474+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.711 seconds
[2025-07-18T11:41:53.596+0000] {processor.py:186} INFO - Started process (PID=4615) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:41:53.597+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:41:53.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:53.600+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:41:54.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:54.149+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:54.156+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:41:54.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:54.250+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:54.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:54.262+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:41:54.281+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.691 seconds
[2025-07-18T11:42:24.781+0000] {processor.py:186} INFO - Started process (PID=4774) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:42:24.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:42:24.785+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:24.784+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:42:25.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:25.312+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:25.321+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:42:25.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:25.414+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:25.425+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:25.425+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:42:25.444+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.669 seconds
[2025-07-18T11:42:55.567+0000] {processor.py:186} INFO - Started process (PID=4933) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:42:55.568+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:42:55.570+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:55.570+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:42:56.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:56.082+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:56.090+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:42:56.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:56.179+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:56.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:56.189+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:42:56.206+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.645 seconds
[2025-07-18T11:43:26.383+0000] {processor.py:186} INFO - Started process (PID=5092) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:43:26.384+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:43:26.386+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:26.386+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:43:26.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:26.908+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:26.918+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:43:27.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:27.016+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:27.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:27.025+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:43:27.044+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.669 seconds
[2025-07-18T11:43:57.669+0000] {processor.py:186} INFO - Started process (PID=5251) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:43:57.670+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:43:57.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:57.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:43:58.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:58.263+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:58.270+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:43:58.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:58.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:58.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:58.370+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:43:58.392+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.731 seconds
[2025-07-18T11:44:28.474+0000] {processor.py:186} INFO - Started process (PID=5416) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:44:28.475+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:44:28.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:28.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:44:29.154+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:29.154+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:29.167+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:44:29.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:29.279+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:29.294+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:29.293+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:44:29.316+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.849 seconds
[2025-07-18T11:44:59.458+0000] {processor.py:186} INFO - Started process (PID=5575) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:44:59.459+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:44:59.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:59.461+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:45:00.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:00.002+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:00.009+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:45:00.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:00.113+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:00.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:00.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:45:00.147+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.695 seconds
[2025-07-18T11:45:30.921+0000] {processor.py:186} INFO - Started process (PID=5734) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:45:30.922+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:45:30.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:30.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:45:31.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:31.513+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:31.522+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:45:31.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:31.636+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:31.648+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:31.647+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:45:31.667+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.753 seconds
[2025-07-18T11:46:02.546+0000] {processor.py:186} INFO - Started process (PID=5893) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:46:02.548+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:46:02.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:02.554+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:46:03.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:03.541+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:03.549+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:46:03.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:03.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:03.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:03.666+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:46:03.683+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 1.155 seconds
[2025-07-18T11:46:33.774+0000] {processor.py:186} INFO - Started process (PID=6052) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:46:33.775+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:46:33.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:33.777+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:46:34.336+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:34.335+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:34.347+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:46:34.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:34.452+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:34.464+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:34.463+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:46:34.487+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.719 seconds
[2025-07-18T11:47:04.896+0000] {processor.py:186} INFO - Started process (PID=6211) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:47:04.897+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:47:04.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:04.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:47:05.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:05.397+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:05.405+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:47:05.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:05.501+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:05.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:05.512+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:47:05.532+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.643 seconds
