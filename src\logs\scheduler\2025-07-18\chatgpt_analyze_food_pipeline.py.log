[2025-07-18T11:25:55.736+0000] {processor.py:186} INFO - Started process (PID=188) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:25:55.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:25:55.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:55.740+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:25:55.843+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:55.843+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:55.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:25:56.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.067+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.081+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.090+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.100+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.108+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.117+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.125+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.126+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:56.141+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:56.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.142+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_analyze_food_pipeline
[2025-07-18T11:25:56.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:25:56.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.437 seconds
[2025-07-18T11:26:27.007+0000] {processor.py:186} INFO - Started process (PID=319) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:27.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:26:27.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.011+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:27.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.107+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:27.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:27.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.432+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:27.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.445+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:26:27.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.468 seconds
[2025-07-18T11:26:57.888+0000] {processor.py:186} INFO - Started process (PID=450) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:57.889+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:26:57.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:57.892+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:58.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.159+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:58.169+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:26:58.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:58.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.307+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:26:58.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.451 seconds
[2025-07-18T11:27:28.730+0000] {processor.py:186} INFO - Started process (PID=581) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:28.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:27:28.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.734+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:28.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.829+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:28.838+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:28.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.953+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:28.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.965+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:27:28.985+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.263 seconds
[2025-07-18T11:27:59.549+0000] {processor.py:186} INFO - Started process (PID=712) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:59.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:27:59.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.553+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:59.638+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.637+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:59.649+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:27:59.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.775+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:59.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.786+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:27:59.809+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.265 seconds
[2025-07-18T11:28:30.000+0000] {processor.py:186} INFO - Started process (PID=843) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:28:30.001+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:28:30.005+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.004+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:28:30.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.086+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.096+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:28:30.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.216+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.226+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:28:30.251+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.259 seconds
[2025-07-18T11:29:00.405+0000] {processor.py:186} INFO - Started process (PID=974) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:00.406+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:29:00.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.409+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:00.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.489+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:00.500+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:00.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.612+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:00.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.625+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:29:00.646+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.248 seconds
[2025-07-18T11:29:31.092+0000] {processor.py:186} INFO - Started process (PID=1105) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:31.093+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:29:31.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.097+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:31.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.179+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.191+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:29:31.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.300+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.311+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:29:31.334+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.248 seconds
[2025-07-18T11:30:02.071+0000] {processor.py:186} INFO - Started process (PID=1236) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:02.072+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:30:02.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.075+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:02.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.164+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.176+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:02.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.314+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:30:02.337+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.272 seconds
[2025-07-18T11:30:32.625+0000] {processor.py:186} INFO - Started process (PID=1367) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:32.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:30:32.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.628+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:32.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:32.748+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:30:32.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.869+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:32.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.880+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:30:32.902+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.284 seconds
[2025-07-18T11:31:03.120+0000] {processor.py:186} INFO - Started process (PID=1498) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:03.121+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:31:03.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.125+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:03.250+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.249+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:03.262+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:03.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:03.432+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.431+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:31:03.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.352 seconds
[2025-07-18T11:31:34.352+0000] {processor.py:186} INFO - Started process (PID=1629) to work on /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:34.354+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_analyze_food_pipeline.py for tasks to queue
[2025-07-18T11:31:34.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.356+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:34.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.439+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:34.451+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_analyze_food_pipeline' retrieved from /opt/airflow/dags/chatgpt_analyze_food_pipeline.py
[2025-07-18T11:31:34.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.592+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:34.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_analyze_food_pipeline to None, run_after=None
[2025-07-18T11:31:34.630+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_analyze_food_pipeline.py took 0.283 seconds
