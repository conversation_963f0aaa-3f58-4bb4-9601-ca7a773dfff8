[2025-07-18T11:25:55.476+0000] {manager.py:483} INFO - Processing files using up to 2 processes at a time 
[2025-07-18T11:25:55.477+0000] {manager.py:484} INFO - Process each file at most once every 30 seconds
[2025-07-18T11:25:55.478+0000] {manager.py:485} INFO - Checking for new files in /opt/airflow/dags every 300 seconds
[2025-07-18T11:25:55.478+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-18T11:25:55.605+0000] {manager.py:824} INFO - There are 25 files in /opt/airflow/dags
[2025-07-18T11:25:55.746+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                       PID  Runtime      # DAGs    # Errors  Last Runtime    Last Run      Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  ----------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py              188  0.03s             0           0                                                   0
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py     191  0.02s             0           0                                                   0
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           0                                                   0
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        0           0                                                   0
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         0           0                                                   0
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               0           0                                                   0
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              0           0                                                   0
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            0           0                                                   0
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_image_pipeline.py                                            0           0                                                   0
/opt/airflow/dags/chatgpt_message_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           0           0                                                   0
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     0           0                                                   0
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           0                                                   0
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 0           0                                                   0
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          0           0                                                   0
/opt/airflow/dags/perplexity_pipeline.py                                               0           0                                                   0
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           0                                                   0
================================================================================
[2025-07-18T11:26:25.950+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.62s           2025-07-18T11:25:56                      43
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.59s           2025-07-18T11:25:59                      43
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.56s           2025-07-18T11:26:00                      43
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.56s           2025-07-18T11:25:56                      43
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.55s           2025-07-18T11:25:56                      43
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.51s           2025-07-18T11:25:57                      43
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.49s           2025-07-18T11:25:57                      43
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.49s           2025-07-18T11:26:00                      43
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.48s           2025-07-18T11:25:58                      43
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.48s           2025-07-18T11:25:59                      43
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.48s           2025-07-18T11:25:58                      43
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.48s           2025-07-18T11:25:57                      43
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.47s           2025-07-18T11:25:59                      43
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.47s           2025-07-18T11:25:58                      43
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.47s           2025-07-18T11:25:56                      43
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.46s           2025-07-18T11:26:00                      43
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.46s           2025-07-18T11:25:58                      43
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.45s           2025-07-18T11:26:00                      43
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.45s           2025-07-18T11:25:57                      43
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.45s           2025-07-18T11:25:59                      43
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.44s           2025-07-18T11:26:01                      43
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.43s           2025-07-18T11:26:01                      43
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-18T11:26:01                       3
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.08s           2025-07-18T11:25:56                       3
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.07s           2025-07-18T11:26:00                       3
================================================================================
[2025-07-18T11:26:56.064+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.50s           2025-07-18T11:26:27                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.50s           2025-07-18T11:26:27                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.50s           2025-07-18T11:26:29                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.46s           2025-07-18T11:26:29                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.46s           2025-07-18T11:26:29                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.45s           2025-07-18T11:26:28                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.43s           2025-07-18T11:26:30                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.43s           2025-07-18T11:26:27                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.43s           2025-07-18T11:26:28                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.43s           2025-07-18T11:26:29                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.42s           2025-07-18T11:26:32                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.42s           2025-07-18T11:26:31                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.41s           2025-07-18T11:26:31                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.41s           2025-07-18T11:26:28                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.40s           2025-07-18T11:26:28                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.40s           2025-07-18T11:26:31                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.40s           2025-07-18T11:26:31                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.40s           2025-07-18T11:26:30                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.39s           2025-07-18T11:26:32                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.38s           2025-07-18T11:26:30                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.38s           2025-07-18T11:26:30                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.37s           2025-07-18T11:26:30                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.24s           2025-07-18T11:26:32                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.07s           2025-07-18T11:26:28                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.06s           2025-07-18T11:26:31                       5
================================================================================
[2025-07-18T11:27:26.656+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.49s           2025-07-18T11:26:58                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.48s           2025-07-18T11:26:58                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.45s           2025-07-18T11:27:00                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.44s           2025-07-18T11:27:00                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.43s           2025-07-18T11:26:58                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.42s           2025-07-18T11:27:00                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.40s           2025-07-18T11:27:00                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.40s           2025-07-18T11:27:01                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.40s           2025-07-18T11:27:01                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.40s           2025-07-18T11:26:59                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.38s           2025-07-18T11:26:59                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.38s           2025-07-18T11:26:59                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.36s           2025-07-18T11:27:00                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.30s           2025-07-18T11:27:01                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.30s           2025-07-18T11:27:02                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.29s           2025-07-18T11:27:02                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.28s           2025-07-18T11:27:02                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.28s           2025-07-18T11:27:02                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.28s           2025-07-18T11:27:02                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-18T11:27:02                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.26s           2025-07-18T11:27:03                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.25s           2025-07-18T11:27:03                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-18T11:27:03                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.07s           2025-07-18T11:27:02                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.06s           2025-07-18T11:26:58                       5
================================================================================
[2025-07-18T11:27:57.479+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.31s           2025-07-18T11:27:29                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.29s           2025-07-18T11:27:30                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.28s           2025-07-18T11:27:33                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.28s           2025-07-18T11:27:30                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.28s           2025-07-18T11:27:32                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.28s           2025-07-18T11:27:28                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.27s           2025-07-18T11:27:33                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.27s           2025-07-18T11:27:33                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.26s           2025-07-18T11:27:32                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.26s           2025-07-18T11:27:33                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.26s           2025-07-18T11:27:33                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.26s           2025-07-18T11:27:33                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.26s           2025-07-18T11:27:31                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.25s           2025-07-18T11:27:31                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.25s           2025-07-18T11:27:32                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.25s           2025-07-18T11:27:29                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.24s           2025-07-18T11:27:31                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.24s           2025-07-18T11:27:31                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.24s           2025-07-18T11:27:32                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.24s           2025-07-18T11:27:31                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.24s           2025-07-18T11:27:29                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.23s           2025-07-18T11:27:29                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-18T11:27:34                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.07s           2025-07-18T11:27:33                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.06s           2025-07-18T11:27:29                       5
================================================================================
[2025-07-18T11:28:27.934+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.30s           2025-07-18T11:27:59                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.29s           2025-07-18T11:28:04                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.27s           2025-07-18T11:27:59                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.27s           2025-07-18T11:28:04                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.27s           2025-07-18T11:28:01                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.26s           2025-07-18T11:28:01                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.26s           2025-07-18T11:28:02                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.26s           2025-07-18T11:28:00                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.25s           2025-07-18T11:28:00                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.25s           2025-07-18T11:28:02                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.25s           2025-07-18T11:28:03                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.25s           2025-07-18T11:28:02                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.25s           2025-07-18T11:28:03                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.25s           2025-07-18T11:28:00                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-18T11:28:03                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.24s           2025-07-18T11:28:01                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-18T11:28:01                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.23s           2025-07-18T11:28:03                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.23s           2025-07-18T11:28:03                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.23s           2025-07-18T11:28:01                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.23s           2025-07-18T11:28:04                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.23s           2025-07-18T11:28:02                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-18T11:28:04                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.07s           2025-07-18T11:28:03                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.06s           2025-07-18T11:28:00                       5
================================================================================
[2025-07-18T11:28:58.337+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.31s           2025-07-18T11:28:30                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.29s           2025-07-18T11:28:32                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.28s           2025-07-18T11:28:34                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.28s           2025-07-18T11:28:32                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.28s           2025-07-18T11:28:30                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.28s           2025-07-18T11:28:33                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.28s           2025-07-18T11:28:30                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.27s           2025-07-18T11:28:33                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.27s           2025-07-18T11:28:30                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.27s           2025-07-18T11:28:34                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-18T11:28:30                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.26s           2025-07-18T11:28:32                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.26s           2025-07-18T11:28:34                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.26s           2025-07-18T11:28:33                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.26s           2025-07-18T11:28:33                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.25s           2025-07-18T11:28:33                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.25s           2025-07-18T11:28:32                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.25s           2025-07-18T11:28:34                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.25s           2025-07-18T11:28:32                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.25s           2025-07-18T11:28:34                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.24s           2025-07-18T11:28:32                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.24s           2025-07-18T11:28:33                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-18T11:28:34                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.07s           2025-07-18T11:28:30                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.06s           2025-07-18T11:28:33                       5
================================================================================
[2025-07-18T11:29:29.028+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.29s           2025-07-18T11:29:00                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.28s           2025-07-18T11:29:02                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.27s           2025-07-18T11:29:00                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.27s           2025-07-18T11:29:04                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.26s           2025-07-18T11:29:04                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.26s           2025-07-18T11:29:04                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.26s           2025-07-18T11:29:02                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-18T11:29:04                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.25s           2025-07-18T11:29:02                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.25s           2025-07-18T11:29:01                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.25s           2025-07-18T11:29:03                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.25s           2025-07-18T11:29:05                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.25s           2025-07-18T11:29:03                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.24s           2025-07-18T11:29:03                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.24s           2025-07-18T11:29:01                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.24s           2025-07-18T11:29:00                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.24s           2025-07-18T11:29:02                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.24s           2025-07-18T11:29:05                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.24s           2025-07-18T11:29:05                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.24s           2025-07-18T11:29:02                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.24s           2025-07-18T11:29:05                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.23s           2025-07-18T11:29:03                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.11s           2025-07-18T11:29:05                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.07s           2025-07-18T11:29:00                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.06s           2025-07-18T11:29:05                       5
================================================================================
[2025-07-18T11:30:00.003+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.28s           2025-07-18T11:29:33                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.28s           2025-07-18T11:29:32                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.28s           2025-07-18T11:29:33                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.28s           2025-07-18T11:29:33                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.28s           2025-07-18T11:29:31                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.27s           2025-07-18T11:29:33                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.27s           2025-07-18T11:29:31                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.27s           2025-07-18T11:29:36                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.27s           2025-07-18T11:29:35                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.27s           2025-07-18T11:29:31                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.27s           2025-07-18T11:29:36                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.27s           2025-07-18T11:29:33                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.27s           2025-07-18T11:29:35                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.26s           2025-07-18T11:29:36                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.26s           2025-07-18T11:29:33                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.26s           2025-07-18T11:29:36                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.25s           2025-07-18T11:29:35                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.25s           2025-07-18T11:29:34                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.25s           2025-07-18T11:29:35                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.25s           2025-07-18T11:29:34                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.24s           2025-07-18T11:29:33                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.23s           2025-07-18T11:29:31                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-18T11:29:36                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.08s           2025-07-18T11:29:31                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.06s           2025-07-18T11:29:35                       5
================================================================================
[2025-07-18T11:30:30.553+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.31s           2025-07-18T11:30:02                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.30s           2025-07-18T11:30:04                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.30s           2025-07-18T11:30:06                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.28s           2025-07-18T11:30:05                      32
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.28s           2025-07-18T11:30:02                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.27s           2025-07-18T11:30:05                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.27s           2025-07-18T11:30:06                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.27s           2025-07-18T11:30:05                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.27s           2025-07-18T11:30:04                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-18T11:30:02                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.26s           2025-07-18T11:30:04                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.26s           2025-07-18T11:30:05                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.26s           2025-07-18T11:30:06                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.25s           2025-07-18T11:30:05                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.25s           2025-07-18T11:30:04                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.25s           2025-07-18T11:30:06                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.25s           2025-07-18T11:30:07                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.25s           2025-07-18T11:30:04                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.24s           2025-07-18T11:30:07                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.24s           2025-07-18T11:30:02                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.24s           2025-07-18T11:30:04                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.24s           2025-07-18T11:30:02                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.12s           2025-07-18T11:30:06                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.07s           2025-07-18T11:30:06                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.06s           2025-07-18T11:30:02                       5
================================================================================
[2025-07-18T11:30:56.472+0000] {manager.py:821} INFO - Searching for files in /opt/airflow/dags
[2025-07-18T11:30:56.649+0000] {manager.py:824} INFO - There are 25 files in /opt/airflow/dags
[2025-07-18T11:31:00.770+0000] {manager.py:997} INFO - 
================================================================================
DAG File Processing Stats

File Path                                                     PID    Runtime      # DAGs    # Errors  Last Runtime    Last Run               Last # of DB Queries
------------------------------------------------------------  -----  ---------  --------  ----------  --------------  -------------------  ----------------------
/opt/airflow/dags/chatgpt_calories_achievements_pipeline.py                            1           0  0.31s           2025-07-18T11:30:32                      32
/opt/airflow/dags/chatgpt_analyze_food_pipeline.py                                     1           0  0.31s           2025-07-18T11:30:32                      32
/opt/airflow/dags/perplexity_pipeline.py                                               1           0  0.28s           2025-07-18T11:30:38                      32
/opt/airflow/dags/chatgpt_recount_calories_pipeline.py                                 1           0  0.28s           2025-07-18T11:30:37                      32
/opt/airflow/dags/chatgpt_challenge_classify_pipeline.py                               1           0  0.28s           2025-07-18T11:30:33                      32
/opt/airflow/dags/chatgpt_describe_challenge_pipeline.py                               1           0  0.28s           2025-07-18T11:30:35                      32
/opt/airflow/dags/chatgpt_message_recommendation_pipeline.py                           1           0  0.27s           2025-07-18T11:30:36                      32
/opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py                               1           0  0.27s           2025-07-18T11:30:33                      32
/opt/airflow/dags/chatgpt_classify_pipeline.py                                         1           0  0.27s           2025-07-18T11:30:35                      32
/opt/airflow/dags/chatgpt_weights_pipeline.py                                          1           0  0.27s           2025-07-18T11:30:37                      32
/opt/airflow/dags/chatgpt_chat_open_pipeline.py                                        1           0  0.27s           2025-07-18T11:30:34                      32
/opt/airflow/dags/chatgpt_calories_small_trend_pipeline.py                             1           0  0.27s           2025-07-18T11:30:33                      32
/opt/airflow/dags/chatgpt_unified_question_pipeline.py                                 1           0  0.27s           2025-07-18T11:30:37                      32
/opt/airflow/dags/chatgpt_message_pipeline.py                                          1           0  0.27s           2025-07-18T11:30:36                      32
/opt/airflow/dags/chatgpt_metrics_pipeline.py                                          1           0  0.26s           2025-07-18T11:30:37                      32
/opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py                               1           0  0.26s           2025-07-18T11:30:34                      32
/opt/airflow/dags/chatgpt_image_object_pipeline.py                                     1           0  0.26s           2025-07-18T11:30:36                      32
/opt/airflow/dags/chatgpt_goals_pipeline.py                                            1           0  0.26s           2025-07-18T11:30:36                      32
/opt/airflow/dags/chatgpt_message_risk_pipeline.py                                     1           0  0.25s           2025-07-18T11:30:37                      32
/opt/airflow/dags/chatgpt_generate_challenges_pipeline.py                              1           0  0.25s           2025-07-18T11:30:36                      32
/opt/airflow/dags/chatgpt_clarify_pipeline.py                                          1           0  0.24s           2025-07-18T11:30:35                      32
/opt/airflow/dags/chatgpt_image_pipeline.py                                            1           0  0.24s           2025-07-18T11:30:36                      32
/opt/airflow/dags/set_calories_trends_pipeline.py                                      0           1  0.13s           2025-07-18T11:30:38                       5
/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py                                    0           1  0.07s           2025-07-18T11:30:33                       5
/opt/airflow/dags/chatgpt_pdf_pipeline.py                                              0           1  0.07s           2025-07-18T11:30:37                       5
================================================================================
