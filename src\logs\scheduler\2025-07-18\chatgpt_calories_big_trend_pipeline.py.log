[2025-07-18T11:25:56.218+0000] {processor.py:186} INFO - Started process (PID=196) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:25:56.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:25:56.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:25:56.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:56.345+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:25:56.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.455+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.467+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.476+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.486+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.497+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.506+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.515+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.516+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:56.710+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:56.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.711+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.712+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:25:56.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.522 seconds
[2025-07-18T11:26:27.513+0000] {processor.py:186} INFO - Started process (PID=329) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:27.514+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:26:27.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.516+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:27.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.587+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:27.596+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:27.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.873+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:27.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:26:27.905+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.399 seconds
[2025-07-18T11:26:58.383+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:58.384+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:26:58.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:58.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.642+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:58.651+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:58.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.751+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:58.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.759+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:26:58.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.404 seconds
[2025-07-18T11:27:29.033+0000] {processor.py:186} INFO - Started process (PID=591) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:29.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:27:29.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:29.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.105+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:29.113+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:29.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.208+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:29.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.218+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:27:29.238+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.211 seconds
[2025-07-18T11:27:59.846+0000] {processor.py:186} INFO - Started process (PID=722) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:59.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:27:59.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:59.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.931+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:59.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:28:00.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.037+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:00.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.048+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:28:00.067+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.227 seconds
[2025-07-18T11:28:30.300+0000] {processor.py:186} INFO - Started process (PID=853) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:28:30.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:28:30.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.303+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:28:30.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.372+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:28:30.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.495+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.507+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:28:30.538+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.243 seconds
[2025-07-18T11:29:00.698+0000] {processor.py:186} INFO - Started process (PID=984) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:00.700+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:29:00.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.702+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:00.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.773+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:00.781+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:00.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:00.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:29:00.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.219 seconds
[2025-07-18T11:29:31.375+0000] {processor.py:186} INFO - Started process (PID=1115) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:31.376+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:29:31.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.378+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:31.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.453+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:31.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.552+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.562+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:29:31.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.212 seconds
[2025-07-18T11:30:02.370+0000] {processor.py:186} INFO - Started process (PID=1246) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:02.371+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:30:02.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:02.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.455+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.465+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:02.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.584+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:30:02.604+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.241 seconds
[2025-07-18T11:30:32.943+0000] {processor.py:186} INFO - Started process (PID=1377) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:32.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:30:32.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:33.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.028+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:33.037+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:33.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.145+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:33.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:30:33.184+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.248 seconds
[2025-07-18T11:31:03.524+0000] {processor.py:186} INFO - Started process (PID=1508) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:03.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:31:03.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.529+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:03.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.608+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:03.617+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:03.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.746+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:03.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.759+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:31:03.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.266 seconds
[2025-07-18T11:31:34.675+0000] {processor.py:186} INFO - Started process (PID=1639) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:34.676+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:31:34.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.678+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:34.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.763+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:34.770+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:34.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.884+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:34.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.899+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:31:34.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.259 seconds
[2025-07-18T11:32:05.288+0000] {processor.py:186} INFO - Started process (PID=1770) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:32:05.288+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:32:05.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.291+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:32:05.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.380+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:05.390+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:32:05.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:05.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.500+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:32:05.519+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.238 seconds
[2025-07-18T11:32:35.761+0000] {processor.py:186} INFO - Started process (PID=1901) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:32:35.762+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:32:35.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:32:35.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.837+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:35.844+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:32:35.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.942+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:35.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.953+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:32:35.973+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.217 seconds
[2025-07-18T11:33:06.296+0000] {processor.py:186} INFO - Started process (PID=2037) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:33:06.296+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:33:06.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.299+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:33:06.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:06.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:33:06.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.481+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:06.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.492+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:33:06.513+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.223 seconds
[2025-07-18T11:33:37.239+0000] {processor.py:186} INFO - Started process (PID=2171) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:33:37.240+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:33:37.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:33:37.602+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.602+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:37.612+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:33:37.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.717+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:37.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.727+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:33:37.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.519 seconds
[2025-07-18T11:34:07.845+0000] {processor.py:186} INFO - Started process (PID=2319) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:34:07.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:34:07.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:34:08.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.254+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:08.263+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:34:08.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.396+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:08.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.413+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:34:08.577+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.739 seconds
[2025-07-18T11:34:39.231+0000] {processor.py:186} INFO - Started process (PID=2475) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:34:39.233+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:34:39.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.236+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:34:39.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.675+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:39.683+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:34:39.801+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.800+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:39.997+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.996+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:34:40.020+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.797 seconds
[2025-07-18T11:35:10.471+0000] {processor.py:186} INFO - Started process (PID=2628) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:35:10.472+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:35:10.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.475+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:35:10.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.908+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:10.918+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:35:11.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:11.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:11.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:11.234+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:35:11.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.793 seconds
[2025-07-18T11:35:41.375+0000] {processor.py:186} INFO - Started process (PID=2781) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:35:41.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:35:41.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.379+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:35:41.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.762+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:41.773+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:35:41.903+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.903+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:42.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:35:42.105+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.737 seconds
[2025-07-18T11:36:12.726+0000] {processor.py:186} INFO - Started process (PID=2936) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:36:12.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:36:12.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.729+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:36:13.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.110+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:13.120+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:36:13.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.410+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:13.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.421+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:36:13.440+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.720 seconds
[2025-07-18T11:36:43.602+0000] {processor.py:186} INFO - Started process (PID=3089) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:36:43.603+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:36:43.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.605+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:36:43.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.965+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:43.973+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:36:44.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.228+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:44.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.238+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:36:44.256+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.660 seconds
[2025-07-18T11:37:14.473+0000] {processor.py:186} INFO - Started process (PID=3241) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:37:14.474+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:37:14.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.476+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:37:14.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.840+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:14.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:37:15.125+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.125+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:15.136+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.136+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:37:15.155+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.688 seconds
[2025-07-18T11:37:45.637+0000] {processor.py:186} INFO - Started process (PID=3396) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:37:45.638+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:37:45.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.641+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:37:46.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:46.322+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:46.414+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:37:47.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:47.173+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:47.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:47.216+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:37:47.280+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 1.652 seconds
[2025-07-18T11:38:17.772+0000] {processor.py:186} INFO - Started process (PID=3549) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:38:17.773+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:38:17.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:17.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:38:18.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:18.196+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:18.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:38:18.441+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:18.440+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:18.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:18.451+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:38:18.470+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.705 seconds
[2025-07-18T11:38:49.184+0000] {processor.py:186} INFO - Started process (PID=3702) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:38:49.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:38:49.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.187+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:38:49.558+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.558+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:49.567+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:38:49.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.850+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:49.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:49.861+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:38:49.881+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.703 seconds
[2025-07-18T11:39:20.102+0000] {processor.py:186} INFO - Started process (PID=3855) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:39:20.104+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:39:20.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:39:20.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.699+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:20.706+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:39:20.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.805+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:20.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:20.815+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:39:20.834+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.738 seconds
[2025-07-18T11:39:51.000+0000] {processor.py:186} INFO - Started process (PID=4008) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:39:51.001+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:39:51.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.003+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:39:51.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.511+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:51.519+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:39:51.612+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.611+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:51.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:51.623+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:39:51.644+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.650 seconds
[2025-07-18T11:40:21.865+0000] {processor.py:186} INFO - Started process (PID=4161) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:40:21.866+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:40:21.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:21.868+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:40:22.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:22.418+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:22.425+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:40:22.518+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:22.517+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:22.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:22.527+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:40:22.547+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.689 seconds
[2025-07-18T11:40:53.398+0000] {processor.py:186} INFO - Started process (PID=4314) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:40:53.398+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:40:53.401+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:53.400+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:40:53.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:53.940+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:53.948+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:40:54.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.040+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:54.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.053+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:40:54.074+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.682 seconds
[2025-07-18T11:41:24.555+0000] {processor.py:186} INFO - Started process (PID=4473) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:41:24.556+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:41:24.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:24.558+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:41:25.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.054+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:25.063+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:41:25.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.153+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:25.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.163+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:41:25.183+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.634 seconds
[2025-07-18T11:41:55.347+0000] {processor.py:186} INFO - Started process (PID=4632) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:41:55.348+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:41:55.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:55.350+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:41:55.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:55.859+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:55.866+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:41:55.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:55.959+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:55.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:55.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:41:55.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.651 seconds
[2025-07-18T11:42:26.552+0000] {processor.py:186} INFO - Started process (PID=4791) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:42:26.554+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:42:26.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:26.556+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:42:27.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.102+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:27.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:42:27.212+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.211+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:27.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:42:27.245+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.698 seconds
[2025-07-18T11:42:57.373+0000] {processor.py:186} INFO - Started process (PID=4950) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:42:57.374+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:42:57.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:57.377+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:42:57.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:57.935+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:57.943+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:42:58.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.044+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:58.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.054+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:42:58.074+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.706 seconds
[2025-07-18T11:43:28.175+0000] {processor.py:186} INFO - Started process (PID=5115) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:43:28.176+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:43:28.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.178+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:43:28.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.742+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:28.750+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:43:28.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.847+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:28.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:28.857+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:43:28.877+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.708 seconds
[2025-07-18T11:43:59.478+0000] {processor.py:186} INFO - Started process (PID=5274) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:43:59.479+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:43:59.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:59.482+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:44:00.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:00.024+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:00.033+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:44:00.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:00.131+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:00.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:00.143+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:44:00.162+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.690 seconds
[2025-07-18T11:44:30.416+0000] {processor.py:186} INFO - Started process (PID=5433) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:44:30.417+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:44:30.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:30.421+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:44:31.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.032+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:31.039+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:44:31.152+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.152+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:31.165+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.164+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:44:31.188+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.779 seconds
[2025-07-18T11:45:01.298+0000] {processor.py:186} INFO - Started process (PID=5592) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:45:01.299+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:45:01.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:01.301+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:45:01.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:01.893+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:01.900+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:45:02.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:02.003+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:02.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:02.014+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:45:02.036+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.745 seconds
[2025-07-18T11:45:32.752+0000] {processor.py:186} INFO - Started process (PID=5751) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:45:32.753+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:45:32.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:32.756+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:45:33.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:33.367+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:33.374+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:45:33.464+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:33.464+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:33.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:33.474+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:45:33.493+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.747 seconds
[2025-07-18T11:46:03.817+0000] {processor.py:186} INFO - Started process (PID=5910) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:46:03.818+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:46:03.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:03.820+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:46:04.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:04.376+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:04.384+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:46:04.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:04.475+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:04.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:04.485+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:46:04.506+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.697 seconds
[2025-07-18T11:46:34.633+0000] {processor.py:186} INFO - Started process (PID=6069) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:46:34.633+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:46:34.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:34.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:46:35.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:35.151+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:35.159+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:46:35.261+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:35.260+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:35.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:35.272+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:46:35.293+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.667 seconds
[2025-07-18T11:47:05.616+0000] {processor.py:186} INFO - Started process (PID=6226) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:47:05.617+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:47:05.619+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:05.619+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:47:06.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:06.110+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:06.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:47:06.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:06.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:06.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:06.230+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:47:06.252+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.642 seconds
[2025-07-18T11:47:36.771+0000] {processor.py:186} INFO - Started process (PID=6387) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:47:36.773+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:47:36.776+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:36.775+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:47:37.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:37.300+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:37.307+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:47:37.399+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:37.398+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:37.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:37.411+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:47:37.430+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.667 seconds
[2025-07-18T11:48:07.556+0000] {processor.py:186} INFO - Started process (PID=6546) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:48:07.557+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:48:07.560+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:07.560+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:48:08.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:08.068+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:08.077+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:48:08.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:08.175+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:08.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:08.187+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:48:08.208+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.658 seconds
[2025-07-18T11:48:38.486+0000] {processor.py:186} INFO - Started process (PID=6705) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:48:38.487+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:48:38.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:38.490+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:48:39.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:39.019+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:39.027+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:48:39.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:39.136+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:39.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:39.147+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:48:39.167+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.688 seconds
[2025-07-18T11:49:09.481+0000] {processor.py:186} INFO - Started process (PID=6870) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:49:09.482+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:49:09.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:09.485+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:49:09.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:09.959+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:09.966+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:49:10.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:10.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:10.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:10.072+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:49:10.093+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.618 seconds
[2025-07-18T11:49:40.412+0000] {processor.py:186} INFO - Started process (PID=7029) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:49:40.414+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:49:40.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:40.416+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:49:40.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:40.973+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:40.979+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:49:41.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:41.078+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:41.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:41.090+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:49:41.109+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.704 seconds
[2025-07-18T11:50:11.241+0000] {processor.py:186} INFO - Started process (PID=7186) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:50:11.242+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:50:11.245+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:11.244+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:50:11.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:11.750+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:11.757+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:50:11.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:11.853+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:11.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:11.866+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:50:11.888+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.654 seconds
[2025-07-18T11:50:42.013+0000] {processor.py:186} INFO - Started process (PID=7344) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:50:42.014+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:50:42.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:42.017+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:50:42.555+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:42.555+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:42.564+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:50:42.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:42.672+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:42.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:42.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:50:42.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.696 seconds
