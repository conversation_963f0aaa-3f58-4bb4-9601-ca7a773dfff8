[2025-07-18T11:25:56.218+0000] {processor.py:186} INFO - Started process (PID=196) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:25:56.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:25:56.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.224+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:25:56.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.337+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:56.345+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:25:56.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.455+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.467+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.476+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.486+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.486+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.497+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.506+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.515+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.516+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:56.710+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:56.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.711+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_calories_big_trend_pipeline
[2025-07-18T11:25:56.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.712+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:25:56.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.522 seconds
[2025-07-18T11:26:27.513+0000] {processor.py:186} INFO - Started process (PID=329) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:27.514+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:26:27.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.516+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:27.588+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.587+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:27.596+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:27.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.873+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:27.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:26:27.905+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.399 seconds
[2025-07-18T11:26:58.383+0000] {processor.py:186} INFO - Started process (PID=460) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:58.384+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:26:58.388+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.388+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:58.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.642+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:58.651+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:26:58.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.751+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:58.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.759+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:26:58.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.404 seconds
[2025-07-18T11:27:29.033+0000] {processor.py:186} INFO - Started process (PID=591) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:29.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:27:29.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.035+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:29.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.105+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:29.113+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:29.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.208+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:29.218+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.218+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:27:29.238+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.211 seconds
[2025-07-18T11:27:59.846+0000] {processor.py:186} INFO - Started process (PID=722) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:59.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:27:59.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:27:59.931+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.931+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:59.939+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:28:00.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.037+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:00.048+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.048+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:28:00.067+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.227 seconds
[2025-07-18T11:28:30.300+0000] {processor.py:186} INFO - Started process (PID=853) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:28:30.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:28:30.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.303+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:28:30.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.372+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:28:30.495+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.495+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.507+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:28:30.538+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.243 seconds
[2025-07-18T11:29:00.698+0000] {processor.py:186} INFO - Started process (PID=984) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:00.700+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:29:00.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.702+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:00.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.773+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:00.781+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:00.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:00.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:29:00.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.219 seconds
[2025-07-18T11:29:31.375+0000] {processor.py:186} INFO - Started process (PID=1115) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:31.376+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:29:31.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.378+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:31.454+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.453+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.461+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:29:31.553+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.552+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.562+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:29:31.581+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.212 seconds
[2025-07-18T11:30:02.370+0000] {processor.py:186} INFO - Started process (PID=1246) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:02.371+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:30:02.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:02.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.455+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.465+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:02.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.573+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.584+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:30:02.604+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.241 seconds
[2025-07-18T11:30:32.943+0000] {processor.py:186} INFO - Started process (PID=1377) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:32.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:30:32.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:33.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.028+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:33.037+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:30:33.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.145+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:33.157+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.156+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:30:33.184+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.248 seconds
[2025-07-18T11:31:03.524+0000] {processor.py:186} INFO - Started process (PID=1508) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:03.525+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:31:03.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.529+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:03.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.608+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:03.617+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:03.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.746+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:03.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.759+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:31:03.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.266 seconds
[2025-07-18T11:31:34.675+0000] {processor.py:186} INFO - Started process (PID=1639) to work on /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:34.676+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py for tasks to queue
[2025-07-18T11:31:34.678+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.678+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:34.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.763+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:34.770+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_big_trend_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py
[2025-07-18T11:31:34.885+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.884+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:34.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.899+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_big_trend_pipeline to None, run_after=None
[2025-07-18T11:31:34.927+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_big_trend_pipeline.py took 0.259 seconds
