[2025-07-18T11:25:57.435+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:25:57.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:25:57.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:25:57.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.529+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:57.536+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:25:57.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.641+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.653+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.851+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.860+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.866+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.872+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.879+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:57.890+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:57.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.891+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_clarify_pipeline
[2025-07-18T11:25:57.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:25:57.909+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.480 seconds
[2025-07-18T11:26:28.799+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:28.800+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:26:28.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:28.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.886+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.893+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:29.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:29.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:26:29.257+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.464 seconds
[2025-07-18T11:26:59.729+0000] {processor.py:186} INFO - Started process (PID=488) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:59.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:26:59.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:59.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.988+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.994+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:27:00.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.113+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:27:00.145+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.423 seconds
[2025-07-18T11:27:30.933+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:27:30.934+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:27:30.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.936+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:27:31.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.006+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.014+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:27:31.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:27:31.142+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.214 seconds
[2025-07-18T11:28:01.475+0000] {processor.py:186} INFO - Started process (PID=752) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:01.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:28:01.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:01.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.549+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:01.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.650+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.660+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:28:01.680+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.211 seconds
[2025-07-18T11:28:32.029+0000] {processor.py:186} INFO - Started process (PID=883) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:32.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:28:32.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:32.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.109+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:32.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.213+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:32.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:28:32.242+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.219 seconds
[2025-07-18T11:29:02.348+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:02.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:29:02.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.351+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:02.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.424+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.432+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:02.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:29:02.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.215 seconds
[2025-07-18T11:29:33.062+0000] {processor.py:186} INFO - Started process (PID=1145) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:33.063+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:29:33.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:33.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.150+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.160+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:33.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.258+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.271+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:29:33.292+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.237 seconds
[2025-07-18T11:30:04.176+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:04.177+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:30:04.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.179+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:04.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.260+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.269+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:04.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.377+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:30:04.397+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.228 seconds
[2025-07-18T11:30:35.379+0000] {processor.py:186} INFO - Started process (PID=1407) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:35.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:30:35.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:35.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.459+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:35.468+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:35.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:35.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.574+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:30:35.594+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.221 seconds
[2025-07-18T11:31:06.041+0000] {processor.py:186} INFO - Started process (PID=1538) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:06.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:31:06.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:06.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.115+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.124+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:06.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.233+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.246+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:31:06.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.232 seconds
[2025-07-18T11:31:36.385+0000] {processor.py:186} INFO - Started process (PID=1669) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:36.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:31:36.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.389+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:36.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.470+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:36.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:36.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.585+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:36.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.596+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:31:36.615+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.237 seconds
[2025-07-18T11:32:07.156+0000] {processor.py:186} INFO - Started process (PID=1800) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:32:07.158+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:32:07.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.160+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:32:07.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.238+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:07.246+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:32:07.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.349+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:07.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:32:07.383+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.233 seconds
[2025-07-18T11:32:37.602+0000] {processor.py:186} INFO - Started process (PID=1931) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:32:37.604+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:32:37.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.607+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:32:37.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.685+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:37.693+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:32:37.797+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.797+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:37.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.808+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:32:37.827+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.231 seconds
[2025-07-18T11:33:08.115+0000] {processor.py:186} INFO - Started process (PID=2062) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:33:08.116+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:33:08.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:33:08.196+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.196+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:08.205+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:33:08.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.311+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:08.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:08.323+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:33:08.345+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.236 seconds
[2025-07-18T11:33:38.571+0000] {processor.py:186} INFO - Started process (PID=2199) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:33:38.572+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:33:38.575+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.574+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:33:38.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.956+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:38.967+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:33:39.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.073+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:39.085+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:39.085+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:33:39.106+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.542 seconds
[2025-07-18T11:34:09.843+0000] {processor.py:186} INFO - Started process (PID=2352) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:34:09.844+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:34:09.847+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.846+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:34:10.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.267+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:10.275+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:34:10.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.376+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:10.519+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:10.519+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:34:10.540+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.703 seconds
[2025-07-18T11:34:40.904+0000] {processor.py:186} INFO - Started process (PID=2504) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:34:40.905+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:34:40.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.907+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:34:41.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:41.289+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:41.297+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:34:41.414+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:41.414+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:41.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:41.583+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:34:41.602+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.705 seconds
[2025-07-18T11:35:12.131+0000] {processor.py:186} INFO - Started process (PID=2657) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:35:12.132+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:35:12.135+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.135+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:35:12.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.598+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:12.607+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:35:12.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.899+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:12.910+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.910+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:35:12.929+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.805 seconds
[2025-07-18T11:35:43.235+0000] {processor.py:186} INFO - Started process (PID=2810) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:35:43.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:35:43.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:43.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:35:43.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:43.639+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:43.648+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:35:43.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:43.934+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:43.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:43.945+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:35:43.968+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.740 seconds
[2025-07-18T11:36:14.299+0000] {processor.py:186} INFO - Started process (PID=2963) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:36:14.300+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:36:14.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.302+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:36:14.691+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.691+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:14.699+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:36:14.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.972+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:14.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.983+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:36:15.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.712 seconds
[2025-07-18T11:36:45.424+0000] {processor.py:186} INFO - Started process (PID=3117) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:36:45.426+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:36:45.429+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.429+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:36:45.807+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.807+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:45.815+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:36:46.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:46.072+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:46.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:46.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:36:46.102+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.684 seconds
[2025-07-18T11:37:16.329+0000] {processor.py:186} INFO - Started process (PID=3271) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:37:16.331+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:37:16.333+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.333+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:37:16.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.743+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:16.755+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:37:17.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:17.038+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:17.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:17.053+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:37:17.074+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.756 seconds
[2025-07-18T11:37:48.954+0000] {processor.py:186} INFO - Started process (PID=3424) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:37:48.955+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:37:48.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.959+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:37:49.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.344+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:49.353+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:37:49.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.605+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:49.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:49.615+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:37:49.631+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.684 seconds
[2025-07-18T11:38:19.974+0000] {processor.py:186} INFO - Started process (PID=3577) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:38:19.975+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:38:19.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.977+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:38:20.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.576+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:20.582+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:38:20.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.686+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:20.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:20.699+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:38:20.718+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.751 seconds
[2025-07-18T11:38:50.826+0000] {processor.py:186} INFO - Started process (PID=3729) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:38:50.827+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:38:50.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.829+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:38:51.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.332+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:51.340+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:38:51.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.433+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:51.443+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:51.442+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:38:51.463+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.643 seconds
[2025-07-18T11:39:21.866+0000] {processor.py:186} INFO - Started process (PID=3883) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:39:21.868+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:39:21.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.870+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:39:22.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.379+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:22.386+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:39:22.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:22.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:22.498+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:39:22.517+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.657 seconds
[2025-07-18T11:39:52.808+0000] {processor.py:186} INFO - Started process (PID=4036) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:39:52.809+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:39:52.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.811+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:39:53.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.334+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:53.342+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:39:53.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.430+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:53.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:53.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:39:53.460+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.660 seconds
[2025-07-18T11:40:23.748+0000] {processor.py:186} INFO - Started process (PID=4189) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:40:23.749+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:40:23.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.752+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:40:24.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.281+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:24.289+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:40:24.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.398+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:24.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:24.408+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:40:24.429+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.688 seconds
[2025-07-18T11:40:55.129+0000] {processor.py:186} INFO - Started process (PID=4342) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:40:55.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:40:55.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:40:55.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.612+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:55.620+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:40:55.711+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.711+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:55.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.721+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:40:55.741+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.618 seconds
[2025-07-18T11:41:26.288+0000] {processor.py:186} INFO - Started process (PID=4501) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:41:26.289+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:41:26.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.291+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:41:26.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.811+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:26.817+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:41:26.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.908+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:26.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.918+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:41:26.936+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.654 seconds
[2025-07-18T11:41:57.116+0000] {processor.py:186} INFO - Started process (PID=4660) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:41:57.117+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:41:57.121+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.120+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:41:57.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.595+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:57.601+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:41:57.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.692+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:57.702+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.702+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:41:57.720+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.610 seconds
[2025-07-18T11:42:28.298+0000] {processor.py:186} INFO - Started process (PID=4825) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:42:28.300+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:42:28.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.303+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:42:28.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.823+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:28.830+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:42:28.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.924+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:28.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:42:28.952+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.659 seconds
[2025-07-18T11:42:59.084+0000] {processor.py:186} INFO - Started process (PID=4984) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:42:59.086+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:42:59.088+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:42:59.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.573+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:59.582+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:42:59.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.680+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:59.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.689+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:42:59.709+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.630 seconds
[2025-07-18T11:43:29.860+0000] {processor.py:186} INFO - Started process (PID=5143) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:43:29.861+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:43:29.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.864+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:43:30.397+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.397+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:30.405+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:43:30.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.501+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:30.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.512+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:43:30.532+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.678 seconds
[2025-07-18T11:44:01.248+0000] {processor.py:186} INFO - Started process (PID=5302) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:44:01.249+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:44:01.252+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.251+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:44:01.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.763+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:01.769+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:44:01.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.865+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:01.874+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.874+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:44:01.893+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.651 seconds
[2025-07-18T11:44:32.149+0000] {processor.py:186} INFO - Started process (PID=5461) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:44:32.150+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:44:32.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.153+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:44:32.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.658+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:32.666+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:44:32.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.787+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:32.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.798+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:44:32.823+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.681 seconds
[2025-07-18T11:45:03.139+0000] {processor.py:186} INFO - Started process (PID=5620) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:45:03.140+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:45:03.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.143+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:45:03.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.725+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:03.732+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:45:03.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.827+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:03.837+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.837+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:45:03.855+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.723 seconds
[2025-07-18T11:45:34.306+0000] {processor.py:186} INFO - Started process (PID=5778) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:45:34.307+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:45:34.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.310+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:45:34.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.892+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:34.899+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:45:35.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.011+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:35.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:35.020+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:45:35.036+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.738 seconds
[2025-07-18T11:46:05.555+0000] {processor.py:186} INFO - Started process (PID=5938) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:46:05.556+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:46:05.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.559+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:46:06.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.067+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:06.075+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:46:06.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:06.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:06.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:46:06.197+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.648 seconds
[2025-07-18T11:46:36.406+0000] {processor.py:186} INFO - Started process (PID=6097) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:46:36.407+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:46:36.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.410+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:46:36.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.908+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:36.915+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:46:37.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.007+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:37.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:37.018+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:46:37.041+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.641 seconds
[2025-07-18T11:47:07.797+0000] {processor.py:186} INFO - Started process (PID=6258) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:47:07.798+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:47:07.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.800+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:47:08.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:08.262+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:08.268+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:47:08.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:08.373+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:08.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:08.382+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:47:08.400+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.609 seconds
