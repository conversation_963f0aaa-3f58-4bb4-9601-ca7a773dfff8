[2025-07-18T11:25:57.435+0000] {processor.py:186} INFO - Started process (PID=226) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:25:57.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:25:57.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:25:57.529+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.529+0000] {cost_tracking.py:58} ERROR - О<PERSON><PERSON>бка подключения к Redis: <PERSON>rror 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:57.536+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:25:57.642+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.641+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.653+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.851+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.860+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.866+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.872+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.872+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.879+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_clarify_pipeline
[2025-07-18T11:25:57.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:57.890+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:57.891+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.891+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_clarify_pipeline
[2025-07-18T11:25:57.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:25:57.909+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.480 seconds
[2025-07-18T11:26:28.799+0000] {processor.py:186} INFO - Started process (PID=357) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:28.800+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:26:28.802+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.802+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:28.886+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.886+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.893+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:29.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.221+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:29.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:29.233+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:26:29.257+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.464 seconds
[2025-07-18T11:26:59.729+0000] {processor.py:186} INFO - Started process (PID=488) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:59.730+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:26:59.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.732+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:26:59.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.988+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.994+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:27:00.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.113+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:27:00.145+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.423 seconds
[2025-07-18T11:27:30.933+0000] {processor.py:186} INFO - Started process (PID=621) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:27:30.934+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:27:30.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.936+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:27:31.006+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.006+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:31.014+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:27:31.114+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.114+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:31.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:31.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:27:31.142+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.214 seconds
[2025-07-18T11:28:01.475+0000] {processor.py:186} INFO - Started process (PID=752) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:01.476+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:28:01.479+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.478+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:01.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.549+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.557+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:01.650+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.650+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.660+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:28:01.680+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.211 seconds
[2025-07-18T11:28:32.029+0000] {processor.py:186} INFO - Started process (PID=883) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:32.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:28:32.033+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:32.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.109+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:32.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:28:32.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.213+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:32.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:32.223+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:28:32.242+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.219 seconds
[2025-07-18T11:29:02.348+0000] {processor.py:186} INFO - Started process (PID=1014) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:02.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:29:02.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.351+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:02.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.424+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.432+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:02.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.538+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:29:02.557+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.215 seconds
[2025-07-18T11:29:33.062+0000] {processor.py:186} INFO - Started process (PID=1145) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:33.063+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:29:33.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:33.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.150+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:33.160+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:29:33.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.258+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.271+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:29:33.292+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.237 seconds
[2025-07-18T11:30:04.176+0000] {processor.py:186} INFO - Started process (PID=1276) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:04.177+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:30:04.180+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.179+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:04.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.260+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.269+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:04.366+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.366+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.377+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:30:04.397+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.228 seconds
[2025-07-18T11:30:35.379+0000] {processor.py:186} INFO - Started process (PID=1407) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:35.380+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:30:35.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.383+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:35.459+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.459+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:35.468+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:30:35.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.563+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:35.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:35.574+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:30:35.594+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.221 seconds
[2025-07-18T11:31:06.041+0000] {processor.py:186} INFO - Started process (PID=1538) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:06.042+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:31:06.044+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.044+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:06.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.115+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:06.124+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:06.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.233+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:06.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:06.246+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:31:06.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.232 seconds
[2025-07-18T11:31:36.385+0000] {processor.py:186} INFO - Started process (PID=1669) to work on /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:36.386+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_clarify_pipeline.py for tasks to queue
[2025-07-18T11:31:36.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.389+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:36.470+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.470+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:36.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_clarify_pipeline' retrieved from /opt/airflow/dags/chatgpt_clarify_pipeline.py
[2025-07-18T11:31:36.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.585+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:36.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.596+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_clarify_pipeline to None, run_after=None
[2025-07-18T11:31:36.615+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_clarify_pipeline.py took 0.237 seconds
