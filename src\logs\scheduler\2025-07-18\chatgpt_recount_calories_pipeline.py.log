[2025-07-18T11:26:00.419+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:00.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:26:00.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:00.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.503+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:00.510+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:00.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.749+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.761+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.769+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.779+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.787+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.795+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.802+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.803+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:00.815+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:00.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.816+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.817+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:26:00.840+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.427 seconds
[2025-07-18T11:26:31.529+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:31.530+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:26:31.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.533+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:31.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.782+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:31.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:31.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:31.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.889+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:26:31.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.388 seconds
[2025-07-18T11:27:02.453+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:02.454+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:27:02.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:02.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.562+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.573+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:02.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.688+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.698+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:27:02.716+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.272 seconds
[2025-07-18T11:27:33.141+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:33.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:27:33.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:33.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.235+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:33.242+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:33.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:27:33.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.247 seconds
[2025-07-18T11:28:03.664+0000] {processor.py:186} INFO - Started process (PID=812) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:03.665+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:28:03.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.667+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:03.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:03.744+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:03.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:03.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:28:03.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.212 seconds
[2025-07-18T11:28:34.013+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:34.014+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:28:34.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.016+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:34.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.095+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.104+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:34.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.214+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:28:34.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.225 seconds
[2025-07-18T11:29:04.982+0000] {processor.py:186} INFO - Started process (PID=1072) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:04.983+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:29:04.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:05.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.062+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:05.069+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:05.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:05.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.174+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:29:05.194+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.217 seconds
[2025-07-18T11:29:35.844+0000] {processor.py:186} INFO - Started process (PID=1203) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:35.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:29:35.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:35.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.935+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.944+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:36.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.055+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:36.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.065+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:29:36.086+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.249 seconds
[2025-07-18T11:30:06.268+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:06.270+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:30:06.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:06.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.350+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:06.358+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:06.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.476+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:06.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.493+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:30:06.517+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.257 seconds
[2025-07-18T11:30:37.356+0000] {processor.py:186} INFO - Started process (PID=1465) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:37.357+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:30:37.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:37.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.447+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.458+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:37.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.576+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:30:37.606+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.257 seconds
[2025-07-18T11:31:08.023+0000] {processor.py:186} INFO - Started process (PID=1598) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:08.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:31:08.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.026+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:08.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.101+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:08.112+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:08.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:08.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:31:08.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.236 seconds
[2025-07-18T11:31:38.715+0000] {processor.py:186} INFO - Started process (PID=1729) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:38.716+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:31:38.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.718+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:38.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.798+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:38.807+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:38.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.911+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:38.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.923+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:31:38.946+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.238 seconds
[2025-07-18T11:32:09.236+0000] {processor.py:186} INFO - Started process (PID=1860) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:32:09.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:32:09.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:32:09.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.317+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:09.326+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:32:09.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.441+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:09.456+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.456+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:32:09.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.249 seconds
[2025-07-18T11:32:39.580+0000] {processor.py:186} INFO - Started process (PID=1989) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:32:39.581+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:32:39.584+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.584+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:32:39.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.663+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:39.672+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:32:39.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.771+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:39.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:32:39.803+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.228 seconds
[2025-07-18T11:33:10.359+0000] {processor.py:186} INFO - Started process (PID=2120) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:33:10.360+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:33:10.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.363+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:33:10.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.436+0000] {cost_tracking.py:76} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:10.442+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:33:10.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.545+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:10.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.558+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:33:10.578+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.226 seconds
[2025-07-18T11:33:42.269+0000] {processor.py:186} INFO - Started process (PID=2270) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:33:42.271+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:33:42.273+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.273+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:33:42.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.637+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:42.646+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:33:42.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.761+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:42.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.774+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:33:42.795+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.533 seconds
[2025-07-18T11:34:13.601+0000] {processor.py:186} INFO - Started process (PID=2423) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:34:13.602+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:34:13.606+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.606+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:34:13.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.991+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:13.999+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:34:14.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.104+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:14.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.116+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:34:14.139+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.544 seconds
[2025-07-18T11:34:44.910+0000] {processor.py:186} INFO - Started process (PID=2576) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:34:44.911+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:34:44.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.913+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:34:45.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.277+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:45.287+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:34:45.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.386+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:45.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.398+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:34:45.418+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.514 seconds
[2025-07-18T11:35:16.658+0000] {processor.py:186} INFO - Started process (PID=2729) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:35:16.659+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:35:16.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.662+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:35:17.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.041+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:17.051+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:35:17.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.174+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:17.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.187+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:35:17.216+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.567 seconds
[2025-07-18T11:35:47.941+0000] {processor.py:186} INFO - Started process (PID=2882) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:35:47.942+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:35:47.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.945+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:35:48.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.350+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:48.357+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:35:48.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.461+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:48.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.472+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:35:48.494+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.561 seconds
[2025-07-18T11:36:19.051+0000] {processor.py:186} INFO - Started process (PID=3035) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:36:19.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:36:19.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.056+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:36:19.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.511+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:19.520+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:36:19.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.633+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:19.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.644+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:36:19.666+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.623 seconds
[2025-07-18T11:36:50.807+0000] {processor.py:186} INFO - Started process (PID=3188) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:36:50.809+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:36:50.811+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.811+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:36:51.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.247+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:51.255+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:36:51.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.391+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:51.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.406+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:36:51.586+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.785 seconds
[2025-07-18T11:37:22.007+0000] {processor.py:186} INFO - Started process (PID=3341) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:37:22.008+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:37:22.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.011+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:37:22.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.413+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:22.422+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:37:22.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.525+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:22.720+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.720+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:37:22.741+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.739 seconds
[2025-07-18T11:37:53.331+0000] {processor.py:186} INFO - Started process (PID=3494) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:37:53.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:37:53.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:37:53.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.706+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:53.714+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:37:53.998+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.998+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:54.012+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:54.012+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:37:54.034+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.709 seconds
[2025-07-18T11:38:24.711+0000] {processor.py:186} INFO - Started process (PID=3647) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:38:24.712+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:38:24.715+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:24.714+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:38:25.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:25.065+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:25.074+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:38:25.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:25.357+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:25.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:25.369+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:38:25.388+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.683 seconds
[2025-07-18T11:38:55.587+0000] {processor.py:186} INFO - Started process (PID=3800) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:38:55.588+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:38:55.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:55.590+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:38:56.069+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.069+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:56.077+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:38:56.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.169+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:56.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:56.180+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:38:56.202+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.621 seconds
[2025-07-18T11:39:26.857+0000] {processor.py:186} INFO - Started process (PID=3953) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:39:26.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:39:26.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.860+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:39:27.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:27.394+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:27.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:39:27.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:27.510+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:27.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:27.521+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:39:27.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.692 seconds
[2025-07-18T11:39:57.706+0000] {processor.py:186} INFO - Started process (PID=4112) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:39:57.707+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:39:57.709+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:57.708+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:39:58.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:58.232+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:58.241+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:39:58.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:58.340+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:58.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:58.351+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:39:58.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.673 seconds
[2025-07-18T11:40:28.756+0000] {processor.py:186} INFO - Started process (PID=4265) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:40:28.756+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:40:28.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:28.760+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:40:29.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:29.324+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:29.331+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:40:29.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:29.435+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:29.448+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:29.448+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:40:29.472+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.723 seconds
[2025-07-18T11:40:59.726+0000] {processor.py:186} INFO - Started process (PID=4424) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:40:59.727+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:40:59.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:59.729+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:41:00.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:00.262+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:00.269+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:41:00.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:00.371+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:00.382+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:00.381+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:41:00.401+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.681 seconds
[2025-07-18T11:41:30.593+0000] {processor.py:186} INFO - Started process (PID=4583) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:41:30.594+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:41:30.596+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:30.596+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:41:31.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:31.110+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:31.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:41:31.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:31.228+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:31.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:31.238+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:41:31.257+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.671 seconds
[2025-07-18T11:42:01.967+0000] {processor.py:186} INFO - Started process (PID=4743) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:42:01.969+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:42:01.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:01.971+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:42:02.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:02.469+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:02.475+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:42:02.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:02.562+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:02.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:02.572+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:42:02.589+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.628 seconds
[2025-07-18T11:42:32.737+0000] {processor.py:186} INFO - Started process (PID=4902) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:42:32.739+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:42:32.741+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.741+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:42:33.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:33.225+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:33.231+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:42:33.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:33.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:33.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:33.328+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:42:33.345+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.614 seconds
[2025-07-18T11:43:03.541+0000] {processor.py:186} INFO - Started process (PID=5066) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:43:03.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:43:03.547+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:43:04.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:04.060+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:04.066+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:43:04.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:04.158+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:04.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:04.168+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:43:04.187+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.652 seconds
[2025-07-18T11:43:34.768+0000] {processor.py:186} INFO - Started process (PID=5225) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:43:34.770+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:43:34.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.772+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:43:35.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:35.265+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:35.272+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:43:35.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:35.365+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:35.375+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:35.374+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:43:35.395+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.632 seconds
[2025-07-18T11:44:05.556+0000] {processor.py:186} INFO - Started process (PID=5384) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:44:05.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:44:05.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:05.561+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:44:06.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:06.060+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:06.068+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:44:06.160+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:06.160+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:06.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:06.171+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:44:06.189+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.640 seconds
[2025-07-18T11:44:36.622+0000] {processor.py:186} INFO - Started process (PID=5542) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:44:36.623+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:44:36.626+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:36.626+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:44:37.191+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:37.191+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:37.199+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:44:37.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:37.303+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:37.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:37.313+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:44:37.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.716 seconds
[2025-07-18T11:45:07.673+0000] {processor.py:186} INFO - Started process (PID=5701) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:45:07.674+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:45:07.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:07.676+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:45:08.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:08.178+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:08.185+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:45:08.277+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:08.277+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:08.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:08.286+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:45:08.307+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.640 seconds
[2025-07-18T11:45:38.937+0000] {processor.py:186} INFO - Started process (PID=5860) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:45:38.939+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:45:38.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.941+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:45:39.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:39.437+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:39.445+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:45:39.543+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:39.543+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:39.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:39.553+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:45:39.571+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.640 seconds
[2025-07-18T11:46:10.047+0000] {processor.py:186} INFO - Started process (PID=6020) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:46:10.048+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:46:10.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:10.052+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:46:10.660+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:10.660+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:10.668+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:46:10.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:10.804+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:10.815+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:10.814+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:46:10.839+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.802 seconds
[2025-07-18T11:46:41.025+0000] {processor.py:186} INFO - Started process (PID=6178) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:46:41.026+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:46:41.028+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:41.028+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:46:41.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:41.546+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:41.552+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:46:41.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:41.657+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:41.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:41.668+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:46:41.693+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.674 seconds
[2025-07-18T11:47:12.334+0000] {processor.py:186} INFO - Started process (PID=6337) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:47:12.335+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:47:12.338+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:12.337+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:47:12.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:12.841+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:12.848+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:47:12.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:12.968+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:12.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:12.979+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:47:12.996+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.669 seconds
[2025-07-18T11:47:43.404+0000] {processor.py:186} INFO - Started process (PID=6497) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:47:43.405+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:47:43.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:43.408+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:47:43.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:43.918+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:43.923+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:47:44.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:44.013+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:44.022+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:44.022+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:47:44.040+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.643 seconds
[2025-07-18T11:48:14.390+0000] {processor.py:186} INFO - Started process (PID=6655) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:48:14.391+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:48:14.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:14.395+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:48:14.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:14.897+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:14.904+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:48:15.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:15.007+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:15.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:15.017+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:48:15.037+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.653 seconds
[2025-07-18T11:48:46.091+0000] {processor.py:186} INFO - Started process (PID=6815) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:48:46.093+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:48:46.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:46.095+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:48:46.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:46.581+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:46.591+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:48:46.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:46.682+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:46.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:46.692+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:48:46.710+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.625 seconds
[2025-07-18T11:49:17.055+0000] {processor.py:186} INFO - Started process (PID=6974) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:49:17.057+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:49:17.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:17.060+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:49:17.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:17.599+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:17.606+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:49:17.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:17.704+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:17.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:17.714+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:49:17.734+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.685 seconds
[2025-07-18T11:49:48.111+0000] {processor.py:186} INFO - Started process (PID=7133) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:49:48.112+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:49:48.115+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:48.115+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:49:48.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:48.630+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:48.638+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:49:48.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:48.751+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:48.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:48.764+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:49:48.786+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.682 seconds
[2025-07-18T11:50:19.289+0000] {processor.py:186} INFO - Started process (PID=7294) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:50:19.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:50:19.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:19.292+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:50:19.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:19.813+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:19.822+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:50:19.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:19.918+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:19.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:19.930+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:50:19.951+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.667 seconds
[2025-07-18T11:50:50.244+0000] {processor.py:186} INFO - Started process (PID=7451) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:50:50.245+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:50:50.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:50.247+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:50:50.730+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:50.730+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:50.737+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:50:50.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:50.849+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:50.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:50.862+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:50:50.884+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.646 seconds
