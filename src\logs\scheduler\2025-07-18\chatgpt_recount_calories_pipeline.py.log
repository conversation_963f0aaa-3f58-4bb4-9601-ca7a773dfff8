[2025-07-18T11:26:00.419+0000] {processor.py:186} INFO - Started process (PID=286) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:00.420+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:26:00.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.423+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:00.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.503+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:00.510+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:00.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.749+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.761+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.769+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.769+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.779+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.787+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.795+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.802+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.803+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.803+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:00.815+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:00.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.816+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_recount_calories_pipeline
[2025-07-18T11:26:00.817+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.817+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:26:00.840+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.427 seconds
[2025-07-18T11:26:31.529+0000] {processor.py:186} INFO - Started process (PID=419) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:31.530+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:26:31.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.533+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:31.783+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.782+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:31.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:26:31.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.877+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:31.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.889+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:26:31.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.388 seconds
[2025-07-18T11:27:02.453+0000] {processor.py:186} INFO - Started process (PID=548) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:02.454+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:27:02.457+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.456+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:02.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.562+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.573+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:02.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.688+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.698+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:27:02.716+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.272 seconds
[2025-07-18T11:27:33.141+0000] {processor.py:186} INFO - Started process (PID=681) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:33.142+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:27:33.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.145+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:33.235+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.235+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:33.242+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:27:33.348+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.348+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.360+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:27:33.380+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.247 seconds
[2025-07-18T11:28:03.664+0000] {processor.py:186} INFO - Started process (PID=812) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:03.665+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:28:03.667+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.667+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:03.737+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.737+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:03.744+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:03.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.840+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:03.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:28:03.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.212 seconds
[2025-07-18T11:28:34.013+0000] {processor.py:186} INFO - Started process (PID=941) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:34.014+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:28:34.016+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.016+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:34.096+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.095+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.104+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:28:34.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.204+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.214+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:28:34.232+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.225 seconds
[2025-07-18T11:29:04.982+0000] {processor.py:186} INFO - Started process (PID=1072) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:04.983+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:29:04.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:05.063+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.062+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:05.069+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:05.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.163+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:05.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.174+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:29:05.194+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.217 seconds
[2025-07-18T11:29:35.844+0000] {processor.py:186} INFO - Started process (PID=1203) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:35.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:29:35.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:35.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.935+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.944+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:29:36.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.055+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:36.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.065+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:29:36.086+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.249 seconds
[2025-07-18T11:30:06.268+0000] {processor.py:186} INFO - Started process (PID=1334) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:06.270+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:30:06.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:06.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.350+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:06.358+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:06.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.476+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:06.493+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.493+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:30:06.517+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.257 seconds
[2025-07-18T11:30:37.356+0000] {processor.py:186} INFO - Started process (PID=1465) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:37.357+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:30:37.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.359+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:37.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.447+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.458+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:30:37.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.576+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.586+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:30:37.606+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.257 seconds
[2025-07-18T11:31:08.023+0000] {processor.py:186} INFO - Started process (PID=1598) to work on /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:08.024+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_recount_calories_pipeline.py for tasks to queue
[2025-07-18T11:31:08.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.026+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:08.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.101+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:08.112+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_recount_calories_pipeline' retrieved from /opt/airflow/dags/chatgpt_recount_calories_pipeline.py
[2025-07-18T11:31:08.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:08.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_recount_calories_pipeline to None, run_after=None
[2025-07-18T11:31:08.253+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_recount_calories_pipeline.py took 0.236 seconds
