[2025-07-18T11:25:56.776+0000] {processor.py:186} INFO - Started process (PID=206) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:25:56.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:25:56.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:25:56.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.795+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:25:56.798+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:25:56.824+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.055 seconds
[2025-07-18T11:26:27.953+0000] {processor.py:186} INFO - Started process (PID=337) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:27.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:26:27.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:27.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.968+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:26:27.971+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:27.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:26:58.822+0000] {processor.py:186} INFO - Started process (PID=465) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:58.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:26:58.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:58.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.839+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:26:58.840+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:58.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:27:29.277+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:27:29.278+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:27:29.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.280+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:27:29.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.293+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:27:29.295+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:27:29.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:28:00.106+0000] {processor.py:186} INFO - Started process (PID=727) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:00.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:28:00.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.109+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:00.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.121+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:28:00.123+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:00.141+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T11:28:30.585+0000] {processor.py:186} INFO - Started process (PID=858) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:30.586+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:28:30.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.589+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:30.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.603+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:28:30.605+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:30.626+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.048 seconds
[2025-07-18T11:29:00.953+0000] {processor.py:186} INFO - Started process (PID=989) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:00.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:29:00.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:00.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.969+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:29:00.971+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:00.989+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:29:31.629+0000] {processor.py:186} INFO - Started process (PID=1120) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:31.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:29:31.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:31.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.648+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:29:31.650+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:31.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.054 seconds
[2025-07-18T11:30:02.652+0000] {processor.py:186} INFO - Started process (PID=1254) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:02.653+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:30:02.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.655+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:02.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.666+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:30:02.668+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:02.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T11:30:33.226+0000] {processor.py:186} INFO - Started process (PID=1382) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:33.227+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:30:33.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.230+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:33.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.243+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:30:33.245+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:33.265+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.046 seconds
[2025-07-18T11:31:03.838+0000] {processor.py:186} INFO - Started process (PID=1513) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:03.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:31:03.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:03.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.861+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:31:03.864+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:03.892+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.062 seconds
[2025-07-18T11:31:34.986+0000] {processor.py:186} INFO - Started process (PID=1649) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:34.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:31:34.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:35.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.002+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:31:35.004+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:35.024+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T11:32:05.588+0000] {processor.py:186} INFO - Started process (PID=1778) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:32:05.589+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:32:05.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.592+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:32:05.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.603+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:32:05.605+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:32:05.627+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:32:35.771+0000] {processor.py:186} INFO - Started process (PID=1904) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:32:35.772+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:32:35.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.774+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:32:35.787+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.786+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:32:35.787+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:32:35.807+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:33:06.211+0000] {processor.py:186} INFO - Started process (PID=2032) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:33:06.212+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:33:06.214+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.214+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:33:06.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.227+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:33:06.229+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:33:06.247+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:33:37.144+0000] {processor.py:186} INFO - Started process (PID=2166) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:33:37.145+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:33:37.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.147+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:33:37.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.162+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:33:37.164+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:33:37.184+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.046 seconds
[2025-07-18T11:34:08.216+0000] {processor.py:186} INFO - Started process (PID=2323) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:34:08.218+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:34:08.223+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.222+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:34:08.248+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:08.246+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:34:08.248+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:34:08.273+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.065 seconds
[2025-07-18T11:34:39.112+0000] {processor.py:186} INFO - Started process (PID=2470) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:34:39.113+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:34:39.117+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.116+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:34:39.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.136+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:34:39.138+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:34:39.165+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.061 seconds
[2025-07-18T11:35:10.416+0000] {processor.py:186} INFO - Started process (PID=2623) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:35:10.418+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:35:10.421+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.420+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:35:10.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.436+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:35:10.440+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:35:10.459+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.051 seconds
[2025-07-18T11:35:41.347+0000] {processor.py:186} INFO - Started process (PID=2778) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:35:41.349+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:35:41.351+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.351+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:35:41.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.364+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:35:41.366+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:35:41.385+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:36:12.635+0000] {processor.py:186} INFO - Started process (PID=2931) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:36:12.636+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:36:12.639+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.639+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:36:12.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.652+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:36:12.655+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:36:12.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.046 seconds
[2025-07-18T11:36:43.505+0000] {processor.py:186} INFO - Started process (PID=3084) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:36:43.507+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:36:43.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.510+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:36:43.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.524+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:36:43.526+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:36:43.548+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.051 seconds
[2025-07-18T11:37:14.421+0000] {processor.py:186} INFO - Started process (PID=3236) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:37:14.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:37:14.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:37:14.442+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.440+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:37:14.442+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:37:14.462+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.048 seconds
[2025-07-18T11:37:45.542+0000] {processor.py:186} INFO - Started process (PID=3391) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:37:45.543+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:37:45.546+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.546+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:37:45.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.561+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:37:45.563+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:37:45.584+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.048 seconds
[2025-07-18T11:38:16.735+0000] {processor.py:186} INFO - Started process (PID=3544) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:38:16.736+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:38:16.739+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:16.738+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:38:16.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:16.753+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:38:16.755+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:38:16.776+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T11:38:48.151+0000] {processor.py:186} INFO - Started process (PID=3697) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:38:48.152+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:38:48.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:48.154+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:38:48.169+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:48.168+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:38:48.169+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:38:48.189+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T11:39:19.035+0000] {processor.py:186} INFO - Started process (PID=3848) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:39:19.036+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:39:19.039+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:19.038+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:39:19.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:19.052+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:39:19.054+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:39:19.072+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T11:39:49.968+0000] {processor.py:186} INFO - Started process (PID=4003) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:39:49.969+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:39:49.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.971+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:39:49.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.987+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:39:49.989+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:39:50.007+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:40:20.831+0000] {processor.py:186} INFO - Started process (PID=4156) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:40:20.832+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:40:20.835+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.835+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:40:20.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.848+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:40:20.850+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:40:20.870+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:40:52.369+0000] {processor.py:186} INFO - Started process (PID=4309) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:40:52.370+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:40:52.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:52.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:40:52.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:52.388+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:40:52.390+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:40:52.408+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:41:23.498+0000] {processor.py:186} INFO - Started process (PID=4468) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:41:23.498+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:41:23.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:23.501+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:41:23.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:23.513+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:41:23.515+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:41:23.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:41:54.318+0000] {processor.py:186} INFO - Started process (PID=4627) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:41:54.319+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:41:54.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:54.321+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:41:54.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:54.334+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:41:54.336+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:41:54.356+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
[2025-07-18T11:42:25.489+0000] {processor.py:186} INFO - Started process (PID=4786) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:42:25.490+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:42:25.492+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:25.492+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:42:25.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:25.503+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:42:25.505+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:42:25.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T11:42:56.265+0000] {processor.py:186} INFO - Started process (PID=4945) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:42:56.266+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:42:56.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:56.268+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:42:56.284+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:56.283+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:42:56.284+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:42:56.304+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:43:27.074+0000] {processor.py:186} INFO - Started process (PID=5104) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:43:27.075+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:43:27.077+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:27.077+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:43:27.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:27.089+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:43:27.090+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:43:27.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.039 seconds
[2025-07-18T11:43:58.434+0000] {processor.py:186} INFO - Started process (PID=5269) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:43:58.435+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:43:58.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:58.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:43:58.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:58.450+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:43:58.451+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:43:58.470+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:44:29.340+0000] {processor.py:186} INFO - Started process (PID=5428) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:44:29.341+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:44:29.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:29.344+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:44:29.359+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:29.358+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:44:29.360+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:44:29.381+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.047 seconds
[2025-07-18T11:45:00.184+0000] {processor.py:186} INFO - Started process (PID=5587) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:45:00.186+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:45:00.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:00.188+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:45:00.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:00.202+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:45:00.204+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:45:00.223+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:45:31.722+0000] {processor.py:186} INFO - Started process (PID=5746) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:45:31.724+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:45:31.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:31.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:45:31.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:31.742+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:45:31.744+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:45:31.767+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.053 seconds
[2025-07-18T11:46:03.730+0000] {processor.py:186} INFO - Started process (PID=5905) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:46:03.731+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:46:03.733+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:03.733+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:46:03.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:03.744+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:46:03.747+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:46:03.765+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T11:46:34.538+0000] {processor.py:186} INFO - Started process (PID=6064) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:46:34.539+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:46:34.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:34.541+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:46:34.556+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:34.555+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:46:34.557+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:46:34.578+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.046 seconds
[2025-07-18T11:47:05.582+0000] {processor.py:186} INFO - Started process (PID=6223) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:47:05.583+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:47:05.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:05.585+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:47:05.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:05.598+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:47:05.600+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:47:05.620+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
