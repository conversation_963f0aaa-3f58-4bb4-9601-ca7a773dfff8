[2025-07-18T11:25:56.776+0000] {processor.py:186} INFO - Started process (PID=206) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:25:56.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:25:56.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.779+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:25:56.796+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.795+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:25:56.798+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:25:56.824+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.055 seconds
[2025-07-18T11:26:27.953+0000] {processor.py:186} INFO - Started process (PID=337) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:27.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:26:27.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:27.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.968+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:26:27.971+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:27.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.045 seconds
[2025-07-18T11:26:58.822+0000] {processor.py:186} INFO - Started process (PID=465) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:58.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:26:58.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.826+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:58.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.839+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:26:58.840+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:26:58.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:27:29.277+0000] {processor.py:186} INFO - Started process (PID=596) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:27:29.278+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:27:29.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.280+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:27:29.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:29.293+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:27:29.295+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:27:29.314+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:28:00.106+0000] {processor.py:186} INFO - Started process (PID=727) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:00.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:28:00.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.109+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:00.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:00.121+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:28:00.123+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:00.141+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.040 seconds
[2025-07-18T11:28:30.585+0000] {processor.py:186} INFO - Started process (PID=858) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:30.586+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:28:30.589+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.589+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:30.604+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.603+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:28:30.605+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:28:30.626+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.048 seconds
[2025-07-18T11:29:00.953+0000] {processor.py:186} INFO - Started process (PID=989) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:00.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:29:00.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:00.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.969+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:29:00.971+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:00.989+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.042 seconds
[2025-07-18T11:29:31.629+0000] {processor.py:186} INFO - Started process (PID=1120) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:31.631+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:29:31.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.634+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:31.649+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.648+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:29:31.650+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:29:31.673+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.054 seconds
[2025-07-18T11:30:02.652+0000] {processor.py:186} INFO - Started process (PID=1254) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:02.653+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:30:02.655+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.655+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:02.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.666+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:30:02.668+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:02.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.041 seconds
[2025-07-18T11:30:33.226+0000] {processor.py:186} INFO - Started process (PID=1382) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:33.227+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:30:33.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.230+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:33.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:33.243+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:30:33.245+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:30:33.265+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.046 seconds
[2025-07-18T11:31:03.838+0000] {processor.py:186} INFO - Started process (PID=1513) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:03.841+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:31:03.844+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:03.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.861+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:31:03.864+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:03.892+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.062 seconds
[2025-07-18T11:31:34.986+0000] {processor.py:186} INFO - Started process (PID=1649) to work on /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:34.987+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py for tasks to queue
[2025-07-18T11:31:34.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.989+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:35.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:35.002+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_challenge_ask_pipeline.py", line 103
    from cost_tracking import track_openai_cost
SyntaxError: expected 'except' or 'finally' block
[2025-07-18T11:31:35.004+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py
[2025-07-18T11:31:35.024+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_challenge_ask_pipeline.py took 0.044 seconds
