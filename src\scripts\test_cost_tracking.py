#!/usr/bin/env python3
"""
Тестовый скрипт для проверки функциональности cost_tracking
"""

import sys
import os
import json
import redis
from datetime import datetime, timezone
from pathlib import Path

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent.parent / "dags"))

from cost_tracking import (
    CostTracker,
    OperationType,
    track_openai_cost,
    export_cost_data_to_csv,
    PIPELINE_RUSSIAN_NAMES
)

def create_test_data():
    """Создает тестовые данные в Redis для проверки экспорта"""
    print("🔧 Создание тестовых данных...")
    
    # Подключение к Redis
    try:
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        r.ping()
        print("✅ Подключение к Redis успешно")
    except Exception as e:
        print(f"❌ Ошибка подключения к Redis: {e}")
        return False
    
    # Создаем экземпляр CostTracker
    cost_tracker = CostTracker()
    
    # Тестовые данные для разных пайплайнов
    test_pipelines = [
        {
            'operation_type': OperationType.RECOMMENDATION_GENERATION,
            'dag_id': 'chatgpt_message_recommendation_pipeline',
            'tokens_input': 3843,
            'tokens_output': 2934,
            'cost_input': 0.009607,
            'cost_output': 0.02934
        },
        {
            'operation_type': OperationType.GENERAL_CHAT,
            'dag_id': 'chatgpt_message_pipeline',
            'tokens_input': 2355,
            'tokens_output': 2600,
            'cost_input': 0.005887,
            'cost_output': 0.026
        },
        {
            'operation_type': OperationType.RISK_ASSESSMENT,
            'dag_id': 'chatgpt_message_risk_pipeline',
            'tokens_input': 2826,
            'tokens_output': 1989,
            'cost_input': 0.007065,
            'cost_output': 0.01989
        },
        {
            'operation_type': OperationType.IMAGE_ANALYSIS,
            'dag_id': 'chatgpt_image_pipeline',
            'tokens_input': 21890,
            'tokens_output': 1707,
            'cost_input': 0.179725,
            'cost_output': 0.01707
        }
    ]
    
    # Добавляем тестовые данные
    for i, pipeline_data in enumerate(test_pipelines):
        token_usage = {
            'tokens_input': pipeline_data['tokens_input'],
            'tokens_output': pipeline_data['tokens_output'],
            'total_tokens': pipeline_data['tokens_input'] + pipeline_data['tokens_output'],
            'cost_input': pipeline_data['cost_input'],
            'cost_output': pipeline_data['cost_output'],
            'total_cost': pipeline_data['cost_input'] + pipeline_data['cost_output'],
            'model': 'gpt-4o',
            'timestamp': datetime.now(timezone.utc).isoformat()
        }
        
        success = cost_tracker.track_operation_cost(
            operation_type=pipeline_data['operation_type'],
            user_id=1001,
            session_id=f"test_session_{i}",
            token_usage=token_usage,
            additional_metadata={
                'dag_id': pipeline_data['dag_id'],
                'task_id': 'test_task',
                'test_data': True
            }
        )
        
        if success:
            print(f"✅ Добавлены тестовые данные для {pipeline_data['dag_id']}")
        else:
            print(f"❌ Ошибка добавления данных для {pipeline_data['dag_id']}")
    
    print(f"🎯 Создано {len(test_pipelines)} тестовых записей")
    return True

def test_csv_export():
    """Тестирует экспорт данных в CSV"""
    print("\n📊 Тестирование экспорта в CSV...")
    
    try:
        csv_file = export_cost_data_to_csv(
            output_file="test_cost_export.csv",
            days=1,  # Только сегодняшние данные
            redis_host="localhost",
            redis_port=6379,
            redis_db=0
        )
        
        print(f"✅ CSV файл создан: {csv_file}")
        
        # Проверяем содержимое файла
        if Path(csv_file).exists():
            with open(csv_file, 'r', encoding='utf-8') as f:
                content = f.read()
                print(f"📄 Размер файла: {len(content)} символов")
                
                # Показываем первые несколько строк
                lines = content.split('\n')[:5]
                print("📋 Первые строки файла:")
                for line in lines:
                    print(f"   {line}")
                    
            return True
        else:
            print("❌ CSV файл не найден")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка экспорта в CSV: {e}")
        return False

def test_pipeline_mapping():
    """Тестирует маппинг пайплайнов на русские названия"""
    print("\n🗺️ Тестирование маппинга пайплайнов...")
    
    test_pipelines = [
        'chatgpt_message_recommendation_pipeline',
        'chatgpt_message_pipeline',
        'chatgpt_message_risk_pipeline',
        'chatgpt_image_pipeline',
        'unknown_pipeline'
    ]
    
    for pipeline in test_pipelines:
        russian_name = PIPELINE_RUSSIAN_NAMES.get(pipeline, pipeline)
        print(f"   {pipeline} -> {russian_name}")
    
    print("✅ Маппинг пайплайнов протестирован")
    return True

def test_track_openai_cost():
    """Тестирует функцию track_openai_cost"""
    print("\n🤖 Тестирование track_openai_cost...")
    
    # Симулируем ответ OpenAI
    mock_response_data = {
        'usage': {
            'prompt_tokens': 1000,
            'completion_tokens': 500,
            'total_tokens': 1500
        }
    }
    
    mock_context = {
        'dag_id': 'chatgpt_message_recommendation_pipeline',
        'task_id': 'generate_openai_response',
        'user_id': 1002
    }
    
    try:
        result = track_openai_cost(
            response_data=mock_response_data,
            context=mock_context
        )
        
        print(f"✅ track_openai_cost выполнена успешно")
        print(f"   Входные токены: {result.get('input_tokens', 0)}")
        print(f"   Выходные токены: {result.get('output_tokens', 0)}")
        print(f"   Общая стоимость: ${result.get('total_cost', 0):.6f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Ошибка в track_openai_cost: {e}")
        return False

def cleanup_test_data():
    """Очищает тестовые данные"""
    print("\n🧹 Очистка тестовых данных...")
    
    try:
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # Находим все ключи cost_tracking
        keys = r.keys("cost_tracking:*")
        
        # Удаляем только тестовые данные
        deleted_count = 0
        for key in keys:
            try:
                data_str = r.get(key)
                if data_str:
                    data = json.loads(data_str)
                    additional_metadata = data.get('additional_metadata', {})
                    if additional_metadata.get('test_data'):
                        r.delete(key)
                        deleted_count += 1
            except:
                continue
        
        print(f"✅ Удалено {deleted_count} тестовых записей")
        
        # Удаляем тестовый CSV файл
        test_csv = Path("test_cost_export.csv")
        if test_csv.exists():
            test_csv.unlink()
            print("✅ Тестовый CSV файл удален")
            
        return True
        
    except Exception as e:
        print(f"❌ Ошибка очистки: {e}")
        return False

def main():
    """Основная функция тестирования"""
    print("🚀 Запуск тестирования системы cost_tracking\n")
    
    tests = [
        ("Создание тестовых данных", create_test_data),
        ("Тестирование маппинга пайплайнов", test_pipeline_mapping),
        ("Тестирование track_openai_cost", test_track_openai_cost),
        ("Тестирование экспорта в CSV", test_csv_export),
        ("Очистка тестовых данных", cleanup_test_data)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🧪 {test_name}")
        print('='*50)
        
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - ПРОЙДЕН")
            else:
                failed += 1
                print(f"❌ {test_name} - ПРОВАЛЕН")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} - ОШИБКА: {e}")
    
    print(f"\n{'='*50}")
    print(f"📊 РЕЗУЛЬТАТЫ ТЕСТИРОВАНИЯ")
    print('='*50)
    print(f"✅ Пройдено: {passed}")
    print(f"❌ Провалено: {failed}")
    print(f"📈 Успешность: {passed/(passed+failed)*100:.1f}%")
    
    if failed == 0:
        print("\n🎉 Все тесты пройдены успешно!")
        return True
    else:
        print(f"\n⚠️ Обнаружены проблемы в {failed} тестах")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
