import asyncio
import redis.asyncio as aioredis
import json
import httpx
client = httpx.AsyncClient(timeout=10.0)

API_BASE_URL = "https://root.biome-dev-api.work/api"
token="12c082765fbe925e59e0c4496cc395398bf9f800787dba680f65f195e9dd38196b217bc693049db8b15240b7048c5860bcd74dec65fd585ab06c70b0ecdb6aa45ba62e7bb7f15edc678920b18cadefdcd36b8019205434d235a92ab1cd70882023ae55ac6a7c236e091cf206519738ab4b090964ff73c036351a400f6b83e6b8"

async def upload_food(user_id, bearer_token, name, description, proteins, fats, carbohydrates, addedAt):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    calories = (proteins * 4) + (carbohydrates * 4) + (fats * 9)
    url = f"{API_BASE_URL}/foods"
    payload = {
        "data": {
            "name": name,
            "description": description,
            "calories": calories,
            "proteins": proteins,
            "fats": fats,
            "carbohydrates": carbohydrates,
            "addedAt": addedAt,
            "user": user_id,
        }
    }
    response = await client.post(url, headers=headers, json=payload)
    if response.status_code == 201:
        print(f"Food uploaded successfully: {response.json()}")
    else:
        print(f"Failed to upload food: {response.status_code}, {response.text}")

foods = [
  {
    "name": "Овощной суп с фасолью",
    "description": "Овощной бульон с картофелем, морковью, луком и белой фасолью.",
    "proteins": 10,
    "fats": 3,
    "carbohydrates": 19,
    "addedAt": "2025-06-13T11:05:00.663"
  },
  {
    "name": "Сырники из творога",
    "description": "Жареные лепешки из творога с яйцом и мукой. Подаются со сметаной.",
    "proteins": 13,
    "fats": 10,
    "carbohydrates": 20,
    "addedAt": "2025-06-13T11:05:00.663"
  },
  {
    "name": "Куриный суп с лапшой",
    "description": "Легкий бульон с курицей, лапшой, морковью и зеленью.",
    "proteins": 14,
    "fats": 7,
    "carbohydrates": 15,
    "addedAt": "2025-06-13T11:05:00.663"
  },
  {
    "name": "Тост с авокадо и яйцом",
    "description": "Обжаренный тост с пюре из авокадо и яйцом пашот. Популярный завтрак.",
    "proteins": 11,
    "fats": 17,
    "carbohydrates": 16,
    "addedAt": "2025-06-14T11:05:00.663"
  },
  {
    "name": "Чечевичный суп",
    "description": "Питательный суп из красной чечевицы с овощами и специями.",
    "proteins": 13,
    "fats": 5,
    "carbohydrates": 22,
    "addedAt": "2025-06-14T11:05:00.663"
  },
  {
    "name": "Банановые панкейки",
    "description": "Панкейки из банана, овсянки и яйца. Подаются с медом или ягодами.",
    "proteins": 9,
    "fats": 6,
    "carbohydrates": 28,
    "addedAt": "2025-06-14T11:05:00.663"
  },
{
    "name": "Куриная грудка с рисом и брокколи",
    "description": "Отварная куриная грудка с гарниром из риса и отварной брокколи. Легкое и сбалансированное блюдо.",
    "proteins": 35,
    "fats": 8,
    "carbohydrates": 40,
    "addedAt": "2025-06-15T11:05:00.663"
  },
  {
    "name": "Овсяная каша с бананом и медом",
    "description": "Теплая овсяная каша на воде с кусочками банана и чайной ложкой меда. Простой и полезный завтрак.",
    "proteins": 6,
    "fats": 4,
    "carbohydrates": 42,
    "addedAt": "2025-06-15T11:05:00.663"

  },
  {
    "name": "Греческий салат",
    "description": "Томаты, огурцы, оливки, лук, болгарский перец, сыр фета и заправка из оливкового масла.",
    "proteins": 7,
    "fats": 12,
    "carbohydrates": 8,
    "addedAt": "2025-06-15T11:05:00.663"
  },
{
    "name": "Паста с томатным соусом и сыром",
    "description": "Спагетти с томатным соусом и тертым сыром. Сытное блюдо для обеда или ужина.",
    "proteins": 14,
    "fats": 10,
    "carbohydrates": 55,
    "addedAt": "2025-06-16T11:05:00.663"
  },
  {
    "name": "Омлет с сыром и зеленью",
    "description": "Два яйца, немного тертого сыра и рубленая зелень. Готовится на сковороде без масла.",
    "proteins": 15,
    "fats": 14,
    "carbohydrates": 2,
    "addedAt": "2025-06-16T11:05:00.663"
  },
  {
    "name": "Смузи из клубники и банана",
    "description": "Напиток на основе молока, клубники и банана. Отлично подходит для перекуса.",
    "proteins": 5,
    "fats": 2,
    "carbohydrates": 26,
    "addedAt": "2025-06-16T11:05:00.663"
  },
{
    "name": "Паста с томатным соусом и сыром",
    "description": "Спагетти с томатным соусом и тертым сыром. Сытное блюдо для обеда или ужина.",
    "proteins": 14,
    "fats": 10,
    "carbohydrates": 55,
    "addedAt": "2025-06-17T11:05:00.663"
  },
  {
    "name": "Омлет с сыром и зеленью",
    "description": "Два яйца, немного тертого сыра и рубленая зелень. Готовится на сковороде без масла.",
    "proteins": 15,
    "fats": 14,
    "carbohydrates": 2,
    "addedAt": "2025-06-17T11:05:00.663"
  },
  {
    "name": "Смузи из клубники и банана",
    "description": "Напиток на основе молока, клубники и банана. Отлично подходит для перекуса.",
    "proteins": 5,
    "fats": 2,
    "carbohydrates": 26,
    "addedAt": "2025-06-17T11:05:00.663"
  },
  {
    "name": "Гречка с грибами",
    "description": "Отварная гречка с жареными шампиньонами и луком. Постное и питательное блюдо.",
    "proteins": 9,
    "fats": 5,
    "carbohydrates": 32,
    "addedAt": "2025-06-18T11:05:00.663"
  },
  {
    "name": "Сэндвич с тунцом",
    "description": "Цельнозерновой хлеб, тунец, листья салата и немного майонеза.",
    "proteins": 19,
    "fats": 10,
    "carbohydrates": 27,
    "addedAt": "2025-06-18T11:05:00.663"
  },
  {
    "name": "Запеченный лосось с овощами",
    "description": "Филе лосося, запеченное с брокколи, морковью и оливковым маслом.",
    "proteins": 28,
    "fats": 18,
    "carbohydrates": 5,
    "addedAt": "2025-06-18T11:05:00.663"
  }
]

async def main():
    for food in foods:
        await upload_food(76, token, food["name"], food["description"], food["proteins"], food["fats"], food["carbohydrates"], food["addedAt"])

if __name__ == "__main__":
    asyncio.run(main())
