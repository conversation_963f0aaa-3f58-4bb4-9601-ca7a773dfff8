from datetime import datetime
# from pydantic import BaseModel
import json
from airflow.decorators import dag, task
import configparser
import requests
import tiktoken
from cost_tracking import track_openai_cost

# Читаем конфиг и извлекаем токен
config = configparser.ConfigParser()
config.read("/opt/airflow/pipe/config.ini")
API_TOKEN_GPT = config.get('TOKENS', 'API_TOKEN_GPT')

default_args = {
    'owner': '<PERSON><PERSON>',
    'retries': 0,
}

@dag(dag_id='chatgpt_describe_challenge_pipeline',
     default_args=default_args,
     start_date=datetime.now(),
     catchup=False,
     schedule_interval=None)
def chatgpt():
    @task()
    def generate_openai_response(**kwargs):
        
        conf = kwargs.get('dag_run').conf
        if not conf or "prompt" not in conf:
            raise ValueError("Не найден промпт в конфигурации DAG.")
        prompt = conf["prompt"]

        belgium_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }
        germany_proxies = {
            "http": "http://**************:8888",
            "https": "http://**************:8888"
        }

        headers = {
            "Authorization": f"Bearer {API_TOKEN_GPT}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "gpt-4o",
            "temperature": 0.5,
            "messages": [{"role": "system", "content": prompt}]
        }

        url = "https://api.openai.com/v1/chat/completions"
        max_retries = 2
        response = None
        for attempt in range(max_retries):
            try:
                response = requests.post(url, json=data, headers=headers, proxies=germany_proxies, timeout=90)
                response.raise_for_status()
                break
            except requests.exceptions.Timeout:
                if attempt == max_retries - 1:
                    raise
        if response is None:
            raise Exception("No response received after retries")
        # response = requests.post(url, json=data, headers=headers)

        content = response.json()["choices"][0]["message"]["content"]

        # Мониторинг затрат OpenAI
        try:
            
            response_data = response.json()

            cost_analysis = track_openai_cost(
                response_data=response_data,
                context={
                    "dag_id": "chatgpt_describe_challenge_pipeline",
                    "task_id": "generate_openai_response"
                }
            )

            print(f"💰 Стоимость операции: ${cost_analysis.get('total_cost', 0):.6f}")
            print(f"🔢 Токены: {cost_analysis.get('total_tokens', 0)}")

        except Exception as e:
            print(f"⚠️ Ошибка отслеживания затрат: {e}")
        

        return content.strip('"')

    @task()
    def get_json_metrics(response):
        response = response.strip()
        start = response.find('{')
        end = response.rfind('}')

        if start != -1 and end != -1:
            json_string = response[start:end + 1].strip()
            metrics = json_string.replace("True", "true").replace("False", "false").replace("None", "null").replace("[]", "null").replace("'", '"')
            try:
                return metrics
            except json.JSONDecodeError:
                raise ValueError("Failed to parse JSON from cleaned string")
        else:
            try:
                response_cleaned = response.strip()
                metrics = json.loads(response_cleaned)
                return metrics
            except json.JSONDecodeError:
                raise ValueError("Invalid JSON format in response")

    response = generate_openai_response(provide_context=True)
    get_json_metrics(response)

dag_chatgpt = chatgpt()