from datetime import datetime
import configparser
import requests
from airflow.decorators import dag, task
import base64
import fitz
import json
import redis
import concurrent.futures
from PIL import Image
import re

# Читаем конфиг и извлекаем токен OpenAI
config = configparser.ConfigParser()
config.read("/opt/airflow/pipe/config.ini")
API_TOKEN_GPT = config.get('TOKENS', 'API_TOKEN_GPT')

default_args = {
    'owner': '<PERSON><PERSON>',
    'retries': 0,
}

proxies = {
    "http": "http://**************:8888",
    "https": "http://**************:8888"
}

@dag(
    dag_id='chatgpt_pdf_pipeline',
    default_args=default_args,
    start_date=datetime.now(),
    catchup=False,
    schedule_interval=None
)
def chatgpt_pdf_pipeline():
    @task()
    def fetch_pdf(**kwargs) -> bytes:
        """
        Извлекает PDF-файл.
        Если в конфигурации присутствует redis_key, то файл берется из Redis,
        иначе используется поле "file" (старый вариант).
        """
        conf = kwargs.get('dag_run').conf
        if "redis_key" in conf:
            redis_key = conf.get("redis_key")
            # Настройте параметры подключения к Redis
            r = redis.from_url("redis://redis:6379", decode_responses=False)
            file_bytes = r.get(redis_key)
            if file_bytes is None:
                raise ValueError(f"PDF не найден в Redis по ключу {redis_key}")
            # Возвращаем base64-строку для корректной сериализации через XCom
            return base64.b64encode(file_bytes).decode("utf-8")
        else:
            file = conf.get("file")
            # Если file уже строка, предполагаем, что это base64 представление
            return file

    @task()
    def convert_pdf_to_png(pdf_bytes) -> list:
        """
        Конвертирует каждую страницу PDF в изображение PNG и возвращает список
        данных в формате data URL.
        """
        # Если входной параметр имеет тип str, декодируем его из base64 в байты
        if isinstance(pdf_bytes, str):
            pdf_bytes = base64.b64decode(pdf_bytes)
        try:
            doc = fitz.open(stream=pdf_bytes, filetype='pdf')
        except Exception as e:
            raise ValueError(
                "Файл поврежден либо находится не в правильном формате. Пожалуйста попробуйте другой файл.")
        images = []
        for page in doc:
            pix = page.get_pixmap()
            png_bytes = pix.tobytes("png")
            b64_str = base64.b64encode(png_bytes).decode("utf-8")
            data_url = f"data:image/png;base64,{b64_str}"
            images.append(data_url)
        return images

    @task()
    def process_pages(png_images: list) -> list:
        """
        Обрабатывает каждую страницу PDF (в PNG формате) через ChatGPT API с использованием requests и заданных прокси,
        выполняя OCR-анализ для каждой страницы параллельно через ThreadPoolExecutor.
        """
        import requests
        import concurrent.futures
from cost_tracking import track_openai_cost

        def process_single_page(image_url: str) -> str:
            headers = {
                "Authorization": f"Bearer {API_TOKEN_GPT}",
                "Content-Type": "application/json"
            }
            prompt = "Проведи OCR-анализ изображения страницы PDF и верни краткий текстовый вывод."
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": prompt},
                        {"type": "image_url", "image_url": {"url": image_url}}
                    ]
                }
            ]
            data = {
                "model": "gpt-4o",
                "temperature": 0,
                "messages": messages
            }
            url = "https://api.openai.com/v1/chat/completions"
            response = requests.post(url, json=data, headers=headers, proxies=proxies)
            response.raise_for_status()
            result = response.json()["choices"][0]["message"]["content"]

        # Мониторинг затрат OpenAI
        try:
            
            response_data = response.json()

            cost_analysis = track_openai_cost(
                response_data=response_data,
                context={
                    "dag_id": "chatgpt_pdf_pipeline",
                    "task_id": "generate_openai_response"
                }
            )

            print(f"💰 Стоимость операции: ${cost_analysis.get('total_cost', 0):.6f}")
            print(f"🔢 Токены: {cost_analysis.get('total_tokens', 0)}")

        except Exception as e:
            print(f"⚠️ Ошибка отслеживания затрат: {e}")
            cleaned = result.replace("```json\n", "").replace("\n```", "").strip()
            return cleaned

        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = [executor.submit(process_single_page, img) for img in png_images]
            results = [future.result() for future in concurrent.futures.as_completed(futures)]
        return results

    @task()
    def combine_ocr_results(ocr_results: list) -> str:
        """
        Объединяет результаты OCR анализа всех страниц и отправляет итоговый запрос
        к ChatGPT для получения финального JSON-итога.
        """
        combined_text = "\n".join(ocr_results)
        headers = {
            "Authorization": f"Bearer {API_TOKEN_GPT}",
            "Content-Type": "application/json"
        }
        prompt = (
            f"На основе следующих результатов OCR анализа всех страниц PDF, суммируй информацию и верни итоговый JSON документ:\n\n"
            f"{combined_text}\n\n"
            "Выводи только JSON и никакой дополнительной информации. Если данных нет, верни {}."
        )
        messages = [{"role": "user", "content": prompt}]
        data = {
            "model": "gpt-4o",
            "temperature": 0.2,
            "messages": messages
        }
        url = "https://api.openai.com/v1/chat/completions"
        response = requests.post(url, json=data, headers=headers, proxies=proxies)
        response.raise_for_status()
        final_summary = response.json()["choices"][0]["message"]["content"].strip()
        return final_summary

    @task()
    def get_json_metrics(response_text: str):
        """
        Очищает итоговый текст от лишних символов и возвращает валидный JSON.
        """
        # response_text = response_text.strip()
        # start = response_text.find('{')
        # end = response_text.rfind('}')
        # if response_text == "Файл поврежден либо находится не в правильном формате. Пожалуйста попробуйте другой файл.":
        #     return response_text
        # if start != -1 and end != -1:
        #     json_string = response_text[start:end + 1].strip()
        #     content = (
        #         json_string.replace("True", "true")
        #         .replace("False", "false")
        #         .replace("None", "null")
        #         .replace("[]", "null")
        #         .replace("'", '"')
        #     )
        #     try:
        #         json.loads(content)
        #         return content
        #     except json.JSONDecodeError:
        #         raise ValueError("Failed to parse JSON from cleaned string")
        # else:
        #     try:
        #         return json.loads(response_text)
        #     except json.JSONDecodeError:
        #         return json.loads('{"Данные из изображения": "Нет полученных данных"}')
        return response_text

    # Последовательность выполнения:
    pdf_bytes = fetch_pdf(provide_context=True)
    png_images = convert_pdf_to_png(pdf_bytes)
    ocr_results = process_pages(png_images)
    final_summary = combine_ocr_results(ocr_results)
    json_metrics = get_json_metrics(final_summary)

    return json_metrics

dag_chatgpt = chatgpt_pdf_pipeline()