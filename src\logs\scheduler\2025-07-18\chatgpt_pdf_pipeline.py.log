[2025-07-18T11:26:00.346+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:00.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:26:00.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:00.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.362+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:26:00.364+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:00.383+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:26:31.442+0000] {processor.py:186} INFO - Started process (PID=414) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:31.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:26:31.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:31.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.457+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:26:31.459+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:31.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T11:27:02.398+0000] {processor.py:186} INFO - Started process (PID=543) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:02.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:27:02.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:02.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.415+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:27:02.417+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:02.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
[2025-07-18T11:27:33.061+0000] {processor.py:186} INFO - Started process (PID=676) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:33.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:27:33.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:33.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.078+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:27:33.080+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:33.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:28:03.577+0000] {processor.py:186} INFO - Started process (PID=807) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:03.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:28:03.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.581+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:03.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.593+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:28:03.595+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:03.614+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:28:33.929+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:33.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:28:33.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:33.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.944+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:28:33.946+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:33.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T11:29:04.970+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:04.971+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:29:04.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:04.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.983+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:29:04.985+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:05.002+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T11:29:35.754+0000] {processor.py:186} INFO - Started process (PID=1198) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:35.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:29:35.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.757+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:35.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.770+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:29:35.772+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:35.793+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T11:30:06.168+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:06.169+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:30:06.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.171+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:06.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.184+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:30:06.186+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:06.205+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T11:30:37.277+0000] {processor.py:186} INFO - Started process (PID=1460) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:37.279+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:30:37.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:37.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.293+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:30:37.295+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:37.316+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:31:07.953+0000] {processor.py:186} INFO - Started process (PID=1593) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:07.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:31:07.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:07.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.967+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:31:07.969+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:07.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
