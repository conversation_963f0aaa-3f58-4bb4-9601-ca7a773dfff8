[2025-07-18T11:26:00.346+0000] {processor.py:186} INFO - Started process (PID=281) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:00.347+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:26:00.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.349+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:00.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.362+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:26:00.364+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:00.383+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:26:31.442+0000] {processor.py:186} INFO - Started process (PID=414) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:31.443+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:26:31.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.445+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:31.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.457+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:26:31.459+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:26:31.477+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T11:27:02.398+0000] {processor.py:186} INFO - Started process (PID=543) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:02.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:27:02.402+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.401+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:02.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.415+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:27:02.417+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:02.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
[2025-07-18T11:27:33.061+0000] {processor.py:186} INFO - Started process (PID=676) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:33.062+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:27:33.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.065+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:33.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.078+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:27:33.080+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:27:33.099+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:28:03.577+0000] {processor.py:186} INFO - Started process (PID=807) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:03.578+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:28:03.581+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.581+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:03.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.593+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:28:03.595+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:03.614+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.044 seconds
[2025-07-18T11:28:33.929+0000] {processor.py:186} INFO - Started process (PID=936) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:33.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:28:33.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:33.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.944+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:28:33.946+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:28:33.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T11:29:04.970+0000] {processor.py:186} INFO - Started process (PID=1069) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:04.971+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:29:04.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:04.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.983+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:29:04.985+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:05.002+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.038 seconds
[2025-07-18T11:29:35.754+0000] {processor.py:186} INFO - Started process (PID=1198) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:35.755+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:29:35.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.757+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:35.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.770+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:29:35.772+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:29:35.793+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T11:30:06.168+0000] {processor.py:186} INFO - Started process (PID=1326) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:06.169+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:30:06.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.171+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:06.185+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.184+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:30:06.186+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:06.205+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T11:30:37.277+0000] {processor.py:186} INFO - Started process (PID=1460) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:37.279+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:30:37.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.281+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:37.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.293+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:30:37.295+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:30:37.316+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:31:07.953+0000] {processor.py:186} INFO - Started process (PID=1593) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:07.954+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:31:07.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:07.968+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.967+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:31:07.969+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:07.987+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.040 seconds
[2025-07-18T11:31:38.627+0000] {processor.py:186} INFO - Started process (PID=1724) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:38.628+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:31:38.630+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.630+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:38.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.642+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:31:38.644+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:31:38.662+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T11:32:09.143+0000] {processor.py:186} INFO - Started process (PID=1855) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:32:09.144+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:32:09.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.147+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:32:09.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.161+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:32:09.163+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:32:09.183+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T11:32:39.496+0000] {processor.py:186} INFO - Started process (PID=1984) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:32:39.497+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:32:39.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.499+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:32:39.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.510+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:32:39.512+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:32:39.530+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.041 seconds
[2025-07-18T11:33:10.351+0000] {processor.py:186} INFO - Started process (PID=2117) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:33:10.352+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:33:10.355+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.354+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:33:10.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.366+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:33:10.368+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:33:10.387+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T11:33:42.179+0000] {processor.py:186} INFO - Started process (PID=2265) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:33:42.180+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:33:42.183+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.183+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:33:42.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.203+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:33:42.205+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:33:42.225+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.055 seconds
[2025-07-18T11:34:13.522+0000] {processor.py:186} INFO - Started process (PID=2418) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:34:13.523+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:34:13.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.526+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:34:13.541+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.540+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:34:13.542+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:34:13.562+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T11:34:44.825+0000] {processor.py:186} INFO - Started process (PID=2571) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:34:44.826+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:34:44.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.829+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:34:44.846+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.845+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:34:44.847+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:34:44.869+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.050 seconds
[2025-07-18T11:35:16.573+0000] {processor.py:186} INFO - Started process (PID=2724) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:35:16.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:35:16.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.578+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:35:16.595+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.594+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:35:16.596+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:35:16.617+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.050 seconds
[2025-07-18T11:35:47.847+0000] {processor.py:186} INFO - Started process (PID=2877) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:35:47.848+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:35:47.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:35:47.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.865+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:35:47.867+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:35:47.887+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T11:36:18.723+0000] {processor.py:186} INFO - Started process (PID=3024) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:36:18.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:36:18.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.727+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:36:18.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.747+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:36:18.750+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:36:18.778+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.064 seconds
[2025-07-18T11:36:50.447+0000] {processor.py:186} INFO - Started process (PID=3177) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:36:50.448+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:36:50.451+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.450+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:36:50.468+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.467+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:36:50.469+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:36:50.490+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.051 seconds
[2025-07-18T11:37:21.243+0000] {processor.py:186} INFO - Started process (PID=3327) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:37:21.244+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:37:21.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.246+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:37:21.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.259+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:37:21.261+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:37:21.280+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
[2025-07-18T11:37:52.479+0000] {processor.py:186} INFO - Started process (PID=3478) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:37:52.480+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:37:52.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:37:52.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.503+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:37:52.506+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:37:52.528+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.056 seconds
[2025-07-18T11:38:23.276+0000] {processor.py:186} INFO - Started process (PID=3631) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:38:23.277+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:38:23.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.280+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:38:23.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.295+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:38:23.297+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:38:23.318+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
[2025-07-18T11:38:54.948+0000] {processor.py:186} INFO - Started process (PID=3789) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:38:54.949+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:38:54.952+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.951+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:38:54.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.963+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:38:54.966+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:38:54.986+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T11:39:26.099+0000] {processor.py:186} INFO - Started process (PID=3939) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:39:26.100+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:39:26.102+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.102+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:39:26.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.117+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:39:26.120+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:39:26.150+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.058 seconds
[2025-07-18T11:39:56.963+0000] {processor.py:186} INFO - Started process (PID=4092) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:39:56.964+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:39:56.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.967+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:39:56.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.981+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:39:56.983+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:39:57.002+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:40:28.019+0000] {processor.py:186} INFO - Started process (PID=4250) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:40:28.020+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:40:28.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:28.023+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:40:28.038+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:28.037+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:40:28.039+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:40:28.057+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T11:40:58.957+0000] {processor.py:186} INFO - Started process (PID=4410) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:40:58.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:40:58.960+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.960+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:40:58.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.971+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:40:58.973+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:40:58.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.042 seconds
[2025-07-18T11:41:29.808+0000] {processor.py:186} INFO - Started process (PID=4567) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:41:29.809+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:41:29.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.812+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:41:29.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.826+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:41:29.829+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:41:29.848+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T11:42:00.846+0000] {processor.py:186} INFO - Started process (PID=4722) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:42:00.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:42:00.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:42:00.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.864+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:42:00.866+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:42:00.885+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T11:42:31.324+0000] {processor.py:186} INFO - Started process (PID=4879) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:42:31.325+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:42:31.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.328+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:42:31.345+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.344+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:42:31.346+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:42:31.370+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.052 seconds
[2025-07-18T11:43:01.956+0000] {processor.py:186} INFO - Started process (PID=5032) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:43:01.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:43:01.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.959+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:43:01.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:01.971+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:43:01.974+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:43:01.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.045 seconds
[2025-07-18T11:43:32.666+0000] {processor.py:186} INFO - Started process (PID=5191) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:43:32.667+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:43:32.670+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.670+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:43:32.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.686+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:43:32.687+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:43:32.709+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.049 seconds
[2025-07-18T11:44:04.047+0000] {processor.py:186} INFO - Started process (PID=5350) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:44:04.048+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:44:04.051+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.050+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:44:04.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.065+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:44:04.067+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:44:04.087+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:44:34.930+0000] {processor.py:186} INFO - Started process (PID=5503) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:44:34.931+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:44:34.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:34.934+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:44:34.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:34.949+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:44:34.951+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:44:34.970+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:45:05.369+0000] {processor.py:186} INFO - Started process (PID=5656) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:45:05.370+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:45:05.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:05.373+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:45:05.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:05.392+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:45:05.394+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:45:05.415+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.053 seconds
[2025-07-18T11:45:36.650+0000] {processor.py:186} INFO - Started process (PID=5815) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:45:36.651+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:45:36.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:36.653+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:45:36.669+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:36.668+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:45:36.670+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:45:36.691+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T11:46:07.316+0000] {processor.py:186} INFO - Started process (PID=5968) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:46:07.317+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:46:07.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:07.319+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:46:07.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:07.334+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:46:07.336+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:46:07.356+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:46:38.084+0000] {processor.py:186} INFO - Started process (PID=6127) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:46:38.085+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:46:38.089+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:38.088+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:46:38.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:38.105+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:46:38.108+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:46:38.129+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.051 seconds
[2025-07-18T11:47:09.204+0000] {processor.py:186} INFO - Started process (PID=6280) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:47:09.206+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:47:09.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:09.208+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:47:09.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:09.223+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:47:09.225+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:47:09.246+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.048 seconds
[2025-07-18T11:47:40.502+0000] {processor.py:186} INFO - Started process (PID=6441) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:47:40.503+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:47:40.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:40.506+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:47:40.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:40.523+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:47:40.526+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:47:40.545+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.051 seconds
[2025-07-18T11:48:11.432+0000] {processor.py:186} INFO - Started process (PID=6600) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:48:11.433+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:48:11.436+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:11.436+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:48:11.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:11.448+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:48:11.451+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:48:11.473+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T11:48:42.724+0000] {processor.py:186} INFO - Started process (PID=6759) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:48:42.725+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:48:42.728+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:42.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:48:42.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:42.741+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:48:42.743+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:48:42.765+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.046 seconds
[2025-07-18T11:49:13.739+0000] {processor.py:186} INFO - Started process (PID=6918) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:49:13.741+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:49:13.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:13.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:49:13.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:13.758+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:49:13.759+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:49:13.780+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T11:49:44.624+0000] {processor.py:186} INFO - Started process (PID=7077) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:49:44.626+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:49:44.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:44.630+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:49:44.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:44.650+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:49:44.652+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:49:44.676+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.059 seconds
[2025-07-18T11:50:15.830+0000] {processor.py:186} INFO - Started process (PID=7236) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:50:15.831+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:50:15.834+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:15.833+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:50:15.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:15.848+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:50:15.851+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:50:15.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.047 seconds
[2025-07-18T11:50:47.259+0000] {processor.py:186} INFO - Started process (PID=7395) to work on /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:50:47.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_pdf_pipeline.py for tasks to queue
[2025-07-18T11:50:47.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:47.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:50:47.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:47.275+0000] {dagbag.py:387} ERROR - Failed to import: /opt/airflow/dags/chatgpt_pdf_pipeline.py
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dagbag.py", line 383, in parse
    loader.exec_module(new_module)
  File "<frozen importlib._bootstrap_external>", line 991, in exec_module
  File "<frozen importlib._bootstrap_external>", line 1129, in get_code
  File "<frozen importlib._bootstrap_external>", line 1059, in source_to_code
  File "<frozen importlib._bootstrap>", line 488, in _call_with_frames_removed
  File "/opt/airflow/dags/chatgpt_pdf_pipeline.py", line 91
    def process_single_page(image_url: str) -> str:
IndentationError: unexpected indent
[2025-07-18T11:50:47.277+0000] {processor.py:927} WARNING - No viable dags retrieved from /opt/airflow/dags/chatgpt_pdf_pipeline.py
[2025-07-18T11:50:47.297+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_pdf_pipeline.py took 0.043 seconds
