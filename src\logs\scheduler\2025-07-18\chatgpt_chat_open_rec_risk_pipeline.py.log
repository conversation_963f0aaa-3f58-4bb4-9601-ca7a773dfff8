[2025-07-18T11:25:57.368+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:25:57.369+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:25:57.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.371+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:25:57.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.452+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:57.458+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:25:57.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.552+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.563+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.708+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.717+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.726+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.734+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.744+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.744+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:57.758+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:57.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.758+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.759+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:25:57.783+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.421 seconds
[2025-07-18T11:26:28.458+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:28.459+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:26:28.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.461+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:28.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.543+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.552+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:28.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.841+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:28.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:26:28.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.419 seconds
[2025-07-18T11:26:59.717+0000] {processor.py:186} INFO - Started process (PID=485) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:59.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:26:59.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:59.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.978+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:27:00.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.090+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.104+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:27:00.126+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.414 seconds
[2025-07-18T11:27:30.653+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:27:30.654+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:27:30.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.657+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:27:30.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.751+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:30.760+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:27:30.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.867+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:30.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:27:30.898+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.251 seconds
[2025-07-18T11:28:01.234+0000] {processor.py:186} INFO - Started process (PID=745) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:01.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:28:01.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:01.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.306+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.314+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:01.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.411+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.423+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:28:01.445+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.216 seconds
[2025-07-18T11:28:31.739+0000] {processor.py:186} INFO - Started process (PID=876) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:31.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:28:31.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:31.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.840+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:31.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:31.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.957+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:31.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:28:31.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.259 seconds
[2025-07-18T11:29:02.080+0000] {processor.py:186} INFO - Started process (PID=1007) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:02.080+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:29:02.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.083+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:02.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.161+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.169+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:02.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.273+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.286+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:29:02.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.235 seconds
[2025-07-18T11:29:32.781+0000] {processor.py:186} INFO - Started process (PID=1138) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:32.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:29:32.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.786+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:32.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.873+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:32.880+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:32.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.990+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.003+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:29:33.028+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.254 seconds
[2025-07-18T11:30:03.929+0000] {processor.py:186} INFO - Started process (PID=1269) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:03.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:30:03.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:03.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:04.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.004+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.012+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:04.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:30:04.144+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.221 seconds
[2025-07-18T11:30:34.362+0000] {processor.py:186} INFO - Started process (PID=1400) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:34.363+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:30:34.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.365+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:34.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.438+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:34.446+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:34.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:34.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.559+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:30:34.584+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.228 seconds
[2025-07-18T11:31:05.013+0000] {processor.py:186} INFO - Started process (PID=1531) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:05.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:31:05.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.018+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:05.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.113+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:05.121+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:05.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.225+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:05.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.236+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:31:05.255+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.251 seconds
[2025-07-18T11:31:36.110+0000] {processor.py:186} INFO - Started process (PID=1662) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:36.111+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:31:36.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.113+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:36.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.187+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:36.196+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:36.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:36.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.312+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:31:36.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.229 seconds
