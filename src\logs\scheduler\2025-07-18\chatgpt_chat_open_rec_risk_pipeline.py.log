[2025-07-18T11:25:57.368+0000] {processor.py:186} INFO - Started process (PID=221) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:25:57.369+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:25:57.372+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.371+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:25:57.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.452+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:57.458+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:25:57.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.552+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.563+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.563+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.708+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.708+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.717+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.726+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.726+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.735+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.734+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.744+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.745+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.744+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:57.758+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:57.759+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.758+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_chat_open_rec_risk_pipeline
[2025-07-18T11:25:57.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:57.759+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:25:57.783+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.421 seconds
[2025-07-18T11:26:28.458+0000] {processor.py:186} INFO - Started process (PID=352) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:28.459+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:26:28.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.461+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:28.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.543+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:28.552+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:28.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.841+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:28.851+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:28.851+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:26:28.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.419 seconds
[2025-07-18T11:26:59.717+0000] {processor.py:186} INFO - Started process (PID=485) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:59.718+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:26:59.721+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:26:59.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:59.978+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:59.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:27:00.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.090+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:00.105+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:00.104+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:27:00.126+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.414 seconds
[2025-07-18T11:27:30.653+0000] {processor.py:186} INFO - Started process (PID=614) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:27:30.654+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:27:30.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.657+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:27:30.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.751+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:30.760+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:27:30.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.867+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:30.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:30.877+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:27:30.898+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.251 seconds
[2025-07-18T11:28:01.234+0000] {processor.py:186} INFO - Started process (PID=745) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:01.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:28:01.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:01.306+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.306+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:01.314+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:01.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.411+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:01.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:01.423+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:28:01.445+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.216 seconds
[2025-07-18T11:28:31.739+0000] {processor.py:186} INFO - Started process (PID=876) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:31.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:28:31.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:31.840+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.840+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:31.849+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:28:31.958+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.957+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:31.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:31.970+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:28:31.992+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.259 seconds
[2025-07-18T11:29:02.080+0000] {processor.py:186} INFO - Started process (PID=1007) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:02.080+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:29:02.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.083+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:02.161+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.161+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:02.169+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:02.274+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.273+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:02.286+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:02.286+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:29:02.309+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.235 seconds
[2025-07-18T11:29:32.781+0000] {processor.py:186} INFO - Started process (PID=1138) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:32.782+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:29:32.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.786+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:32.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.873+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:32.880+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:29:32.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:32.990+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:33.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:33.003+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:29:33.028+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.254 seconds
[2025-07-18T11:30:03.929+0000] {processor.py:186} INFO - Started process (PID=1269) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:03.930+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:30:03.932+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:03.932+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:04.004+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.004+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:04.012+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:04.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:04.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:04.124+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:30:04.144+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.221 seconds
[2025-07-18T11:30:34.362+0000] {processor.py:186} INFO - Started process (PID=1400) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:34.363+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:30:34.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.365+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:34.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.438+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:34.446+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:30:34.548+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.547+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:34.559+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:34.559+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:30:34.584+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.228 seconds
[2025-07-18T11:31:05.013+0000] {processor.py:186} INFO - Started process (PID=1531) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:05.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:31:05.019+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.018+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:05.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.113+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:05.121+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:05.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.225+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:05.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:05.236+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:31:05.255+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.251 seconds
[2025-07-18T11:31:36.110+0000] {processor.py:186} INFO - Started process (PID=1662) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:36.111+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:31:36.113+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.113+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:36.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.187+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:36.196+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:31:36.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.301+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:36.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:36.312+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:31:36.333+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.229 seconds
[2025-07-18T11:32:06.895+0000] {processor.py:186} INFO - Started process (PID=1793) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:32:06.896+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:32:06.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:06.899+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:32:06.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:06.973+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:06.982+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:32:07.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:07.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:07.097+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:32:07.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.230 seconds
[2025-07-18T11:32:37.341+0000] {processor.py:186} INFO - Started process (PID=1924) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:32:37.342+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:32:37.344+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.344+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:32:37.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.411+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:37.419+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:32:37.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.514+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:37.524+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:37.524+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:32:37.545+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.210 seconds
[2025-07-18T11:33:07.596+0000] {processor.py:186} INFO - Started process (PID=2052) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:33:07.598+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:33:07.600+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:07.600+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:33:07.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:07.674+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:07.683+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:33:07.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:07.781+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:07.791+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:07.791+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:33:07.809+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.218 seconds
[2025-07-18T11:33:37.958+0000] {processor.py:186} INFO - Started process (PID=2187) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:33:37.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:33:37.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.962+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:33:38.380+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.380+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:38.389+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:33:38.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:38.511+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:38.510+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:33:38.527+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.575 seconds
[2025-07-18T11:34:09.082+0000] {processor.py:186} INFO - Started process (PID=2340) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:34:09.084+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:34:09.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.086+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:34:09.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.500+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:09.511+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:34:09.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.623+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:09.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:09.774+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:34:09.795+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.719 seconds
[2025-07-18T11:34:40.125+0000] {processor.py:186} INFO - Started process (PID=2492) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:34:40.127+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:34:40.129+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.129+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:34:40.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.530+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:40.538+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:34:40.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.665+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:40.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:40.838+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:34:40.856+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.740 seconds
[2025-07-18T11:35:11.360+0000] {processor.py:186} INFO - Started process (PID=2645) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:35:11.361+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:35:11.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:11.364+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:35:11.760+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:11.759+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:11.767+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:35:12.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.052+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:12.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:12.063+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:35:12.083+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.730 seconds
[2025-07-18T11:35:42.191+0000] {processor.py:186} INFO - Started process (PID=2798) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:35:42.192+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:35:42.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.195+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:35:42.586+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.586+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:42.594+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:35:42.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.847+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:42.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:42.858+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:35:42.878+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.693 seconds
[2025-07-18T11:36:13.546+0000] {processor.py:186} INFO - Started process (PID=2951) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:36:13.547+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:36:13.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.550+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:36:13.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:13.927+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:13.936+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:36:14.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:14.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:14.206+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:36:14.227+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.689 seconds
[2025-07-18T11:36:44.632+0000] {processor.py:186} INFO - Started process (PID=3105) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:36:44.634+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:36:44.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:44.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:36:45.052+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.052+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:45.060+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:36:45.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.319+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:45.331+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:45.331+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:36:45.352+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.725 seconds
[2025-07-18T11:37:15.539+0000] {processor.py:186} INFO - Started process (PID=3259) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:37:15.540+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:37:15.545+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.544+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:37:15.942+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:15.942+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:15.950+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:37:16.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.236+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:16.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:16.246+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:37:16.264+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.733 seconds
[2025-07-18T11:37:48.082+0000] {processor.py:186} INFO - Started process (PID=3412) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:37:48.084+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:37:48.090+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.087+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:37:48.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.575+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:48.585+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:37:48.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.865+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:48.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:48.880+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:37:48.903+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.831 seconds
[2025-07-18T11:38:19.239+0000] {processor.py:186} INFO - Started process (PID=3565) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:38:19.241+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:38:19.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.243+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:38:19.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.778+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:19.787+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:38:19.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:19.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:19.891+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:38:19.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.680 seconds
[2025-07-18T11:38:50.032+0000] {processor.py:186} INFO - Started process (PID=3717) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:38:50.034+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:38:50.036+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.036+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:38:50.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.625+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:50.635+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:38:50.734+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.734+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:50.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:50.744+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:38:50.765+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.738 seconds
[2025-07-18T11:39:21.158+0000] {processor.py:186} INFO - Started process (PID=3871) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:39:21.160+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:39:21.163+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.162+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:39:21.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.676+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:21.683+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:39:21.786+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.785+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:21.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:21.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:39:21.813+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.661 seconds
[2025-07-18T11:39:52.106+0000] {processor.py:186} INFO - Started process (PID=4024) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:39:52.107+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:39:52.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.109+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:39:52.611+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.611+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:52.618+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:39:52.713+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.712+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:52.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:52.724+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:39:52.745+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.646 seconds
[2025-07-18T11:40:22.981+0000] {processor.py:186} INFO - Started process (PID=4177) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:40:22.982+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:40:22.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:22.985+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:40:23.533+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.533+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:23.540+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:40:23.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.644+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:23.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:23.656+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:40:23.679+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.704 seconds
[2025-07-18T11:40:54.738+0000] {processor.py:186} INFO - Started process (PID=4336) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:40:54.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:40:54.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:54.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:40:55.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.232+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:55.240+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:40:55.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.326+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:55.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:55.337+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:40:55.357+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.624 seconds
[2025-07-18T11:41:25.902+0000] {processor.py:186} INFO - Started process (PID=4495) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:41:25.904+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:41:25.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:25.906+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:41:26.418+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.418+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:26.427+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:41:26.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.521+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:26.532+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:26.531+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:41:26.551+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.654 seconds
[2025-07-18T11:41:56.732+0000] {processor.py:186} INFO - Started process (PID=4654) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:41:56.733+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:41:56.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:56.735+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:41:57.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.225+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:57.232+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:41:57.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:57.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:57.334+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:41:57.356+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.629 seconds
[2025-07-18T11:42:27.981+0000] {processor.py:186} INFO - Started process (PID=4819) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:42:27.982+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:42:27.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:27.984+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:42:28.494+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.494+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:28.503+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:42:28.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.597+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:28.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:28.607+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:42:28.625+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.650 seconds
[2025-07-18T11:42:58.828+0000] {processor.py:186} INFO - Started process (PID=4978) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:42:58.829+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:42:58.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:58.831+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:42:59.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.311+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:59.318+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:42:59.409+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:59.419+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:59.419+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:42:59.438+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.616 seconds
[2025-07-18T11:43:29.579+0000] {processor.py:186} INFO - Started process (PID=5137) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:43:29.581+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:43:29.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:29.583+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:43:30.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.055+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:30.062+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:43:30.153+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.152+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:30.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:30.163+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:43:30.181+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.608 seconds
[2025-07-18T11:44:00.942+0000] {processor.py:186} INFO - Started process (PID=5296) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:44:00.943+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:44:00.946+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:00.945+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:44:01.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.452+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:01.459+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:44:01.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.562+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:01.572+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:01.572+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:44:01.592+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.657 seconds
[2025-07-18T11:44:31.990+0000] {processor.py:186} INFO - Started process (PID=5455) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:44:31.991+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:44:31.994+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:31.993+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:44:32.522+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.522+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:32.531+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:44:32.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.624+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:32.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:32.635+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:44:32.656+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.672 seconds
[2025-07-18T11:45:02.852+0000] {processor.py:186} INFO - Started process (PID=5614) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:45:02.853+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:45:02.856+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:02.856+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:45:03.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.408+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:03.417+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:45:03.538+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.537+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:03.550+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:03.550+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:45:03.574+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.731 seconds
[2025-07-18T11:45:34.260+0000] {processor.py:186} INFO - Started process (PID=5773) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:45:34.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:45:34.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:45:34.819+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.818+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:34.826+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:45:34.934+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.933+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:34.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:34.944+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:45:34.966+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.713 seconds
[2025-07-18T11:46:05.268+0000] {processor.py:186} INFO - Started process (PID=5932) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:46:05.269+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:46:05.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:46:05.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.774+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:05.780+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:46:05.882+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.882+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:05.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:05.894+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:46:05.916+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.654 seconds
[2025-07-18T11:46:36.053+0000] {processor.py:186} INFO - Started process (PID=6091) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:46:36.054+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:46:36.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.057+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:46:36.585+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.584+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:36.592+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:46:36.694+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.693+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:36.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:36.704+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:46:36.727+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.681 seconds
[2025-07-18T11:47:07.152+0000] {processor.py:186} INFO - Started process (PID=6249) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:47:07.153+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:47:07.156+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.156+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:47:07.613+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.613+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:07.621+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:47:07.717+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.717+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:07.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:07.731+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:47:07.753+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.607 seconds
[2025-07-18T11:47:38.197+0000] {processor.py:186} INFO - Started process (PID=6409) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:47:38.198+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:47:38.201+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:38.200+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:47:38.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:38.715+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:38.723+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:47:38.827+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:38.826+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:38.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:38.838+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:47:38.859+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.668 seconds
[2025-07-18T11:48:09.022+0000] {processor.py:186} INFO - Started process (PID=6568) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:48:09.023+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:48:09.026+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:09.026+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:48:09.542+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:09.542+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:09.549+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:48:09.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:09.654+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:09.665+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:09.665+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:48:09.685+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.670 seconds
[2025-07-18T11:48:39.979+0000] {processor.py:186} INFO - Started process (PID=6727) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:48:39.980+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:48:39.983+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:39.982+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:48:40.506+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:40.506+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:40.513+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:48:40.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:40.614+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:40.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:40.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:48:40.645+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.674 seconds
[2025-07-18T11:49:10.874+0000] {processor.py:186} INFO - Started process (PID=6892) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:49:10.875+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:49:10.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:10.878+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:49:11.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:11.390+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:11.397+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:49:11.501+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:11.500+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:11.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:11.511+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:49:11.534+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.671 seconds
[2025-07-18T11:49:41.886+0000] {processor.py:186} INFO - Started process (PID=7051) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:49:41.887+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:49:41.890+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:41.889+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:49:42.408+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:42.408+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:42.418+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:49:42.525+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:42.524+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:42.535+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:42.535+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:49:42.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.675 seconds
[2025-07-18T11:50:12.670+0000] {processor.py:186} INFO - Started process (PID=7209) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:50:12.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:50:12.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:12.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:50:13.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:13.208+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:13.216+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:50:13.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:13.313+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:13.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:13.323+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:50:13.343+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.680 seconds
[2025-07-18T11:50:43.473+0000] {processor.py:186} INFO - Started process (PID=7368) to work on /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:50:43.474+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py for tasks to queue
[2025-07-18T11:50:43.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:43.476+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:50:43.964+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:43.964+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:43.970+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_chat_open_rec_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py
[2025-07-18T11:50:44.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:44.070+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:44.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:44.081+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_chat_open_rec_risk_pipeline to None, run_after=None
[2025-07-18T11:50:44.102+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_chat_open_rec_risk_pipeline.py took 0.636 seconds
