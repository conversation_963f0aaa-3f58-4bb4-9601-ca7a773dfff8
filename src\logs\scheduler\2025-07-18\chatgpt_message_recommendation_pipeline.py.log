[2025-07-18T11:25:59.400+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:25:59.402+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:25:59.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.404+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:25:59.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.500+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:59.509+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:25:59.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.798+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.822+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.836+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.857+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.873+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.884+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.898+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.899+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:59.918+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:59.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.919+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.919+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:25:59.946+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.551 seconds
[2025-07-18T11:26:30.587+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:26:30.588+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:26:30.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.591+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:26:30.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.837+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.845+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:26:30.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.950+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:26:30.983+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.402 seconds
[2025-07-18T11:27:01.865+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:01.866+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:27:01.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.868+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:01.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.946+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:02.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.075+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:27:02.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.235 seconds
[2025-07-18T11:27:32.523+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:32.523+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:27:32.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.526+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:32.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.598+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.606+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:32.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.703+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.714+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:27:32.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.215 seconds
[2025-07-18T11:28:03.060+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:03.061+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:28:03.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.063+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:03.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.132+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:03.140+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:03.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.236+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:03.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.246+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:28:03.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.212 seconds
[2025-07-18T11:28:33.401+0000] {processor.py:186} INFO - Started process (PID=921) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:33.402+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:28:33.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.404+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:33.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.477+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:33.486+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:33.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.586+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.598+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:28:33.618+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.223 seconds
[2025-07-18T11:29:04.427+0000] {processor.py:186} INFO - Started process (PID=1052) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:04.428+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:29:04.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.430+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:04.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.508+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:04.518+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:04.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.628+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:04.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.640+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:29:04.660+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.239 seconds
[2025-07-18T11:29:35.218+0000] {processor.py:186} INFO - Started process (PID=1183) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:35.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:29:35.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.221+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:35.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.297+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.305+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:35.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.405+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:35.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.417+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:29:35.438+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.227 seconds
[2025-07-18T11:30:05.614+0000] {processor.py:186} INFO - Started process (PID=1314) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:05.615+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:30:05.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.617+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:05.696+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.696+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.703+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:05.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.820+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.831+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:30:05.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.250 seconds
[2025-07-18T11:30:36.735+0000] {processor.py:186} INFO - Started process (PID=1445) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:36.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:30:36.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:36.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.828+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:36.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.951+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:30:36.970+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.243 seconds
[2025-07-18T11:31:07.376+0000] {processor.py:186} INFO - Started process (PID=1578) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:07.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:31:07.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.379+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:07.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.461+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.471+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:07.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.580+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.591+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:31:07.613+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.243 seconds
