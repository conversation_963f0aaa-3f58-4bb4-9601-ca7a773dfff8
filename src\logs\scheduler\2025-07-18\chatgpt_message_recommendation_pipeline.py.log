[2025-07-18T11:25:59.400+0000] {processor.py:186} INFO - Started process (PID=266) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:25:59.402+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:25:59.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.404+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:25:59.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.500+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:59.509+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:25:59.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.798+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.822+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.822+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.836+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.836+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.857+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.857+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.873+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.884+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.898+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.899+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:59.918+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:59.919+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.919+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_message_recommendation_pipeline
[2025-07-18T11:25:59.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.919+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:25:59.946+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.551 seconds
[2025-07-18T11:26:30.587+0000] {processor.py:186} INFO - Started process (PID=397) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:26:30.588+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:26:30.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.591+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:26:30.838+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.837+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.845+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:26:30.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.950+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.961+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:26:30.983+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.402 seconds
[2025-07-18T11:27:01.865+0000] {processor.py:186} INFO - Started process (PID=528) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:01.866+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:27:01.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.868+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:01.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.946+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.955+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:02.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.075+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:27:02.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.235 seconds
[2025-07-18T11:27:32.523+0000] {processor.py:186} INFO - Started process (PID=661) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:32.523+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:27:32.526+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.526+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:32.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.598+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.606+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:27:32.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.703+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.714+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:27:32.731+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.215 seconds
[2025-07-18T11:28:03.060+0000] {processor.py:186} INFO - Started process (PID=792) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:03.061+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:28:03.064+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.063+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:03.132+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.132+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:03.140+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:03.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.236+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:03.246+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.246+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:28:03.267+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.212 seconds
[2025-07-18T11:28:33.401+0000] {processor.py:186} INFO - Started process (PID=921) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:33.402+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:28:33.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.404+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:33.478+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.477+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:33.486+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:28:33.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.586+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.598+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:28:33.618+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.223 seconds
[2025-07-18T11:29:04.427+0000] {processor.py:186} INFO - Started process (PID=1052) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:04.428+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:29:04.430+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.430+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:04.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.508+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:04.518+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:04.628+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.628+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:04.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.640+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:29:04.660+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.239 seconds
[2025-07-18T11:29:35.218+0000] {processor.py:186} INFO - Started process (PID=1183) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:35.219+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:29:35.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.221+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:35.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.297+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.305+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:29:35.405+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.405+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:35.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.417+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:29:35.438+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.227 seconds
[2025-07-18T11:30:05.614+0000] {processor.py:186} INFO - Started process (PID=1314) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:05.615+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:30:05.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.617+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:05.696+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.696+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.703+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:05.820+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.820+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.831+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:30:05.858+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.250 seconds
[2025-07-18T11:30:36.735+0000] {processor.py:186} INFO - Started process (PID=1445) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:36.737+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:30:36.743+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.742+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:36.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.828+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:30:36.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.937+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.951+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:30:36.970+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.243 seconds
[2025-07-18T11:31:07.376+0000] {processor.py:186} INFO - Started process (PID=1578) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:07.377+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:31:07.379+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.379+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:07.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.461+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.471+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:07.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.580+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.592+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.591+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:31:07.613+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.243 seconds
[2025-07-18T11:31:38.107+0000] {processor.py:186} INFO - Started process (PID=1709) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:38.108+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:31:38.111+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.110+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:38.184+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.184+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:38.191+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:31:38.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.292+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:38.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.303+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:31:38.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.222 seconds
[2025-07-18T11:32:08.593+0000] {processor.py:186} INFO - Started process (PID=1840) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:32:08.594+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:32:08.598+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.597+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:32:08.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.671+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:08.681+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:32:08.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.781+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:08.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.794+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:32:08.817+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.231 seconds
[2025-07-18T11:32:39.218+0000] {processor.py:186} INFO - Started process (PID=1971) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:32:39.218+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:32:39.221+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.220+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:32:39.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.291+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:39.298+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:32:39.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.395+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:39.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.406+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:32:39.427+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.215 seconds
[2025-07-18T11:33:09.778+0000] {processor.py:186} INFO - Started process (PID=2102) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:33:09.779+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:33:09.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.781+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:33:09.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.859+0000] {cost_tracking.py:76} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:09.867+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:33:09.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.976+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:09.988+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.988+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:33:10.011+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.238 seconds
[2025-07-18T11:33:41.177+0000] {processor.py:186} INFO - Started process (PID=2247) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:33:41.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:33:41.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.181+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:33:41.554+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.554+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:41.563+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:33:41.676+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.675+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:41.689+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.689+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:33:41.709+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.539 seconds
[2025-07-18T11:34:12.684+0000] {processor.py:186} INFO - Started process (PID=2400) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:34:12.685+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:34:12.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.688+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:34:13.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.062+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:13.071+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:34:13.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:13.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.208+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:34:13.235+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.557 seconds
[2025-07-18T11:34:43.988+0000] {processor.py:186} INFO - Started process (PID=2553) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:34:43.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:34:43.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.991+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:34:44.354+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.354+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:44.364+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:34:44.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.473+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:44.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.484+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:34:44.504+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.523 seconds
[2025-07-18T11:35:15.512+0000] {processor.py:186} INFO - Started process (PID=2706) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:35:15.514+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:35:15.517+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.517+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:35:15.949+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.948+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:15.956+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:35:16.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.069+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:16.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:35:16.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.603 seconds
[2025-07-18T11:35:47.020+0000] {processor.py:186} INFO - Started process (PID=2859) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:35:47.021+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:35:47.024+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.024+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:35:47.463+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.462+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:47.473+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:35:47.580+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.579+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:47.591+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.591+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:35:47.615+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.601 seconds
[2025-07-18T11:36:18.043+0000] {processor.py:186} INFO - Started process (PID=3012) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:36:18.044+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:36:18.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.047+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:36:18.514+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.514+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:18.523+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:36:18.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.634+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:18.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.645+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:36:18.666+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.631 seconds
[2025-07-18T11:36:49.795+0000] {processor.py:186} INFO - Started process (PID=3165) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:36:49.796+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:36:49.800+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:49.799+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:36:50.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.205+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:50.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:36:50.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.360+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:50.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.376+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:36:50.397+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.608 seconds
[2025-07-18T11:37:20.492+0000] {processor.py:186} INFO - Started process (PID=3318) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:37:20.494+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:37:20.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:20.496+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:37:20.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:20.883+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:20.891+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:37:21.167+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.167+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:21.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.178+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:37:21.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.715 seconds
[2025-07-18T11:37:51.952+0000] {processor.py:186} INFO - Started process (PID=3472) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:37:51.953+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:37:51.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.955+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:37:52.363+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.363+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:52.371+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:37:52.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.621+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:52.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.634+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:37:52.655+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.712 seconds
[2025-07-18T11:38:22.967+0000] {processor.py:186} INFO - Started process (PID=3625) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:38:22.968+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:38:22.970+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.970+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:38:23.488+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.487+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:23.496+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:38:23.587+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.586+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:23.597+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.597+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:38:23.619+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.658 seconds
[2025-07-18T11:38:54.186+0000] {processor.py:186} INFO - Started process (PID=3777) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:38:54.187+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:38:54.190+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.190+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:38:54.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.762+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:54.770+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:38:54.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.868+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:54.879+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.879+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:38:54.900+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.721 seconds
[2025-07-18T11:39:25.380+0000] {processor.py:186} INFO - Started process (PID=3930) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:39:25.381+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:39:25.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:25.384+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:39:25.911+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:25.911+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:25.920+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:39:26.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.019+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:26.031+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.031+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:39:26.051+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.678 seconds
[2025-07-18T11:39:56.238+0000] {processor.py:186} INFO - Started process (PID=4083) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:39:56.239+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:39:56.242+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.241+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:39:56.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.781+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:56.790+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:39:56.889+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.889+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:56.900+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.900+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:39:56.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.689 seconds
[2025-07-18T11:40:27.296+0000] {processor.py:186} INFO - Started process (PID=4236) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:40:27.298+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:40:27.301+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.300+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:40:27.859+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.859+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:27.866+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:40:27.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.966+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:27.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.977+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:40:27.999+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.709 seconds
[2025-07-18T11:40:58.330+0000] {processor.py:186} INFO - Started process (PID=4401) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:40:58.331+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:40:58.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.333+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:40:58.799+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.798+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:58.805+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:40:58.894+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.893+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:58.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.902+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:40:58.920+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.596 seconds
[2025-07-18T11:41:29.455+0000] {processor.py:186} INFO - Started process (PID=4561) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:41:29.456+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:41:29.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.458+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:41:29.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.950+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:29.957+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:41:30.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:30.046+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:30.055+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:30.054+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:41:30.073+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.624 seconds
[2025-07-18T11:42:00.935+0000] {processor.py:186} INFO - Started process (PID=4727) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:42:00.936+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:42:00.938+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.938+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:42:01.428+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:01.428+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:01.435+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:42:01.528+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:01.528+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:01.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:01.537+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:42:01.555+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.625 seconds
[2025-07-18T11:42:31.697+0000] {processor.py:186} INFO - Started process (PID=4886) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:42:31.698+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:42:31.700+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:42:32.229+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.229+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:32.237+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:42:32.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.337+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:32.346+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.346+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:42:32.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.673 seconds
[2025-07-18T11:43:02.715+0000] {processor.py:186} INFO - Started process (PID=5045) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:43:02.716+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:43:02.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.719+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:43:03.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.226+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:03.233+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:43:03.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.327+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:03.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.337+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:43:03.354+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.646 seconds
[2025-07-18T11:43:34.029+0000] {processor.py:186} INFO - Started process (PID=5208) to work on /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:43:34.030+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py for tasks to queue
[2025-07-18T11:43:34.032+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.032+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:43:34.551+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.551+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:34.558+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_recommendation_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py
[2025-07-18T11:43:34.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.667+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:34.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:34.680+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_recommendation_pipeline to None, run_after=None
[2025-07-18T11:43:34.706+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_recommendation_pipeline.py took 0.683 seconds
