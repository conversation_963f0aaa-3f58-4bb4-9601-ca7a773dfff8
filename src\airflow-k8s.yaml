# PersistentVolumeClaims для хранения данных

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgres-db-volume
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 5Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-volume-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-insight-volume-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: assets-volume-data
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: airflow-redis-volume
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi

# -----------------------------
# Deployments и Services для Redis
# -----------------------------

apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:latest
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: redis-volume
          mountPath: /data
        readinessProbe:
          exec:
            command: ["redis-cli", "ping"]
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: redis-volume
        persistentVolumeClaim:
          claimName: redis-volume-data
---
apiVersion: v1
kind: Service
metadata:
  name: redis
spec:
  selector:
    app: redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
  type: ClusterIP

# Redis Insight

apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis-insight
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis-insight
  template:
    metadata:
      labels:
        app: redis-insight
    spec:
      containers:
      - name: redis-insight
        image: redislabs/redisinsight:1.14.0
        ports:
        - containerPort: 8001
        volumeMounts:
        - name: redis-insight-volume
          mountPath: /db
      volumes:
      - name: redis-insight-volume
        persistentVolumeClaim:
          claimName: redis-insight-volume-data
---
apiVersion: v1
kind: Service
metadata:
  name: redis-insight
spec:
  selector:
    app: redis-insight
  ports:
    - protocol: TCP
      port: 8001
      targetPort: 8001
  type: ClusterIP

# Airflow Redis (изолированный)

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow-redis
  template:
    metadata:
      labels:
        app: airflow-redis
    spec:
      containers:
      - name: airflow-redis
        image: redis:6.2
        ports:
        - containerPort: 6379
        volumeMounts:
        - name: airflow-redis-volume
          mountPath: /data
        readinessProbe:
          exec:
            command: ["redis-cli", "ping"]
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: airflow-redis-volume
        persistentVolumeClaim:
          claimName: airflow-redis-volume
---
apiVersion: v1
kind: Service
metadata:
  name: airflow-redis
spec:
  selector:
    app: airflow-redis
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
  type: ClusterIP

# -----------------------------
# FastAPI Service
# -----------------------------

apiVersion: apps/v1
kind: Deployment
metadata:
  name: fastapi-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: fastapi-app
  template:
    metadata:
      labels:
        app: fastapi-app
    spec:
      containers:
      - name: fastapi-app
        image: tiangolo/uvicorn-gunicorn-fastapi:python3.9-slim
        ports:
        - containerPort: 8000
        volumeMounts:
        - name: fastapi-code
          mountPath: /app
      volumes:
      - name: fastapi-code
        hostPath:
          path: ./fastapi  # для разработки, для production лучше использовать PVC или образ с кодом
---
apiVersion: v1
kind: Service
metadata:
  name: fastapi-app
spec:
  selector:
    app: fastapi-app
  ports:
    - protocol: TCP
      port: 9000
      targetPort: 8000
  type: NodePort  # или LoadBalancer/ClusterIP в зависимости от требований

# -----------------------------
# Postgres Service
# -----------------------------

apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgres
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgres
  template:
    metadata:
      labels:
        app: postgres
    spec:
      containers:
      - name: postgres
        image: postgres:13
        env:
        - name: POSTGRES_USER
          value: "airflow"
        - name: POSTGRES_PASSWORD
          value: "airflow"
        - name: POSTGRES_DB
          value: "airflow"
        ports:
        - containerPort: 5432
        volumeMounts:
        - name: postgres-data
          mountPath: /var/lib/postgresql/data
        readinessProbe:
          exec:
            command: ["pg_isready", "-U", "airflow"]
          initialDelaySeconds: 10
          periodSeconds: 10
      volumes:
      - name: postgres-data
        persistentVolumeClaim:
          claimName: postgres-db-volume
---
apiVersion: v1
kind: Service
metadata:
  name: postgres
spec:
  selector:
    app: postgres
  ports:
    - protocol: TCP
      port: 5432
      targetPort: 5432
  type: ClusterIP

# -----------------------------
# Airflow Webserver
# -----------------------------

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-webserver
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow-webserver
  template:
    metadata:
      labels:
        app: airflow-webserver
    spec:
      containers:
      - name: airflow-webserver
        image: extending_airflow:latest
        imagePullPolicy: IfNotPresent
        env:
        - name: AIRFLOW__CORE__EXECUTOR
          value: "CeleryExecutor"
        - name: AIRFLOW__CORE__PARALLELISM
        - name: AIRFLOW__CORE__PARALLELISM
          value: "32"
        - name: AIRFLOW__CORE__DAG_CONCURRENCY
          value: "16"
        - name: AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG
          value: "4"
        - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
          value: "postgresql+psycopg2://airflow:airflow@postgres/airflow"
        - name: AIRFLOW__CORE__FERNET_KEY
          value: ""
        - name: AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION
          value: "false"
        - name: AIRFLOW__CORE__LOAD_EXAMPLES
          value: "false"
        - name: AIRFLOW__API__AUTH_BACKENDS
          value: "airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session"
        - name: AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK
          value: "true"
        - name: AIRFLOW_CONFIG
          value: "/opt/airflow/config/airflow.cfg"
        - name: AIRFLOW__CELERY__BROKER_URL
          value: "redis://airflow-redis:6379/0"
        - name: AIRFLOW__CELERY__RESULT_BACKEND
          value: "db+*********************************************"
        command: ["/bin/bash", "-c", "airflow dags unpause chatgpt_message_pipeline && airflow dags unpause chatgpt_weights_pipeline && airflow dags unpause chatgpt_chat_open_pipeline && airflow dags unpause chatgpt_clarify_pipeline && airflow dags unpause chatgpt_classify_pipeline && airflow dags unpause chatgpt_chat_open_rec_risk_pipeline && airflow dags unpause chatgpt_message_recommendation_pipeline && airflow dags unpause chatgpt_message_risk_pipeline && airflow dags unpause chatgpt_metrics_pipeline && airflow dags unpause chatgpt_goals_pipeline && airflow dags unpause chatgpt_image_pipeline && airflow dags unpause chatgpt_image_object_pipeline && airflow dags unpause perplexity_pipeline && airflow webserver"]
        ports:
        - containerPort: 8080
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 30
      volumes:
      - name: dags
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/dags
      - name: logs
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/logs
      - name: config
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/config
      - name: plugins
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/plugins
      - name: assets
        hostPath:
          path: ./assets
---
apiVersion: v1
kind: Service
metadata:
  name: airflow-webserver
spec:
  selector:
    app: airflow-webserver
  ports:
    - protocol: TCP
      port: 9090
      targetPort: 8080
  type: ClusterIP

# -----------------------------
# Airflow Scheduler
# -----------------------------

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-scheduler
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow-scheduler
  template:
    metadata:
      labels:
        app: airflow-scheduler
    spec:
      containers:
      - name: airflow-scheduler
        image: extending_airflow:latest
        imagePullPolicy: IfNotPresent
        env:
          - name: AIRFLOW__CORE__EXECUTOR
            value: "CeleryExecutor"
          - name: AIRFLOW__CORE__PARALLELISM
            value: "32"
          - name: AIRFLOW__CORE__DAG_CONCURRENCY
            value: "16"
          - name: AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG
            value: "4"
          - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
            value: "postgresql+psycopg2://airflow:airflow@postgres/airflow"
          - name: AIRFLOW__CORE__FERNET_KEY
            value: ""
          - name: AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION
            value: "false"
          - name: AIRFLOW__CORE__LOAD_EXAMPLES
            value: "false"
          - name: AIRFLOW__API__AUTH_BACKENDS
            value: "airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session"
          - name: AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK
            value: "true"
          - name: AIRFLOW_CONFIG
            value: "/opt/airflow/config/airflow.cfg"
          - name: AIRFLOW__CELERY__BROKER_URL
            value: "redis://airflow-redis:6379/0"
          - name: AIRFLOW__CELERY__RESULT_BACKEND
            value: "db+*********************************************"
        command: ["airflow", "scheduler"]
        readinessProbe:
          httpGet:
            path: /health
            port: 8974
          initialDelaySeconds: 30
          periodSeconds: 30
      volumes:
      - name: dags
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/dags
      - name: logs
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/logs
      - name: config
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/config
      - name: plugins
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/plugins
      - name: assets
        hostPath:
          path: ./assets
---
apiVersion: v1
kind: Service
metadata:
  name: airflow-scheduler
spec:
  selector:
    app: airflow-scheduler
  ports:
    - protocol: TCP
      port: 8974
      targetPort: 8974
  type: ClusterIP

# -----------------------------
# Airflow Triggerer
# -----------------------------

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-triggerer
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow-triggerer
  template:
    metadata:
      labels:
        app: airflow-triggerer
    spec:
      containers:
      - name: airflow-triggerer
        image: extending_airflow:latest
        imagePullPolicy: IfNotPresent
        env:
          - name: AIRFLOW__CORE__EXECUTOR
            value: "CeleryExecutor"
          - name: AIRFLOW__CORE__PARALLELISM
            value: "32"
          - name: AIRFLOW__CORE__DAG_CONCURRENCY
            value: "16"
          - name: AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG
            value: "4"
          - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
            value: "postgresql+psycopg2://airflow:airflow@postgres/airflow"
          - name: AIRFLOW__CORE__FERNET_KEY
            value: ""
          - name: AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION
            value: "false"
          - name: AIRFLOW__CORE__LOAD_EXAMPLES
            value: "false"
          - name: AIRFLOW__API__AUTH_BACKENDS
            value: "airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session"
          - name: AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK
            value: "true"
          - name: AIRFLOW_CONFIG
            value: "/opt/airflow/config/airflow.cfg"
          - name: AIRFLOW__CELERY__BROKER_URL
            value: "redis://airflow-redis:6379/0"
          - name: AIRFLOW__CELERY__RESULT_BACKEND
            value: "db+*********************************************"
        command: ["airflow", "triggerer"]
      volumes:
      - name: dags
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/dags
      - name: logs
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/logs
      - name: config
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/config
      - name: plugins
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/plugins
      - name: assets
        hostPath:
          path: ./assets

# -----------------------------
# Airflow Init (Job)
# -----------------------------

apiVersion: batch/v1
kind: Job
metadata:
  name: airflow-init
spec:
  template:
    spec:
      containers:
      - name: airflow-init
        image: extending_airflow:latest
        imagePullPolicy: IfNotPresent
        env:
          - name: AIRFLOW__CORE__EXECUTOR
            value: "CeleryExecutor"
          - name: AIRFLOW__CORE__PARALLELISM
            value: "32"
          - name: AIRFLOW__CORE__DAG_CONCURRENCY
            value: "16"
          - name: AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG
            value: "4"
          - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
            value: "postgresql+psycopg2://airflow:airflow@postgres/airflow"
          - name: AIRFLOW__CORE__FERNET_KEY
            value: ""
          - name: AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION
            value: "false"
          - name: AIRFLOW__CORE__LOAD_EXAMPLES
            value: "false"
          - name: AIRFLOW__API__AUTH_BACKENDS
            value: "airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session"
          - name: AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK
            value: "true"
          - name: AIRFLOW_CONFIG
            value: "/opt/airflow/config/airflow.cfg"
          - name: AIRFLOW__CELERY__BROKER_URL
            value: "redis://airflow-redis:6379/0"
          - name: AIRFLOW__CELERY__RESULT_BACKEND
            value: "db+*********************************************"
        command: ["/bin/bash", "-c", "if [[ -z \"${AIRFLOW_UID}\" ]]; then echo 'WARNING: AIRFLOW_UID not set!'; fi; airflow db init && airflow users create --username admin --password admin --firstname Admin --lastname User --role Admin --email <EMAIL>"]
        volumeMounts:
        - name: assets
          mountPath: /opt/airflow/pipe
        - name: sources
          mountPath: /sources
      restartPolicy: OnFailure
      volumes:
      - name: assets
        hostPath:
          path: ./assets
      - name: sources
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}
---
# -----------------------------
# Airflow Worker
# -----------------------------

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-worker
spec:
  replicas: 3
  selector:
    matchLabels:
      app: airflow-worker
  template:
    metadata:
      labels:
        app: airflow-worker
    spec:
      containers:
      - name: airflow-worker
        image: extending_airflow:latest
        imagePullPolicy: IfNotPresent
        env:
          - name: AIRFLOW__CORE__EXECUTOR
            value: "CeleryExecutor"
          - name: AIRFLOW__CORE__PARALLELISM
            value: "32"
          - name: AIRFLOW__CORE__DAG_CONCURRENCY
            value: "16"
          - name: AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG
            value: "4"
          - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
            value: "postgresql+psycopg2://airflow:airflow@postgres/airflow"
          - name: AIRFLOW__CORE__FERNET_KEY
            value: ""
          - name: AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION
            value: "false"
          - name: AIRFLOW__CORE__LOAD_EXAMPLES
            value: "false"
          - name: AIRFLOW__API__AUTH_BACKENDS
            value: "airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session"
          - name: AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK
            value: "true"
          - name: AIRFLOW_CONFIG
            value: "/opt/airflow/config/airflow.cfg"
          - name: AIRFLOW__CELERY__BROKER_URL
            value: "redis://airflow-redis:6379/0"
          - name: AIRFLOW__CELERY__RESULT_BACKEND
            value: "db+*********************************************"
        command: ["airflow", "celery", "worker"]
        volumeMounts:
        - name: dags
          hostPath:
            path: ${AIRFLOW_PROJ_DIR:-.}/dags
        - name: logs
          hostPath:
            path: ${AIRFLOW_PROJ_DIR:-.}/logs
        - name: config
          hostPath:
            path: ${AIRFLOW_PROJ_DIR:-.}/config
        - name: plugins
          hostPath:
            path: ${AIRFLOW_PROJ_DIR:-.}/plugins
        - name: assets
          hostPath:
            path: ./assets
      volumes:
      - name: dags
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/dags
      - name: logs
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/logs
      - name: config
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/config
      - name: plugins
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/plugins
      - name: assets
        hostPath:
          path: ./assets

# -----------------------------
# Airflow Flower
# -----------------------------

apiVersion: apps/v1
kind: Deployment
metadata:
  name: airflow-flower
spec:
  replicas: 1
  selector:
    matchLabels:
      app: airflow-flower
  template:
    metadata:
      labels:
        app: airflow-flower
    spec:
      containers:
      - name: airflow-flower
        image: extending_airflow:latest
        imagePullPolicy: IfNotPresent
        env:
          - name: AIRFLOW__CORE__EXECUTOR
            value: "CeleryExecutor"
          - name: AIRFLOW__CORE__EXECUTOR
            value: "CeleryExecutor"
          - name: AIRFLOW__CORE__PARALLELISM
            value: "32"
          - name: AIRFLOW__CORE__DAG_CONCURRENCY
            value: "16"
          - name: AIRFLOW__CORE__MAX_ACTIVE_RUNS_PER_DAG
            value: "4"
          - name: AIRFLOW__DATABASE__SQL_ALCHEMY_CONN
            value: "postgresql+psycopg2://airflow:airflow@postgres/airflow"
          - name: AIRFLOW__CORE__FERNET_KEY
            value: ""
          - name: AIRFLOW__CORE__DAGS_ARE_PAUSED_AT_CREATION
            value: "false"
          - name: AIRFLOW__CORE__LOAD_EXAMPLES
            value: "false"
          - name: AIRFLOW__API__AUTH_BACKENDS
            value: "airflow.api.auth.backend.basic_auth,airflow.api.auth.backend.session"
          - name: AIRFLOW__SCHEDULER__ENABLE_HEALTH_CHECK
            value: "true"
          - name: AIRFLOW_CONFIG
            value: "/opt/airflow/config/airflow.cfg"
          - name: AIRFLOW__CELERY__BROKER_URL
            value: "redis://airflow-redis:6379/0"
          - name: AIRFLOW__CELERY__RESULT_BACKEND
            value: "db+*********************************************"
        command: ["airflow", "celery", "flower"]
        ports:
        - containerPort: 5555
      volumes:
      - name: dags
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/dags
      - name: logs
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/logs
      - name: config
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/config
      - name: plugins
        hostPath:
          path: ${AIRFLOW_PROJ_DIR:-.}/plugins
      - name: assets
        hostPath:
          path: ./assets
---
apiVersion: v1
kind: Service
metadata:
  name: airflow-flower
spec:
  selector:
    app: airflow-flower
  ports:
    - protocol: TCP
      port: 5555
      targetPort: 5555
  type: ClusterIP