[2025-07-18T11:25:55.744+0000] {processor.py:186} INFO - Started process (PID=191) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:25:55.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:25:55.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:55.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:25:55.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:55.841+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:55.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:25:56.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.066+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.078+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.087+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.098+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.107+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.115+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.126+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.127+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:56.141+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:56.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.142+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:25:56.322+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.583 seconds
[2025-07-18T11:26:27.015+0000] {processor.py:186} INFO - Started process (PID=322) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:27.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:26:27.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:27.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.107+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:27.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:27.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:27.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.444+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:26:27.467+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.459 seconds
[2025-07-18T11:26:57.898+0000] {processor.py:186} INFO - Started process (PID=453) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:57.899+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:26:57.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:57.901+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:58.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.174+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:58.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:58.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:58.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.314+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:26:58.338+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.448 seconds
[2025-07-18T11:27:28.739+0000] {processor.py:186} INFO - Started process (PID=584) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:28.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:27:28.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:28.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.829+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:28.838+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:28.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.951+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:28.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.963+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:27:28.984+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.252 seconds
[2025-07-18T11:27:59.558+0000] {processor.py:186} INFO - Started process (PID=715) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:59.559+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:27:59.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:59.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.644+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:59.654+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:59.770+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:59.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:27:59.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.253 seconds
[2025-07-18T11:28:30.010+0000] {processor.py:186} INFO - Started process (PID=846) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:28:30.012+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:28:30.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:28:30.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.097+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:28:30.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.226+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:28:30.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.247 seconds
[2025-07-18T11:29:00.413+0000] {processor.py:186} INFO - Started process (PID=977) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:00.414+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:29:00.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.417+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:00.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.498+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:00.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:00.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.616+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:00.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.629+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:29:00.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.245 seconds
[2025-07-18T11:29:31.104+0000] {processor.py:186} INFO - Started process (PID=1108) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:31.105+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:29:31.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:31.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.196+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.207+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:31.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.314+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.326+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:29:31.346+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.249 seconds
[2025-07-18T11:30:02.078+0000] {processor.py:186} INFO - Started process (PID=1239) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:02.079+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:30:02.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.081+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:02.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.164+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.176+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:02.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.291+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.303+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:30:02.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.254 seconds
[2025-07-18T11:30:32.633+0000] {processor.py:186} INFO - Started process (PID=1370) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:32.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:30:32.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:32.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.749+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:32.759+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:32.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.872+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:32.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:30:32.908+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.281 seconds
[2025-07-18T11:31:03.129+0000] {processor.py:186} INFO - Started process (PID=1501) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:03.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:31:03.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:03.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.260+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:03.273+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:03.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.424+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:03.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:31:03.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.343 seconds
[2025-07-18T11:31:34.364+0000] {processor.py:186} INFO - Started process (PID=1632) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:34.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:31:34.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:34.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.462+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:34.470+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:34.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.607+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:34.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.623+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:31:34.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.291 seconds
