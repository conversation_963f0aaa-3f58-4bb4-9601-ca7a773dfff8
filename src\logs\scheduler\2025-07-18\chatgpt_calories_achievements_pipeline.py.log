[2025-07-18T11:25:55.744+0000] {processor.py:186} INFO - Started process (PID=191) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:25:55.745+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:25:55.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:55.746+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:25:55.841+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:55.841+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:55.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:25:56.067+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.066+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.078+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.087+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.098+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.098+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.107+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.115+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.126+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.126+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.127+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:56.141+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:56.143+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.142+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_calories_achievements_pipeline
[2025-07-18T11:25:56.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:56.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:25:56.322+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.583 seconds
[2025-07-18T11:26:27.015+0000] {processor.py:186} INFO - Started process (PID=322) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:27.016+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:26:27.020+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.019+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:27.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.107+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:27.117+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:27.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.431+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:27.445+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:27.444+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:26:27.467+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.459 seconds
[2025-07-18T11:26:57.898+0000] {processor.py:186} INFO - Started process (PID=453) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:57.899+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:26:57.902+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:57.901+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:58.174+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.174+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:58.182+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:26:58.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.299+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:58.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:58.314+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:26:58.338+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.448 seconds
[2025-07-18T11:27:28.739+0000] {processor.py:186} INFO - Started process (PID=584) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:28.740+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:27:28.744+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.744+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:28.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.829+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:28.838+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:28.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.951+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:28.963+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:28.963+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:27:28.984+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.252 seconds
[2025-07-18T11:27:59.558+0000] {processor.py:186} INFO - Started process (PID=715) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:59.559+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:27:59.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:59.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.644+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:59.654+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:27:59.770+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.770+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:59.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:59.781+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:27:59.804+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.253 seconds
[2025-07-18T11:28:30.010+0000] {processor.py:186} INFO - Started process (PID=846) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:28:30.012+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:28:30.015+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.015+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:28:30.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.097+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:30.110+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:28:30.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:30.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:30.226+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:28:30.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.247 seconds
[2025-07-18T11:29:00.413+0000] {processor.py:186} INFO - Started process (PID=977) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:00.414+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:29:00.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.417+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:00.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.498+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:00.508+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:00.616+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.616+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:00.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:00.629+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:29:00.650+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.245 seconds
[2025-07-18T11:29:31.104+0000] {processor.py:186} INFO - Started process (PID=1108) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:31.105+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:29:31.108+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.108+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:31.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.196+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:31.207+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:29:31.314+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.314+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:31.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:31.326+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:29:31.346+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.249 seconds
[2025-07-18T11:30:02.078+0000] {processor.py:186} INFO - Started process (PID=1239) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:02.079+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:30:02.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.081+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:02.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.164+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:02.176+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:02.291+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.291+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:02.304+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:02.303+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:30:02.326+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.254 seconds
[2025-07-18T11:30:32.633+0000] {processor.py:186} INFO - Started process (PID=1370) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:32.635+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:30:32.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.637+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:32.750+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.749+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:32.759+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:30:32.873+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.872+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:32.884+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:32.884+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:30:32.908+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.281 seconds
[2025-07-18T11:31:03.129+0000] {processor.py:186} INFO - Started process (PID=1501) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:03.130+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:31:03.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.133+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:03.260+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.260+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:03.273+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:03.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.424+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:03.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:03.440+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:31:03.465+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.343 seconds
[2025-07-18T11:31:34.364+0000] {processor.py:186} INFO - Started process (PID=1632) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:34.365+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:31:34.368+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.367+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:34.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.462+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:34.470+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:31:34.607+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.607+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:34.623+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:34.623+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:31:34.647+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.291 seconds
[2025-07-18T11:32:05.004+0000] {processor.py:186} INFO - Started process (PID=1763) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:32:05.005+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:32:05.008+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.007+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:32:05.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.086+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:05.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:32:05.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.205+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:05.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:05.219+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:32:05.243+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.246 seconds
[2025-07-18T11:32:35.481+0000] {processor.py:186} INFO - Started process (PID=1894) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:32:35.482+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:32:35.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.484+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:32:35.568+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.568+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:35.577+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:32:35.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.693+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:35.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:35.705+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:32:35.726+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.251 seconds
[2025-07-18T11:33:05.933+0000] {processor.py:186} INFO - Started process (PID=2025) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:33:05.933+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:33:05.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:05.935+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:33:06.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.018+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:06.028+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:33:06.147+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.147+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:06.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:06.158+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:33:06.178+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.254 seconds
[2025-07-18T11:33:36.510+0000] {processor.py:186} INFO - Started process (PID=2157) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:33:36.511+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:33:36.513+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:36.513+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:33:36.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:36.936+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:36.946+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:33:37.061+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:37.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:37.073+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:33:37.095+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.591 seconds
[2025-07-18T11:34:07.199+0000] {processor.py:186} INFO - Started process (PID=2310) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:34:07.200+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:34:07.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:34:07.625+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.624+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:07.634+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:34:07.751+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.750+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:07.761+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:07.761+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:34:07.787+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.598 seconds
[2025-07-18T11:34:38.428+0000] {processor.py:186} INFO - Started process (PID=2463) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:34:38.429+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:34:38.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:38.432+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:34:38.871+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:38.871+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:38.879+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:34:38.996+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:38.995+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:39.009+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:39.009+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:34:39.249+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.829 seconds
[2025-07-18T11:35:09.701+0000] {processor.py:186} INFO - Started process (PID=2616) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:35:09.703+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:35:09.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:09.705+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:35:10.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.083+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:10.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:35:10.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:10.390+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:10.390+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:35:10.408+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.715 seconds
[2025-07-18T11:35:40.544+0000] {processor.py:186} INFO - Started process (PID=2769) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:35:40.545+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:35:40.549+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:40.548+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:35:40.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:40.945+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:40.957+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:35:41.100+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.099+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:41.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:41.289+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:35:41.310+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.774 seconds
[2025-07-18T11:36:11.842+0000] {processor.py:186} INFO - Started process (PID=2922) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:36:11.843+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:36:11.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:11.845+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:36:12.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.239+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:12.249+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:36:12.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.380+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:12.573+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:12.572+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:36:12.590+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.754 seconds
[2025-07-18T11:36:42.760+0000] {processor.py:186} INFO - Started process (PID=3075) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:36:42.761+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:36:42.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:42.763+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:36:43.140+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.140+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:43.150+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:36:43.423+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.423+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:43.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:43.433+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:36:43.454+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.701 seconds
[2025-07-18T11:37:13.689+0000] {processor.py:186} INFO - Started process (PID=3229) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:37:13.690+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:37:13.693+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:13.693+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:37:14.078+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.078+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:14.087+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:37:14.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.385+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:14.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:14.394+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:37:14.413+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.732 seconds
[2025-07-18T11:37:44.767+0000] {processor.py:186} INFO - Started process (PID=3382) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:37:44.768+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:37:44.771+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:44.770+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:37:45.164+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.164+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:45.173+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:37:45.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.447+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:45.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:45.457+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:37:45.479+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.720 seconds
[2025-07-18T11:38:15.968+0000] {processor.py:186} INFO - Started process (PID=3535) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:38:15.969+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:38:15.972+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:15.971+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:38:16.370+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:16.369+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:16.380+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:38:16.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:16.652+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:16.664+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:16.664+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:38:16.686+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.725 seconds
[2025-07-18T11:38:47.383+0000] {processor.py:186} INFO - Started process (PID=3688) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:38:47.384+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:38:47.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:47.387+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:38:47.781+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:47.780+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:47.790+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:38:48.080+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:48.080+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:48.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:48.091+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:38:48.107+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.732 seconds
[2025-07-18T11:39:18.293+0000] {processor.py:186} INFO - Started process (PID=3841) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:39:18.294+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:39:18.297+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:18.296+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:39:18.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:18.682+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:18.692+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:39:18.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:18.983+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:18.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:18.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:39:19.015+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.728 seconds
[2025-07-18T11:39:49.234+0000] {processor.py:186} INFO - Started process (PID=3994) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:39:49.235+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:39:49.238+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:39:49.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.793+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:49.802+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:39:49.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.895+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:49.905+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:49.905+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:39:49.926+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.699 seconds
[2025-07-18T11:40:20.050+0000] {processor.py:186} INFO - Started process (PID=4147) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:40:20.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:40:20.053+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.053+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:40:20.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.644+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:20.652+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:40:20.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:20.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:20.763+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:40:20.781+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.737 seconds
[2025-07-18T11:40:51.670+0000] {processor.py:186} INFO - Started process (PID=4300) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:40:51.671+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:40:51.673+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:51.673+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:40:52.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:52.194+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:52.203+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:40:52.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:52.295+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:52.307+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:52.306+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:40:52.324+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.659 seconds
[2025-07-18T11:41:22.776+0000] {processor.py:186} INFO - Started process (PID=4459) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:41:22.777+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:41:22.780+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:22.780+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:41:23.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:23.324+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:23.332+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:41:23.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:23.424+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:23.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:23.434+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:41:23.452+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.681 seconds
[2025-07-18T11:41:53.604+0000] {processor.py:186} INFO - Started process (PID=4618) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:41:53.605+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:41:53.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:53.607+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:41:54.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:54.149+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:54.157+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:41:54.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:54.243+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:54.255+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:54.255+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:41:54.274+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.675 seconds
[2025-07-18T11:42:24.789+0000] {processor.py:186} INFO - Started process (PID=4777) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:42:24.790+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:42:24.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:24.793+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:42:25.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:25.332+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:25.339+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:42:25.439+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:25.438+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:25.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:25.449+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:42:25.470+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.687 seconds
[2025-07-18T11:42:55.574+0000] {processor.py:186} INFO - Started process (PID=4936) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:42:55.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:42:55.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:55.577+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:42:56.075+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:56.075+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:56.083+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:42:56.175+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:56.174+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:56.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:56.186+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:42:56.206+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.638 seconds
[2025-07-18T11:43:26.394+0000] {processor.py:186} INFO - Started process (PID=5095) to work on /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:43:26.395+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py for tasks to queue
[2025-07-18T11:43:26.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:26.398+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:43:26.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:26.899+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:26.907+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_calories_achievements_pipeline' retrieved from /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py
[2025-07-18T11:43:27.001+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:27.000+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:27.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:27.011+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_calories_achievements_pipeline to None, run_after=None
[2025-07-18T11:43:27.030+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_calories_achievements_pipeline.py took 0.643 seconds
