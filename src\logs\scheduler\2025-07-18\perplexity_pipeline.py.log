[2025-07-18T11:26:00.964+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:00.965+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:26:00.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.966+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:01.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.048+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:01.058+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:01.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.300+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:perplexity_pipeline
[2025-07-18T11:26:01.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.310+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:perplexity_pipeline
[2025-07-18T11:26:01.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.316+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:perplexity_pipeline
[2025-07-18T11:26:01.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.326+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:perplexity_pipeline
[2025-07-18T11:26:01.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.334+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:perplexity_pipeline
[2025-07-18T11:26:01.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.341+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:perplexity_pipeline
[2025-07-18T11:26:01.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.349+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:perplexity_pipeline
[2025-07-18T11:26:01.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.350+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:01.361+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:01.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.361+0000] {dag.py:3262} INFO - Creating ORM DAG for perplexity_pipeline
[2025-07-18T11:26:01.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.362+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:26:01.377+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.419 seconds
[2025-07-18T11:26:31.963+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:31.964+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:26:31.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.966+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:32.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.179+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:32.187+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:32.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.279+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:32.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.292+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:26:32.318+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.361 seconds
[2025-07-18T11:27:03.039+0000] {processor.py:186} INFO - Started process (PID=565) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:03.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:27:03.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:03.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.119+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:03.129+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:03.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.231+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:03.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.242+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:27:03.263+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.229 seconds
[2025-07-18T11:27:33.718+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:33.719+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:27:33.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.722+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:33.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.805+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:33.813+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:33.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.922+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.933+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:27:33.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.244 seconds
[2025-07-18T11:28:04.216+0000] {processor.py:186} INFO - Started process (PID=827) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:04.217+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:28:04.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.219+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:04.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.290+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:04.299+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:04.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.391+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:04.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.400+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:28:04.419+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.209 seconds
[2025-07-18T11:28:34.572+0000] {processor.py:186} INFO - Started process (PID=958) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:34.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:28:34.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.576+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:34.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.653+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.663+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:34.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.772+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.784+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:28:34.805+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.239 seconds
[2025-07-18T11:29:05.306+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:05.307+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:29:05.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.309+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:05.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.384+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:05.392+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:05.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:05.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.508+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:29:05.526+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.226 seconds
[2025-07-18T11:29:36.309+0000] {processor.py:186} INFO - Started process (PID=1218) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:36.310+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:29:36.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.313+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:36.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.396+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:36.405+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:36.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.510+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:36.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.523+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:29:36.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.240 seconds
[2025-07-18T11:30:06.981+0000] {processor.py:186} INFO - Started process (PID=1354) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:06.982+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:30:06.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.984+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:07.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.065+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:07.072+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:07.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.171+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:07.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.182+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:30:07.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.227 seconds
[2025-07-18T11:30:37.843+0000] {processor.py:186} INFO - Started process (PID=1480) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:37.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:30:37.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:37.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.946+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.955+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:38.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:38.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:38.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:38.073+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:30:38.092+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.255 seconds
[2025-07-18T11:31:08.565+0000] {processor.py:186} INFO - Started process (PID=1613) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:08.566+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:31:08.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:08.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.645+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:08.655+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:08.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:08.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.766+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:31:08.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.226 seconds
