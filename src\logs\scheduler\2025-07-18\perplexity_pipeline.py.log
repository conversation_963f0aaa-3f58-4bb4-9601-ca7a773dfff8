[2025-07-18T11:26:00.964+0000] {processor.py:186} INFO - Started process (PID=301) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:00.965+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:26:00.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.966+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:01.049+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.048+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:01.058+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:01.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.300+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:perplexity_pipeline
[2025-07-18T11:26:01.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.310+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:perplexity_pipeline
[2025-07-18T11:26:01.317+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.316+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:perplexity_pipeline
[2025-07-18T11:26:01.326+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.326+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:perplexity_pipeline
[2025-07-18T11:26:01.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.334+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:perplexity_pipeline
[2025-07-18T11:26:01.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.341+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:perplexity_pipeline
[2025-07-18T11:26:01.349+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.349+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:perplexity_pipeline
[2025-07-18T11:26:01.350+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.350+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:01.361+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:01.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.361+0000] {dag.py:3262} INFO - Creating ORM DAG for perplexity_pipeline
[2025-07-18T11:26:01.362+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.362+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:26:01.377+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.419 seconds
[2025-07-18T11:26:31.963+0000] {processor.py:186} INFO - Started process (PID=432) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:31.964+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:26:31.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.966+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:32.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.179+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:32.187+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:26:32.280+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.279+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:32.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.292+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:26:32.318+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.361 seconds
[2025-07-18T11:27:03.039+0000] {processor.py:186} INFO - Started process (PID=565) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:03.040+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:27:03.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.043+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:03.120+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.119+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:03.129+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:03.231+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.231+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:03.243+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:03.242+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:27:03.263+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.229 seconds
[2025-07-18T11:27:33.718+0000] {processor.py:186} INFO - Started process (PID=696) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:33.719+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:27:33.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.722+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:33.805+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.805+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:33.813+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:27:33.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.922+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.933+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:27:33.955+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.244 seconds
[2025-07-18T11:28:04.216+0000] {processor.py:186} INFO - Started process (PID=827) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:04.217+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:28:04.219+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.219+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:04.290+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.290+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:04.299+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:04.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.391+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:04.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.400+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:28:04.419+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.209 seconds
[2025-07-18T11:28:34.572+0000] {processor.py:186} INFO - Started process (PID=958) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:34.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:28:34.576+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.576+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:34.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.653+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.663+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:28:34.772+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.772+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.784+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:28:34.805+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.239 seconds
[2025-07-18T11:29:05.306+0000] {processor.py:186} INFO - Started process (PID=1087) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:05.307+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:29:05.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.309+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:05.384+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.384+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:05.392+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:05.498+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.498+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:05.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.508+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:29:05.526+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.226 seconds
[2025-07-18T11:29:36.309+0000] {processor.py:186} INFO - Started process (PID=1218) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:36.310+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:29:36.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.313+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:36.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.396+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:36.405+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:29:36.510+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.510+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:36.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.523+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:29:36.542+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.240 seconds
[2025-07-18T11:30:06.981+0000] {processor.py:186} INFO - Started process (PID=1354) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:06.982+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:30:06.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.984+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:07.065+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.065+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:07.072+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:07.171+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.171+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:07.182+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.182+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:30:07.201+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.227 seconds
[2025-07-18T11:30:37.843+0000] {processor.py:186} INFO - Started process (PID=1480) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:37.845+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:30:37.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.848+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:37.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.946+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.955+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:30:38.062+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:38.062+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:38.073+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:38.073+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:30:38.092+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.255 seconds
[2025-07-18T11:31:08.565+0000] {processor.py:186} INFO - Started process (PID=1613) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:08.566+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:31:08.569+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.568+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:08.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.645+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:08.655+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:08.756+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.756+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:08.766+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.766+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:31:08.784+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.226 seconds
[2025-07-18T11:31:39.274+0000] {processor.py:186} INFO - Started process (PID=1744) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:39.275+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:31:39.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:39.277+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:39.358+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:39.358+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:39.367+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:31:39.476+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:39.476+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:39.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:39.487+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:31:39.508+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.241 seconds
[2025-07-18T11:32:09.958+0000] {processor.py:186} INFO - Started process (PID=1880) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:32:09.959+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:32:09.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.961+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:32:10.042+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:10.042+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:10.053+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:32:10.168+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:10.168+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:10.179+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:10.179+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:32:10.198+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.247 seconds
[2025-07-18T11:32:41.177+0000] {processor.py:186} INFO - Started process (PID=2011) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:32:41.178+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:32:41.181+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:41.181+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:32:41.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:41.251+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:41.260+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:32:41.357+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:41.356+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:41.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:41.367+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:32:41.385+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.214 seconds
[2025-07-18T11:33:11.857+0000] {processor.py:186} INFO - Started process (PID=2142) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:33:11.858+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:33:11.862+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:11.861+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:33:11.937+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:11.937+0000] {cost_tracking.py:76} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:11.946+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:33:12.046+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:12.045+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:12.059+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:12.058+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:33:12.081+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.230 seconds
[2025-07-18T11:33:43.122+0000] {processor.py:186} INFO - Started process (PID=2293) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:33:43.123+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:33:43.127+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:43.127+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:33:43.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:43.489+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:43.498+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:33:43.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:43.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:43.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:43.621+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:33:43.643+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.528 seconds
[2025-07-18T11:34:14.429+0000] {processor.py:186} INFO - Started process (PID=2441) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:34:14.430+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:34:14.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.433+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:34:14.794+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.794+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:14.802+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:34:14.906+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:14.920+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.919+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:34:14.941+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.519 seconds
[2025-07-18T11:34:45.716+0000] {processor.py:186} INFO - Started process (PID=2594) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:34:45.717+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:34:45.719+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.719+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:34:46.104+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:46.104+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:46.114+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:34:46.228+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:46.228+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:46.240+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:46.240+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:34:46.260+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.551 seconds
[2025-07-18T11:35:17.406+0000] {processor.py:186} INFO - Started process (PID=2746) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:35:17.407+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:35:17.411+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.411+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:35:17.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.826+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:17.835+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:35:17.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.956+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:17.967+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.967+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:35:17.989+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.591 seconds
[2025-07-18T11:35:48.919+0000] {processor.py:186} INFO - Started process (PID=2900) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:35:48.921+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:35:48.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.924+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:35:49.308+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:49.308+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:49.321+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:35:49.434+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:49.434+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:49.446+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:49.445+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:35:49.466+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.554 seconds
[2025-07-18T11:36:20.363+0000] {processor.py:186} INFO - Started process (PID=3058) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:36:20.364+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:36:20.367+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:20.366+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:36:20.758+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:20.758+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:20.771+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:36:20.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:20.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:21.043+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:21.043+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:36:21.064+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.710 seconds
[2025-07-18T11:36:52.451+0000] {processor.py:186} INFO - Started process (PID=3213) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:36:52.452+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:36:52.455+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:52.454+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:36:52.869+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:52.869+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:52.877+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:36:52.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:52.999+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:53.155+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:53.155+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:36:53.175+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.730 seconds
[2025-07-18T11:37:23.574+0000] {processor.py:186} INFO - Started process (PID=3365) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:37:23.575+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:37:23.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:23.578+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:37:23.957+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:23.957+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:23.967+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:37:24.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:24.224+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:24.234+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:24.234+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:37:24.257+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.690 seconds
[2025-07-18T11:37:54.969+0000] {processor.py:186} INFO - Started process (PID=3518) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:37:54.970+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:37:54.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:54.973+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:37:55.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:55.373+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:55.385+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:37:55.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:55.628+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:55.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:55.637+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:37:55.658+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.695 seconds
[2025-07-18T11:38:26.262+0000] {processor.py:186} INFO - Started process (PID=3670) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:38:26.263+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:38:26.266+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.266+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:38:26.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.608+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:26.617+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:38:26.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.853+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:26.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.862+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:38:26.881+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.626 seconds
[2025-07-18T11:38:57.115+0000] {processor.py:186} INFO - Started process (PID=3823) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:38:57.116+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:38:57.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:57.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:38:57.652+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:57.652+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:57.659+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:38:57.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:57.762+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:57.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:57.774+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:38:57.797+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.689 seconds
[2025-07-18T11:39:28.245+0000] {processor.py:186} INFO - Started process (PID=3982) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:39:28.246+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:39:28.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.248+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:39:28.788+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.788+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:28.796+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:39:28.899+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.898+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:28.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.909+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:39:28.931+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.694 seconds
[2025-07-18T11:39:59.228+0000] {processor.py:186} INFO - Started process (PID=4135) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:39:59.229+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:39:59.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.232+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:39:59.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.727+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:59.735+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:39:59.829+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:59.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.839+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:39:59.859+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.637 seconds
[2025-07-18T11:40:30.431+0000] {processor.py:186} INFO - Started process (PID=4288) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:40:30.434+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:40:30.438+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:30.437+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:40:31.010+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:31.010+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:31.018+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:40:31.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:31.133+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:31.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:31.145+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:40:31.168+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.747 seconds
[2025-07-18T11:41:01.606+0000] {processor.py:186} INFO - Started process (PID=4447) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:41:01.607+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:41:01.609+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:01.609+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:41:02.145+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:02.145+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:02.151+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:41:02.257+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:02.256+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:02.269+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:02.269+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:41:02.290+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.690 seconds
[2025-07-18T11:41:32.480+0000] {processor.py:186} INFO - Started process (PID=4606) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:41:32.481+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:41:32.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:32.483+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:41:32.999+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:32.999+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:33.007+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:41:33.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:33.106+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:33.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:33.116+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:41:33.137+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.663 seconds
[2025-07-18T11:42:03.682+0000] {processor.py:186} INFO - Started process (PID=4765) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:42:03.683+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:42:03.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:03.685+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:42:04.213+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:04.213+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:04.222+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:42:04.332+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:04.332+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:04.341+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:04.341+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:42:04.358+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.683 seconds
[2025-07-18T11:42:34.433+0000] {processor.py:186} INFO - Started process (PID=4924) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:42:34.434+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:42:34.437+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:34.436+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:42:34.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:34.955+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:34.963+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:42:35.066+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:35.066+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:35.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:35.079+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:42:35.107+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.680 seconds
[2025-07-18T11:43:05.278+0000] {processor.py:186} INFO - Started process (PID=5083) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:43:05.280+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:43:05.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:05.282+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:43:05.768+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:05.768+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:05.777+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:43:05.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:05.888+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:05.901+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:05.901+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:43:05.923+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.650 seconds
[2025-07-18T11:43:36.486+0000] {processor.py:186} INFO - Started process (PID=5242) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:43:36.488+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:43:36.491+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:36.490+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:43:37.074+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:37.073+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:37.080+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:43:37.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:37.178+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:37.189+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:37.188+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:43:37.206+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.727 seconds
[2025-07-18T11:44:07.283+0000] {processor.py:186} INFO - Started process (PID=5401) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:44:07.284+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:44:07.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:07.286+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:44:07.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:07.808+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:07.818+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:44:07.923+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:07.923+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:07.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:07.936+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:44:07.957+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.680 seconds
[2025-07-18T11:44:38.423+0000] {processor.py:186} INFO - Started process (PID=5560) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:44:38.423+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:44:38.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:38.426+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:44:38.961+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:38.961+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:38.970+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:44:39.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:39.075+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:39.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:39.087+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:44:39.110+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.693 seconds
[2025-07-18T11:45:09.773+0000] {processor.py:186} INFO - Started process (PID=5719) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:45:09.775+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:45:09.778+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:09.778+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:45:10.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:10.300+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:10.307+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:45:10.410+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:10.409+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:10.420+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:10.420+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:45:10.439+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.675 seconds
[2025-07-18T11:45:40.694+0000] {processor.py:186} INFO - Started process (PID=5878) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:45:40.695+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:45:40.701+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:40.700+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:45:41.251+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:41.251+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:41.260+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:45:41.369+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:41.369+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:41.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:41.380+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:45:41.396+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.712 seconds
[2025-07-18T11:46:11.950+0000] {processor.py:186} INFO - Started process (PID=6037) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:46:11.952+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:46:11.956+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:11.956+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:46:12.523+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:12.523+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:12.531+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:46:12.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:12.650+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:12.662+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:12.661+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:46:12.681+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.739 seconds
[2025-07-18T11:46:42.800+0000] {processor.py:186} INFO - Started process (PID=6196) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:46:42.801+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:46:42.804+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:42.804+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:46:43.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:43.352+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:43.361+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:46:43.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:43.461+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:43.472+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:43.471+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:46:43.496+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.703 seconds
[2025-07-18T11:47:13.686+0000] {processor.py:186} INFO - Started process (PID=6355) to work on /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:47:13.687+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/perplexity_pipeline.py for tasks to queue
[2025-07-18T11:47:13.690+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:13.689+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:47:14.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:14.275+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:14.284+0000] {processor.py:925} INFO - DAG(s) 'perplexity_pipeline' retrieved from /opt/airflow/dags/perplexity_pipeline.py
[2025-07-18T11:47:14.394+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:14.393+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:14.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:14.407+0000] {dag.py:4180} INFO - Setting next_dagrun for perplexity_pipeline to None, run_after=None
[2025-07-18T11:47:14.429+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/perplexity_pipeline.py took 0.749 seconds
