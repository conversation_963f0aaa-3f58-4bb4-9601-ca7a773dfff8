[2025-07-18T11:25:59.289+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:25:59.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:25:59.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.293+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:25:59.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:59.384+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:25:59.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.633+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_message_pipeline
[2025-07-18T11:25:59.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.645+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_message_pipeline
[2025-07-18T11:25:59.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.653+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_message_pipeline
[2025-07-18T11:25:59.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.662+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_message_pipeline
[2025-07-18T11:25:59.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.670+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_message_pipeline
[2025-07-18T11:25:59.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.679+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_message_pipeline
[2025-07-18T11:25:59.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.687+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_message_pipeline
[2025-07-18T11:25:59.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.688+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:59.702+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:59.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.702+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_message_pipeline
[2025-07-18T11:25:59.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.703+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:25:59.722+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.439 seconds
[2025-07-18T11:26:30.245+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:26:30.246+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:26:30.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.248+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:26:30.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.460+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.467+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:26:30.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.564+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.573+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:26:30.590+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.350 seconds
[2025-07-18T11:27:01.810+0000] {processor.py:186} INFO - Started process (PID=523) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:01.811+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:27:01.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.813+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:01.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.904+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.912+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:02.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.029+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.040+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:27:02.060+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.257 seconds
[2025-07-18T11:27:32.260+0000] {processor.py:186} INFO - Started process (PID=656) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:32.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:27:32.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:32.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.336+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.346+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:32.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.450+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.462+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:27:32.482+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.228 seconds
[2025-07-18T11:28:02.792+0000] {processor.py:186} INFO - Started process (PID=787) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:02.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:28:02.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.794+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:02.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.866+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.875+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:02.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.978+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.991+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:28:03.011+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.225 seconds
[2025-07-18T11:28:33.393+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:33.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:28:33.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.396+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:33.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.469+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:33.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:33.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.583+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.594+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:28:33.617+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.229 seconds
[2025-07-18T11:29:04.421+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:04.422+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:29:04.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.424+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:04.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.504+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:04.515+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:04.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.627+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:04.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:29:04.660+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.245 seconds
[2025-07-18T11:29:35.208+0000] {processor.py:186} INFO - Started process (PID=1180) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:35.209+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:29:35.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.211+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:35.284+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.283+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.293+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:35.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.393+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:35.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.406+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:29:35.428+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.227 seconds
[2025-07-18T11:30:05.605+0000] {processor.py:186} INFO - Started process (PID=1311) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:05.606+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:30:05.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:05.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.679+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.687+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:05.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.794+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.806+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:30:05.828+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.229 seconds
[2025-07-18T11:30:36.724+0000] {processor.py:186} INFO - Started process (PID=1442) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:36.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:30:36.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:36.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.823+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.832+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:36.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.941+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:30:36.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.245 seconds
[2025-07-18T11:31:07.120+0000] {processor.py:186} INFO - Started process (PID=1571) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:07.121+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:31:07.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.124+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:07.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.192+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:07.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.302+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.312+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:31:07.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.217 seconds
