[2025-07-18T11:25:59.289+0000] {processor.py:186} INFO - Started process (PID=261) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:25:59.290+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:25:59.293+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.293+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:25:59.373+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.373+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:59.384+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:25:59.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.633+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_message_pipeline
[2025-07-18T11:25:59.645+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.645+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_message_pipeline
[2025-07-18T11:25:59.653+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.653+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_message_pipeline
[2025-07-18T11:25:59.663+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.662+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_message_pipeline
[2025-07-18T11:25:59.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.670+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_message_pipeline
[2025-07-18T11:25:59.680+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.679+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_message_pipeline
[2025-07-18T11:25:59.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.687+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_message_pipeline
[2025-07-18T11:25:59.688+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.688+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:25:59.702+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:25:59.703+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.702+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_message_pipeline
[2025-07-18T11:25:59.704+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.703+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:25:59.722+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.439 seconds
[2025-07-18T11:26:30.245+0000] {processor.py:186} INFO - Started process (PID=392) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:26:30.246+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:26:30.249+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.248+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:26:30.461+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.460+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.467+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:26:30.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.564+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.574+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.573+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:26:30.590+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.350 seconds
[2025-07-18T11:27:01.810+0000] {processor.py:186} INFO - Started process (PID=523) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:01.811+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:27:01.814+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.813+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:01.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:01.904+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:01.912+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:02.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.029+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.040+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.040+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:27:02.060+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.257 seconds
[2025-07-18T11:27:32.260+0000] {processor.py:186} INFO - Started process (PID=656) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:32.261+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:27:32.264+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.264+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:32.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.336+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.346+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:27:32.450+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.450+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.462+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.462+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:27:32.482+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.228 seconds
[2025-07-18T11:28:02.792+0000] {processor.py:186} INFO - Started process (PID=787) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:02.793+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:28:02.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.794+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:02.866+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.866+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:02.875+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:02.978+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.978+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:02.991+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:02.991+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:28:03.011+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.225 seconds
[2025-07-18T11:28:33.393+0000] {processor.py:186} INFO - Started process (PID=918) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:33.394+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:28:33.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.396+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:33.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.469+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:33.479+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:28:33.583+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.583+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.594+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.594+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:28:33.617+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.229 seconds
[2025-07-18T11:29:04.421+0000] {processor.py:186} INFO - Started process (PID=1049) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:04.422+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:29:04.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.424+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:04.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.504+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:04.515+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:04.627+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.627+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:04.640+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.639+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:29:04.660+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.245 seconds
[2025-07-18T11:29:35.208+0000] {processor.py:186} INFO - Started process (PID=1180) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:35.209+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:29:35.211+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.211+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:35.284+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.283+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.293+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:29:35.393+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.393+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:35.407+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.406+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:29:35.428+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.227 seconds
[2025-07-18T11:30:05.605+0000] {processor.py:186} INFO - Started process (PID=1311) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:05.606+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:30:05.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.608+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:05.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.679+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.687+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:05.795+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.794+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:05.806+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.806+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:30:05.828+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.229 seconds
[2025-07-18T11:30:36.724+0000] {processor.py:186} INFO - Started process (PID=1442) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:36.726+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:30:36.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.728+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:36.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.823+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:36.832+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:30:36.929+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.929+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:36.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:36.941+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:30:36.964+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.245 seconds
[2025-07-18T11:31:07.120+0000] {processor.py:186} INFO - Started process (PID=1571) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:07.121+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:31:07.124+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.124+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:07.193+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.192+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.204+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:07.302+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.302+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.312+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.312+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:31:07.331+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.217 seconds
[2025-07-18T11:31:37.845+0000] {processor.py:186} INFO - Started process (PID=1704) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:37.846+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:31:37.849+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.849+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:37.924+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:37.924+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:37.931+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:31:38.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.030+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:38.041+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.040+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:31:38.057+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.218 seconds
[2025-07-18T11:32:08.321+0000] {processor.py:186} INFO - Started process (PID=1835) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:32:08.321+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:32:08.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:32:08.398+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.398+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:08.406+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:32:08.505+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.505+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:08.516+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.516+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:32:08.536+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.223 seconds
[2025-07-18T11:32:38.952+0000] {processor.py:186} INFO - Started process (PID=1966) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:32:38.953+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:32:38.955+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:38.955+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:32:39.030+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.030+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:39.038+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:32:39.134+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.134+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:39.144+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.144+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:32:39.164+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.219 seconds
[2025-07-18T11:33:09.511+0000] {processor.py:186} INFO - Started process (PID=2097) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:33:09.512+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:33:09.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.515+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:33:09.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.598+0000] {cost_tracking.py:76} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:09.607+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:33:09.706+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.705+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:09.716+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.716+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:33:09.735+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.230 seconds
[2025-07-18T11:33:40.946+0000] {processor.py:186} INFO - Started process (PID=2241) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:33:40.948+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:33:40.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:40.950+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:33:41.353+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.352+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:41.360+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:33:41.477+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.477+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:41.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.490+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:33:41.520+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.581 seconds
[2025-07-18T11:34:12.324+0000] {processor.py:186} INFO - Started process (PID=2394) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:34:12.325+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:34:12.328+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.328+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:34:12.699+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.699+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:12.706+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:34:12.812+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.811+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:12.824+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.824+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:34:12.846+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.528 seconds
[2025-07-18T11:34:43.678+0000] {processor.py:186} INFO - Started process (PID=2547) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:34:43.679+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:34:43.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:43.682+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:34:44.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.047+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:44.057+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:34:44.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.162+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:44.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.173+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:34:44.194+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.524 seconds
[2025-07-18T11:35:15.318+0000] {processor.py:186} INFO - Started process (PID=2700) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:35:15.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:35:15.324+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.323+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:35:15.711+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.710+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:15.720+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:35:15.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.838+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:15.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.850+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:35:15.871+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.564 seconds
[2025-07-18T11:35:46.573+0000] {processor.py:186} INFO - Started process (PID=2853) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:35:46.574+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:35:46.578+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.577+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:35:46.953+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:46.952+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:46.963+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:35:47.072+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.071+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:47.083+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:35:47.108+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.543 seconds
[2025-07-18T11:36:17.718+0000] {processor.py:186} INFO - Started process (PID=3006) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:36:17.719+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:36:17.724+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:17.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:36:18.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.136+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:18.150+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:36:18.296+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.296+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:18.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.311+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:36:18.335+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.623 seconds
[2025-07-18T11:36:49.398+0000] {processor.py:186} INFO - Started process (PID=3161) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:36:49.399+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:36:49.403+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:49.402+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:36:49.848+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:49.848+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:49.856+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:36:49.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:49.982+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:49.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:49.993+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:36:50.016+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.625 seconds
[2025-07-18T11:37:20.483+0000] {processor.py:186} INFO - Started process (PID=3315) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:37:20.484+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:37:20.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:20.487+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:37:20.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:20.883+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:20.891+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:37:21.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.161+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:21.173+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.173+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:37:21.194+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.718 seconds
[2025-07-18T11:37:51.693+0000] {processor.py:186} INFO - Started process (PID=3466) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:37:51.695+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:37:51.698+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:51.698+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:37:52.149+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.148+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:52.156+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:37:52.396+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.395+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:52.406+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.406+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:37:52.428+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.744 seconds
[2025-07-18T11:38:22.557+0000] {processor.py:186} INFO - Started process (PID=3619) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:38:22.558+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:38:22.561+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.560+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:38:22.945+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:22.944+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:22.951+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:38:23.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.195+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:23.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.205+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:38:23.227+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.676 seconds
[2025-07-18T11:38:54.175+0000] {processor.py:186} INFO - Started process (PID=3774) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:38:54.176+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:38:54.178+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.178+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:38:54.718+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.717+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:54.725+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:38:54.828+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.828+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:54.839+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.839+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:38:54.859+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.690 seconds
[2025-07-18T11:39:25.372+0000] {processor.py:186} INFO - Started process (PID=3927) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:39:25.373+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:39:25.376+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:25.375+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:39:25.936+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:25.936+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:25.942+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:39:26.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.045+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:26.058+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.058+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:39:26.078+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.712 seconds
[2025-07-18T11:39:56.228+0000] {processor.py:186} INFO - Started process (PID=4080) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:39:56.229+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:39:56.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.232+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:39:56.774+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.774+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:56.782+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:39:56.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.881+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:56.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:56.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:39:56.911+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.689 seconds
[2025-07-18T11:40:27.263+0000] {processor.py:186} INFO - Started process (PID=4231) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:40:27.265+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:40:27.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.267+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:40:27.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.831+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:27.838+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:40:27.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.941+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:27.950+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:27.950+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:40:27.970+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.714 seconds
[2025-07-18T11:40:58.299+0000] {processor.py:186} INFO - Started process (PID=4396) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:40:58.301+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:40:58.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.303+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:40:58.782+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.782+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:58.789+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:40:58.883+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.883+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:58.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:58.893+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:40:58.910+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.617 seconds
[2025-07-18T11:41:29.105+0000] {processor.py:186} INFO - Started process (PID=4555) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:41:29.106+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:41:29.109+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.109+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:41:29.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.615+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:29.623+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:41:29.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.730+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:29.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.740+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:41:29.759+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.659 seconds
[2025-07-18T11:42:00.201+0000] {processor.py:186} INFO - Started process (PID=4716) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:42:00.203+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:42:00.205+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.205+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:42:00.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.677+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:00.685+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:42:00.775+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.775+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:00.784+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.784+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:42:00.802+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.607 seconds
[2025-07-18T11:42:31.021+0000] {processor.py:186} INFO - Started process (PID=4873) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:42:31.022+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:42:31.025+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.025+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:42:31.515+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.515+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:31.523+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:42:31.615+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.615+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:31.624+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.624+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:42:31.642+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.627 seconds
[2025-07-18T11:43:02.052+0000] {processor.py:186} INFO - Started process (PID=5037) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:43:02.054+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:43:02.057+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.057+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:43:02.530+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.530+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:02.538+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:43:02.631+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.631+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:02.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.640+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:43:02.661+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.617 seconds
[2025-07-18T11:43:32.761+0000] {processor.py:186} INFO - Started process (PID=5196) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:43:32.762+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:43:32.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:32.764+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:43:33.216+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:33.216+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:33.224+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:43:33.310+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:33.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:33.320+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:33.320+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:43:33.340+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.586 seconds
[2025-07-18T11:44:04.145+0000] {processor.py:186} INFO - Started process (PID=5355) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:44:04.147+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:44:04.151+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.150+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:44:04.643+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.642+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:04.651+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:44:04.748+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.747+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:04.757+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.757+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:44:04.779+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.640 seconds
[2025-07-18T11:44:35.095+0000] {processor.py:186} INFO - Started process (PID=5513) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:44:35.096+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:44:35.099+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:35.099+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:44:35.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:35.604+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:35.615+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:44:35.729+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:35.728+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:35.740+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:35.740+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:44:35.761+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.672 seconds
[2025-07-18T11:45:06.179+0000] {processor.py:186} INFO - Started process (PID=5673) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:45:06.180+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:45:06.183+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:06.182+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:45:06.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:06.658+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:06.665+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:45:06.754+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:06.754+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:06.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:06.764+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:45:06.785+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.612 seconds
[2025-07-18T11:45:37.536+0000] {processor.py:186} INFO - Started process (PID=5832) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:45:37.537+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:45:37.540+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:37.540+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:45:38.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.091+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:38.097+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:45:38.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.193+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:38.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.203+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:45:38.222+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.694 seconds
[2025-07-18T11:46:08.331+0000] {processor.py:186} INFO - Started process (PID=5991) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:46:08.332+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:46:08.335+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:08.335+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:46:08.888+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:08.888+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:08.895+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:46:09.003+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:09.002+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:09.014+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:09.014+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:46:09.034+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.711 seconds
[2025-07-18T11:46:39.145+0000] {processor.py:186} INFO - Started process (PID=6149) to work on /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:46:39.146+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_pipeline.py for tasks to queue
[2025-07-18T11:46:39.148+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:39.147+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:46:39.617+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:39.616+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:39.624+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_pipeline.py
[2025-07-18T11:46:39.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:39.721+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:39.732+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:39.731+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_pipeline to None, run_after=None
[2025-07-18T11:46:39.750+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_pipeline.py took 0.612 seconds
