# Система отслеживания стоимости (Cost Tracking)

Комплексная система для отслеживания затрат на использование AI пайплайнов с автоматическим экспортом в CSV и Google Sheets.

## 🎯 Возможности

- ✅ Отслеживание входящих и исходящих токенов для всех пайплайнов
- ✅ Расчет стоимости в USD для input и output токенов
- ✅ Автоматическое сохранение данных в Redis
- ✅ Экспорт данных в CSV с русскими названиями пайплайнов
- ✅ Интеграция с Google Sheets для автоматической загрузки отчетов
- ✅ Автоматизированный DAG для регулярного экспорта
- ✅ Агрегация данных по пайплайнам с итоговыми суммами

## 📊 Структура данных в отчете

| Колонка | Описание |
|---------|----------|
| Название | Русское название пайплайна |
| Tokens (input) | Количество входящих токенов |
| Tokens (output) | Количество исходящих токенов |
| Tokens (Всего) | Общее количество токенов |
| Стоимость (input) USD | Стоимость входящих токенов в долларах |
| Стоимость (output) USD | Стоимость исходящих токенов в долларах |
| Стоимость (Всего) USD | Общая стоимость в долларах |

## 🗺️ Маппинг пайплайнов

| Английское название | Русское название |
|-------------------|------------------|
| chatgpt_message_recommendation_pipeline | ГЕНЕРАЦИЯ РИСКОВ И РЕКОМЕНДАЦИЙ |
| chatgpt_message_pipeline | СООБЩЕНИЕ ПО РИСКУ |
| chatgpt_message_risk_pipeline | СООБЩЕНИЕ ПО РЕКОМЕНДАЦИИ |
| chatgpt_metrics_pipeline | АНАЛИЗ МЕТРИК |
| chatgpt_classify_pipeline | КЛАССИФИКАТОР СООБЩЕНИЙ |
| chatgpt_generate_challenges_pipeline | ГЕНЕРАЦИЯ ЧЕЛЛЕНДЖЕЙ В РЕЖИМЕ АННОТИРОВАНИЯ |
| chatgpt_pdf_pipeline | РЕЖИМ ОБЫЧНОГО ОБЩЕНИЯ В ЧАТЕ |
| chatgpt_image_pipeline | АНАЛИЗ ФОТО (ТЕКСТОВЫЙ АНАЛИЗ) |
| chatgpt_goals_pipeline | ОПРЕДЕЛЕНИЕ ЦЕЛЕЙ ПОЛЬЗОВАТЕЛЯ |
| chatgpt_unified_question_pipeline | ГЕНЕРАЦИЯ ПО РУССКИМ АНКЕТИРОВАНИЯМ |
| chatgpt_analyze_food_pipeline | ГЕНЕРАЦИЯ ОТКРЫВАЮЩЕГО СООБЩЕНИЯ ДЛЯ РИСК+РЕКОМЕНДАЦИИ |
| chatgpt_image_object_pipeline | ОРГАНИЗАЦИЯ ВОПРОСОВ ДЛЯ ПОСТОНБОРДИНГА |
| chatgpt_describe_challenge_pipeline | ГЕНЕРАЦИЯ СООБЩЕНИЙ ПО АНАЛИЗУ ФОТО |

## 🚀 Быстрый старт

### 1. Установка зависимостей

```bash
pip install google-auth google-auth-oauthlib google-auth-httplib2 google-api-python-client pandas redis
```

### 2. Экспорт только в CSV

```bash
python src/scripts/cost_tracking_export.py --mode csv --output cost_data.csv --days 30
```

### 3. Полный процесс (CSV + Google Sheets)

```bash
python src/scripts/cost_tracking_export.py --mode full \
  --spreadsheet-id "YOUR_SPREADSHEET_ID" \
  --credentials "path/to/google_sheets_credentials.json" \
  --days 30
```

## 🔧 Настройка

### Google Sheets API

1. Следуйте инструкциям в `GOOGLE_SHEETS_SETUP.md`
2. Получите файл учетных данных `google_sheets_credentials.json`
3. Создайте Google Sheets документ и получите его ID

### Автоматизация через Airflow

1. Следуйте инструкциям в `AUTOMATION_SETUP.md`
2. Настройте переменные в Airflow:
   - `COST_TRACKING_SPREADSHEET_ID`
   - `GOOGLE_SHEETS_CREDENTIALS_PATH`
3. Активируйте DAG `cost_tracking_export`

## 🧪 Тестирование

Запустите тестовый скрипт для проверки всей функциональности:

```bash
python src/scripts/test_cost_tracking.py
```

Тест проверяет:
- ✅ Создание тестовых данных в Redis
- ✅ Функцию track_openai_cost
- ✅ Маппинг пайплайнов на русские названия
- ✅ Экспорт данных в CSV
- ✅ Очистку тестовых данных

## 📁 Структура файлов

```
src/
├── dags/
│   ├── cost_tracking.py              # Основной модуль отслеживания затрат
│   └── cost_tracking_export_dag.py   # DAG для автоматического экспорта
└── scripts/
    ├── cost_tracking_export.py       # Скрипт для ручного экспорта
    ├── test_cost_tracking.py         # Тестовый скрипт
    ├── GOOGLE_SHEETS_SETUP.md        # Инструкции по настройке Google Sheets
    ├── AUTOMATION_SETUP.md           # Инструкции по настройке автоматизации
    └── README_COST_TRACKING.md       # Эта документация
```

## 🔍 API Reference

### Основные функции

#### `track_openai_cost(response_data, context)`
Отслеживает затраты для OpenAI запроса.

**Параметры:**
- `response_data`: JSON ответ от OpenAI API
- `context`: Контекст выполнения с dag_id, task_id, user_id

#### `export_cost_data_to_csv(output_file, days)`
Экспортирует данные в CSV файл.

**Параметры:**
- `output_file`: Путь к выходному файлу
- `days`: Количество дней для экспорта

#### `export_and_upload_cost_data(spreadsheet_id, credentials_file)`
Полный процесс экспорта и загрузки в Google Sheets.

**Параметры:**
- `spreadsheet_id`: ID Google Sheets документа
- `credentials_file`: Путь к файлу учетных данных

## 🛠️ Интеграция в пайплайны

Для добавления отслеживания затрат в новый пайплайн:

```python
from cost_tracking import track_openai_cost

# В вашем пайплайне после вызова OpenAI API
response = requests.post(url, json=data, headers=headers)
response_data = response.json()

# Отслеживание затрат
cost_analysis = track_openai_cost(
    response_data=response_data,
    context={
        "dag_id": "your_pipeline_name",
        "task_id": "generate_openai_response",
        "user_id": user_id  # опционально
    }
)

print(f"💰 Стоимость: ${cost_analysis.get('total_cost', 0):.6f}")
print(f"🔢 Токены: {cost_analysis.get('total_tokens', 0)}")
```

## 📈 Мониторинг

### Просмотр данных в Redis

```python
import redis
import json

r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
keys = r.keys("cost_tracking:*")

for key in keys[:5]:  # Показать первые 5 записей
    data = json.loads(r.get(key))
    print(f"Key: {key}")
    print(f"Data: {json.dumps(data, indent=2)}")
```

### Получение статистики

```python
from cost_tracking import get_cost_summary

# Статистика за последние 7 дней
summary = get_cost_summary(days=7)
print(json.dumps(summary, indent=2))
```

## 🔒 Безопасность

- Файлы учетных данных Google API не должны попадать в репозиторий
- Используйте переменные окружения для конфиденциальных данных
- Регулярно ротируйте ключи Service Account
- Ограничьте права доступа к Google Sheets только необходимым пользователям

## 🐛 Troubleshooting

### Проблема: "Redis connection failed"
**Решение:** Убедитесь, что Redis сервер запущен и доступен

### Проблема: "Google Sheets API error"
**Решение:** Проверьте права доступа Service Account к документу

### Проблема: "No data in CSV export"
**Решение:** Убедитесь, что в Redis есть данные за указанный период

### Проблема: "Pipeline not found in mapping"
**Решение:** Добавьте новый пайплайн в `PIPELINE_RUSSIAN_NAMES`

## 📞 Поддержка

При возникновении проблем:
1. Запустите тестовый скрипт: `python src/scripts/test_cost_tracking.py`
2. Проверьте логи Airflow для DAG `cost_tracking_export`
3. Убедитесь, что все зависимости установлены
4. Проверьте настройки Redis и Google Sheets API
