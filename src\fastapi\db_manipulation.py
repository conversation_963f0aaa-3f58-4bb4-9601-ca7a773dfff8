import httpx
client = httpx.AsyncClient(timeout=10.0)
from typing import Optional
from itertools import zip_longest
from enum import Enum

class EntityType(str, Enum):
    general = "general"
    recommendation = "recommendation"
    risk = "risk"

API_BASE_URL = "https://root.biome-dev-api.work/api"

async def update_health_profile(user_id, bearer_token, metrics):
    url = f"{API_BASE_URL}/users/{user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    data = {"healthProfile": metrics}

    resp = await client.put(url, headers=headers, json=data)
    return resp

async def update_goals_profile(user_id, bearer_token, goals):
    url = f"{API_BASE_URL}/users/{user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    data = {"goals": goals}

    resp = await client.put(url, headers=headers, json=data)
    return resp

async def fetch_user_data(bearer_token: str, user_id: int):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    url = f"{API_BASE_URL}/users/{user_id}"
    response = await client.get(url, headers=headers)
    if response.status_code == 200:
        user_data = response.json()
        health_profile = user_data.get("healthProfile", {})
        goals = user_data.get("goals", {})
        timezone = user_data.get("timezone", "")
        return {
            "healthProfile": health_profile,
            "goals": goals,
            "timezone": timezone
        }
    else:
        raise ValueError(f"Failed to fetch user data. Status: {response.status_code}")

async def upload_recommendation(user_id, recommendation_data, bearer_token):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }

    url = f"{API_BASE_URL}/recommendations"
    payload = {
        "data": {
            "title": recommendation_data["title"],
            "shortDescription": recommendation_data["shortDescription"],
            "content": recommendation_data["content"],
            "user": user_id,
        }
    }
    response = await client.post(url, headers=headers, json=payload)
    if response.status_code == 201:
        print(f"Recommendation uploaded successfully: {response.json()}")
    else:
        print(f"Failed to upload recommendation: {response.status_code}, {response.text}")

async def upload_risk(user_id, risk_data, bearer_token):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    url = f"{API_BASE_URL}/risks"
    payload = {
        "data": {
            "title": risk_data["title"],
            "shortDescription": risk_data["shortDescription"],
            "content": risk_data["content"],
            "user": user_id,
            "assessment": risk_data["assessment"],
        }
    }
    response = await client.post(url, headers=headers, json=payload)
    if response.status_code == 201:
        print(f"Risk uploaded successfully: {response.json()}")
    else:
        print(f"Failed to upload risk: {response.status_code}, {response.text}")

async def fetch_all_recommendations(bearer_token: str, user_id: int):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    url = f"{API_BASE_URL}/recommendations"
    params = {
        "filters[user][id][$eq]": user_id,
        "populate": "user"
    }
    response = await client.get(url, headers=headers, params=params)
    if response.status_code == 200:
        data = response.json().get("data", [])
        recommendations = [
            {
                "title": rec.get("title", "No Title"),
                "shortDescription": rec.get("shortDescription", "No ShortDescription"),
                "content": rec.get("content", "No Content")
            } for rec in data
        ]
        return recommendations
    else:
        raise ValueError(f"Failed to fetch recommendations. Status: {response.status_code}")

async def fetch_all_risks(bearer_token: str, user_id: int):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    url = f"{API_BASE_URL}/risks"
    params = {
        "filters[user][id][$eq]": user_id,
        "populate": "user"
    }
    response = await client.get(url, headers=headers, params=params)
    if response.status_code == 200:
        data = response.json().get("data", [])
        risks = [
            {
                "title": risk.get("title", "No Title"),
                "shortDescription": risk.get("shortDescription", "No ShortDescription"),
                "content": risk.get("content", "No Content")
            } for risk in data
        ]
        return risks
    else:
        raise ValueError(f"Failed to fetch risks. Status: {response.status_code}")

async def fetch_recommendation_by_id(bearer_token: str, entity_id: int):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    url = f"{API_BASE_URL}/recommendations"
    params = {
        "filters[id][$eq]": entity_id,
        "populate": "user"
    }
    response = await client.get(url, headers=headers, params=params)
    if response.status_code == 200:
        data = response.json().get("data", [])
        if data:
            recommendation = data[0]
            print(recommendation.get("title", "No Title"))
            print(recommendation.get("content", "No Content"))
            return {
                "title": recommendation.get("title", "No Title"),
                "content": recommendation.get("content", "No Content"),
            }
        else:
            raise ValueError(f"Recommendation with ID {entity_id} not found.")
    else:
        raise ValueError(f"Failed to fetch recommendations. Status: {response.status_code}")

async def fetch_risk_by_id(bearer_token: str, entity_id: int):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    url = f"{API_BASE_URL}/risks"
    params = {
        "filters[id][$eq]": entity_id,
        "populate": "user"
    }
    response = await client.get(url, headers=headers, params=params)
    if response.status_code == 200:
        data = response.json().get("data", [])
        if data:
            risk = data[0]
            print(risk.get("title", "No Title"))
            print(risk.get("content", "No Content"))
            return {
                "title": risk.get("title", "No Title"),
                "content": risk.get("content", "No Content"),
            }
        else:
            raise ValueError(f"Risk with ID {entity_id} not found.")
    else:
        raise ValueError(f"Failed to fetch risks. Status: {response.status_code}")

async def delete_user_recommendations_from_db(bearer_token, user_id):
    url = f"{API_BASE_URL}/recommendations?filters[user][id][$eq]={user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    try:
        response = await client.get(url, headers=headers)
        if response.status_code == 200:
            recommendations = response.json()["data"]
            for recommendation in recommendations:
                rec_id = recommendation["documentId"]
                delete_url = f"{API_BASE_URL}/recommendations/{rec_id}"
                delete_response = await client.delete(delete_url, headers=headers)
                if delete_response.status_code != 204:
                    print(f"Ошибка удаления рекомендации {rec_id}: {delete_response.text}")
                else:
                    print('Удалено')
        else:
            print(f"Failed to get recommendations for user {user_id}: {response.status_code}")
    except Exception as e:
        print(f"Ошибка при удалении рекомендаций: {e}")

async def delete_user_risks_from_db(bearer_token, user_id):
    url = f"{API_BASE_URL}/risks?filters[user][id][$eq]={user_id}"
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    try:
        response = await client.get(url, headers=headers)
        if response.status_code == 200:
            risks = response.json()["data"]
            for risk in risks:
                risk_id = risk["documentId"]
                delete_url = f"{API_BASE_URL}/risks/{risk_id}"
                delete_response = await client.delete(delete_url, headers=headers)
                if delete_response.status_code != 204:
                    print(f"Ошибка удаления риска {risk_id}: {delete_response.text}")
                else:
                    print('Удалено')
        else:
            print(f"Failed to get risks for user {user_id}: {response.status_code}")
    except Exception as e:
        print(f"Ошибка при удалении рисков: {e}")

async def delete_existing_chats_by_document_id(user_id: int, bearer_token: str, entity_type: Optional[str] = None, entity_id: Optional[int] = None):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }

    # Формируем параметры для фильтрации существующих чатов
    params = {
        "filters[users_permissions_user][id][$eq]": user_id
    }
    if entity_type == "risk" and entity_id is not None:
        params["filters[risk][id][$eq]"] = entity_id
    elif entity_type == "recommendation" and entity_id is not None:
        params["filters[recommendation][id][$eq]"] = entity_id
    elif entity_type == "general":
        pass  # Для общего чата дополнительных фильтров не требуется

    try:
        # Получаем существующие чаты
        fetch_response = await client.get(f"{API_BASE_URL}/chats", headers=headers, params=params)
        if fetch_response.status_code == 200:
            existing_chats = fetch_response.json().get("data", [])
            for chat in existing_chats:
                document_id = chat.get("documentId")  # Получаем documentId
                if not document_id:
                    print("Document ID отсутствует в данных чата. Пропускаем.")
                    continue

                delete_url = f"{API_BASE_URL}/chats/{document_id}"
                delete_response = await client.delete(delete_url, headers=headers)
                if delete_response.status_code == 200:  # Успешное удаление
                    print(f"Чат с documentId {document_id} успешно удалён.")
                else:
                    print(f"Ошибка при удалении чата с documentId {document_id}: {delete_response.status_code}, {delete_response.text}")
        else:
            print(f"Не удалось получить существующие чаты: {fetch_response.status_code}, {fetch_response.text}")
    except Exception as e:
        print(f"Ошибка при удалении существующих чатов: {e}")

async def upload_chat_messages_for_closing(user_id: int, bearer_token: str, r, entity_type: str, entity_id: Optional[int] = None):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }

    # Формируем ключи для Redis в зависимости от типа чата
    if entity_type == "general":
        user_messages_key = f"user:messages-general:{user_id}"
        system_messages_key = f"system:messages-general:{user_id}"
    elif entity_type == "risk":
        user_messages_key = f"user:messages-risk{entity_id}:{user_id}"
        system_messages_key = f"system:messages-risk{entity_id}:{user_id}"
    elif entity_type == "recommendation":
        user_messages_key = f"user:messages-recommendation{entity_id}:{user_id}"
        system_messages_key = f"system:messages-recommendation{entity_id}:{user_id}"
    else:
        print("Unknown entity_type")
        return

    # Получаем все сообщения из Redis
    user_messages = await r.lrange(user_messages_key, 0, -1)
    system_messages = await r.lrange(system_messages_key, 0, -1)

    # Формируем список сообщений в формате JSON
    messages_content = []
    # Используем zip_longest, чтобы учесть возможное неравное количество сообщений
    for idx, (s_msg, u_msg) in enumerate(zip_longest(system_messages, user_messages)):
        if s_msg:
            messages_content.append({
                "id": idx * 2 + 1,
                "message": s_msg.decode('utf-8') if isinstance(s_msg, bytes) else s_msg,
                "isUserMessage": False,
                "entity_type": entity_type,
                "entity_id": entity_id
            })
        if u_msg:
            messages_content.append({
                "id": idx * 2 + 2,
                "message": u_msg.decode('utf-8') if isinstance(u_msg, bytes) else u_msg,
                "isUserMessage": True,
                "entity_type": entity_type,
                "entity_id": entity_id
            })

    # Формируем основной payload
    payload = {
        "data": {
            "content": messages_content,
            "users_permissions_user": user_id
        }
    }

    # Добавляем entity_id в зависимости от типа
    if entity_type == EntityType.risk and entity_id is not None:
        payload["data"]["risk"] = entity_id
    elif entity_type == EntityType.recommendation and entity_id is not None:
        payload["data"]["recommendation"] = entity_id

    # Отправляем запрос в API
    try:
        resp = await client.post(f"{API_BASE_URL}/chats", headers=headers, json=payload)
        if resp.status_code in [200, 201]:
            print(f"Chat successfully uploaded: {resp.json()}")
        else:
            print(f"Error uploading chat: {resp.status_code}, {resp.text}")
    except Exception as e:
        print(f"Exception occurred while uploading chat: {e}")

async def fetch_all_chats(bearer_token: str, user_id: int):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    url = f"{API_BASE_URL}/chats"
    params = {
        "filters[users_permissions_user][id][$eq]": user_id
    }

    response = await client.get(url, headers=headers, params=params)
    if response.status_code == 200:
        return response.json().get("data", [])
    else:
        raise ValueError(f"Failed to fetch chats. Status: {response.status_code}, Response: {response.text}")

async def get_last_uploaded_image(image_id, bearer_token, user_id=None, user_jwt_token=None):
    params = {}
    if user_jwt_token:
        headers = {
            "Authorization": f"{user_jwt_token}",
            "Content-Type": "application/json"
        }
        url = f"{API_BASE_URL}/secure-files/{image_id}"
    else:
        headers = {
            "Authorization": f"Bearer {bearer_token}",
            "Content-Type": "application/json"
        }
        url = f"{API_BASE_URL}/upload/files?sort[0]=createdAt:desc"
        params = {
            "files?filters[id][$eq]": image_id
        }

    response = await client.get(url, headers=headers, params=params)
    if response.status_code == 200:
        print(response)
        data = response.json()
        if user_jwt_token:
            data_info = data.get("data", {})
            print(data_info)
            print(data_info.get("id", "No id"))
            print(data_info.get("name", "No name"))
            file_info = data.get("data", {}).get("file", {})
            print(file_info.get("id", "No id"))
            print(file_info.get("name", "No name"))
            print(file_info.get("ext", "No ext"))
            return {
                "id": data_info.get("id", "No id"),
                "name": file_info.get("name", "No name"),
                "ext": file_info.get("ext", "No ext"),
            }
        else:
            if data:
                image = data[0]
                print(image.get("url", "No url"))
                print(image.get("ext", "No ext"))
                print(image.get("name", "No name"))
                return {
                    "url": image.get("url", "No url"),
                    "ext": image.get("ext", "No ext"),
                    "name": image.get("name", "No name"),
                }
            else:
                raise ValueError("Image not found.")
    else:
        raise ValueError(f"Failed to fetch file. Status: {response.status_code}, Body: {response.text}")

async def upload_food(user_id, food_data, bearer_token):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    url = f"{API_BASE_URL}/foods"
    payload = {
        "data": {
            "name": food_data["name"],
            "description": food_data["description"],
            "calories": food_data["calories"],
            "proteins": food_data["proteins"],
            "fats": food_data["fats"],
            "carbohydrates": food_data["carbohydrates"],
            "addedAt": food_data["addedAt"],
            "user": user_id,
        }
    }
    response = await client.post(url, headers=headers, json=payload)
    if response.status_code == 201:
        print(f"Food uploaded successfully: {response.json()}")
    else:
        print(f"Failed to upload food: {response.status_code}, {response.text}")

async def upload_challenge(user_id, challenge_data, bearer_token):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    url = f"{API_BASE_URL}/challenges"

    # Определяем hasTimer: если пользователь не передал, то True только для timedAction
    mechanic = challenge_data.get("mechanic")
    has_timer = challenge_data.get("hasTimer")
    if has_timer is None:
        has_timer = True if mechanic == "timedAction" else False

    # Базовые обязательные поля
    payload_data = {
        "title": challenge_data["title"],
        "shortTitle": challenge_data["shortTitle"],
        "description": challenge_data["description"],
        "importanceDescription": challenge_data["importanceDescription"],
        "category": challenge_data["category"],
        "condition": challenge_data["condition"],
        "mechanic": mechanic,
        "hasTimer": has_timer,
        "user": user_id,
    }

    # Опциональные поля по механике
    # Для dailyChecklist
    if challenge_data.get("mechanic") == "dailyChecklist":
        if "durationDays" in challenge_data:
            payload_data["durationDays"] = challenge_data["durationDays"]
        if "dayDescriptions" in challenge_data:
            payload_data["dayDescriptions"] = challenge_data["dayDescriptions"]

    # Для timedAction
    if challenge_data.get("mechanic") == "timedAction":
        if "initialDurationSecs" in challenge_data:
            payload_data["initialDurationSecs"] = challenge_data["initialDurationSecs"]

    # Для goalProgress
    if challenge_data.get("mechanic") == "goalProgress":
        if "goal" in challenge_data:
            # ожидаем только шаги, без km
            payload_data["goal"] = challenge_data["goal"]
        if "cutoffTime" in challenge_data:
            payload_data["cutoffTime"] = challenge_data["cutoffTime"]

    # Для countdownToTime
    if challenge_data.get("mechanic") == "countdownToTime":
        if "cutoffTime" in challenge_data:
            payload_data["cutoffTime"] = challenge_data["cutoffTime"]

    payload = {"data": payload_data}

    response = await client.post(url, headers=headers, json=payload)
    if response.status_code == 201:
        print(f"Challenge uploaded successfully: {response.json()}")
    else:
        print(f"Failed to upload challenge: {response.status_code}, {response.text}")


async def get_user_challenges(user_id: int, bearer_token: str):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    url = f"{API_BASE_URL}/challenges"
    params = {
        "filters[user][id][$eq]": user_id,
        "populate": "user"
    }
    response = await client.get(url, headers=headers, params=params)
    if response.status_code == 200:
        data = response.json().get("data", [])
        challenges = [
            {
                "title": challenge.get("title", "No Title")
            } for challenge in data
        ]
        return challenges
    else:
        raise ValueError(f"Failed to fetch challenges. Status: {response.status_code}")


async def get_user_challenges_mechanics(user_id: int, bearer_token: str):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    url = f"{API_BASE_URL}/challenges"
    params = {
        "filters[user][id][$eq]": user_id,
        "populate": "user"
    }
    response = await client.get(url, headers=headers, params=params)
    if response.status_code == 200:
        data = response.json().get("data", [])
        challenges = [
            {
                "mechanic": challenge.get("mechanic", "No Mechanic")
            } for challenge in data
        ]
        return challenges
    else:
        raise ValueError(f"Failed to fetch challenges. Status: {response.status_code}")


async def get_user_foods(user_id: int, bearer_token: str):
    """
    Fetch foods for a user and return a list of dicts with actual fields.
    """
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
    }
    url = f"{API_BASE_URL}/foods"
    params = {
        "filters[user][id][$eq]": user_id,
        "populate": "user",
        "pagination[page]": 1,
        "pagination[pageSize]": 1000,
        "sort": "createdAt:desc",
    }
    response = await client.get(url, headers=headers, params=params)
    if response.status_code != 200:
        raise ValueError(f"Failed to fetch foods. Status: {response.status_code}")

    result = response.json()
    items = result.get("data", [])

    foods = []
    for entry in items:
        record = entry.get("attributes", entry)
        foods.append({
            "name": record.get("name", ""),
            "description": record.get("description", ""),
            "calories": record.get("calories", 0),
            "proteins": record.get("proteins", 0),
            "fats": record.get("fats", 0),
            "carbohydrates": record.get("carbohydrates", 0),
            "addedAt": record.get("addedAt") or record.get("createdAt") or ""
        })
    return foods

async def upload_trend(user_id, trend_data, bearer_token):
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json"
    }
    url = f"{API_BASE_URL}/trends"
    payload = {
        "data": {
            "content": trend_data["content"],
            "type": trend_data["type"],
            "addedAt": trend_data["addedAt"],
            "user": user_id,
        }
    }
    response = await client.post(url, headers=headers, json=payload)
    if response.status_code == 201:
        print(f"Trend uploaded successfully: {response.json()}")
    else:
        print(f"Failed to upload trend: {response.status_code}, {response.text}")