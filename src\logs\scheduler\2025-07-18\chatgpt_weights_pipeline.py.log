[2025-07-18T11:26:00.894+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:00.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:26:00.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.897+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:00.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.977+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:00.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:01.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.206+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_weights_pipeline
[2025-07-18T11:26:01.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.217+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_weights_pipeline
[2025-07-18T11:26:01.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.226+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_weights_pipeline
[2025-07-18T11:26:01.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.236+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_weights_pipeline
[2025-07-18T11:26:01.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.244+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_weights_pipeline
[2025-07-18T11:26:01.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.254+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_weights_pipeline
[2025-07-18T11:26:01.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.261+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_weights_pipeline
[2025-07-18T11:26:01.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:01.274+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:01.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.274+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_weights_pipeline
[2025-07-18T11:26:01.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.275+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:26:01.295+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.407 seconds
[2025-07-18T11:26:31.956+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:31.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:26:31.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.959+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:32.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.186+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:32.194+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:32.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.287+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:32.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.300+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:26:32.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.374 seconds
[2025-07-18T11:27:02.773+0000] {processor.py:186} INFO - Started process (PID=560) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:02.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:27:02.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.777+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:02.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.852+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.860+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:02.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:27:02.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.228 seconds
[2025-07-18T11:27:33.443+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:33.444+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:27:33.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:33.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.520+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:33.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:33.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.641+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.654+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:27:33.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.237 seconds
[2025-07-18T11:28:03.932+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:03.933+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:28:03.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.935+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:04.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.016+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:04.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:04.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.137+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:04.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.150+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:28:04.171+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.245 seconds
[2025-07-18T11:28:34.292+0000] {processor.py:186} INFO - Started process (PID=951) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:34.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:28:34.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:34.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.374+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:34.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.482+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.496+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:28:34.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.236 seconds
[2025-07-18T11:29:05.236+0000] {processor.py:186} INFO - Started process (PID=1082) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:05.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:29:05.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:05.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.313+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:05.320+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:05.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:05.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:29:05.446+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.217 seconds
[2025-07-18T11:29:36.127+0000] {processor.py:186} INFO - Started process (PID=1213) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:36.128+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:29:36.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:36.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.209+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:36.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:36.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:36.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.337+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:29:36.358+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.238 seconds
[2025-07-18T11:30:06.821+0000] {processor.py:186} INFO - Started process (PID=1346) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:06.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:30:06.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.825+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:06.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.895+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:06.903+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:07.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.007+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:07.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.018+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:30:07.037+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.222 seconds
[2025-07-18T11:30:37.647+0000] {processor.py:186} INFO - Started process (PID=1475) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:37.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:30:37.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:37.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.735+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.746+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:37.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.854+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.865+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:30:37.887+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.245 seconds
[2025-07-18T11:31:08.313+0000] {processor.py:186} INFO - Started process (PID=1606) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:08.314+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:31:08.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.316+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:08.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.387+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:08.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:08.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.496+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:08.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:31:08.526+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.219 seconds
