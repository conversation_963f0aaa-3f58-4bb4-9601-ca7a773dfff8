[2025-07-18T11:26:00.894+0000] {processor.py:186} INFO - Started process (PID=296) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:00.895+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:26:00.897+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.897+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:00.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.977+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:00.983+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:01.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.206+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_weights_pipeline
[2025-07-18T11:26:01.217+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.217+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_weights_pipeline
[2025-07-18T11:26:01.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.226+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_weights_pipeline
[2025-07-18T11:26:01.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.236+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_weights_pipeline
[2025-07-18T11:26:01.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.244+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_weights_pipeline
[2025-07-18T11:26:01.254+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.254+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_weights_pipeline
[2025-07-18T11:26:01.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.261+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_weights_pipeline
[2025-07-18T11:26:01.262+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.262+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:01.274+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:01.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.274+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_weights_pipeline
[2025-07-18T11:26:01.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:01.275+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:26:01.295+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.407 seconds
[2025-07-18T11:26:31.956+0000] {processor.py:186} INFO - Started process (PID=429) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:31.957+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:26:31.959+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:31.959+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:32.187+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.186+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:32.194+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:26:32.287+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.287+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:32.300+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:32.300+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:26:32.323+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.374 seconds
[2025-07-18T11:27:02.773+0000] {processor.py:186} INFO - Started process (PID=560) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:02.774+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:27:02.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.777+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:02.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.852+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.860+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:02.965+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.965+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.976+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:27:02.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.228 seconds
[2025-07-18T11:27:33.443+0000] {processor.py:186} INFO - Started process (PID=689) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:33.444+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:27:33.447+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.446+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:33.520+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.520+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:33.530+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:27:33.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.641+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:33.654+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:33.654+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:27:33.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.237 seconds
[2025-07-18T11:28:03.932+0000] {processor.py:186} INFO - Started process (PID=820) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:03.933+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:28:03.935+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.935+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:04.017+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.016+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:04.025+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:04.137+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.137+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:04.150+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:04.150+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:28:04.171+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.245 seconds
[2025-07-18T11:28:34.292+0000] {processor.py:186} INFO - Started process (PID=951) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:34.293+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:28:34.295+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.294+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:34.374+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.374+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:34.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:28:34.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.482+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:34.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:34.496+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:28:34.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.236 seconds
[2025-07-18T11:29:05.236+0000] {processor.py:186} INFO - Started process (PID=1082) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:05.237+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:29:05.239+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.239+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:05.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.313+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:05.320+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:05.416+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.415+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:05.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:05.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:29:05.446+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.217 seconds
[2025-07-18T11:29:36.127+0000] {processor.py:186} INFO - Started process (PID=1213) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:36.128+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:29:36.131+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.131+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:36.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.209+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:36.217+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:29:36.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.325+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:36.337+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:36.337+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:29:36.358+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.238 seconds
[2025-07-18T11:30:06.821+0000] {processor.py:186} INFO - Started process (PID=1346) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:06.823+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:30:06.826+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.825+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:06.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.895+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:06.903+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:07.007+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.007+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:07.018+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:07.018+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:30:07.037+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.222 seconds
[2025-07-18T11:30:37.647+0000] {processor.py:186} INFO - Started process (PID=1475) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:37.648+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:30:37.651+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.651+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:37.736+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.735+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.746+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:30:37.854+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.854+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.865+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:30:37.887+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.245 seconds
[2025-07-18T11:31:08.313+0000] {processor.py:186} INFO - Started process (PID=1606) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:08.314+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:31:08.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.316+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:08.387+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.387+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:08.395+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:08.497+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.496+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:08.508+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:08.508+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:31:08.526+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.219 seconds
[2025-07-18T11:31:38.988+0000] {processor.py:186} INFO - Started process (PID=1739) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:38.989+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:31:38.993+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.992+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:39.076+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:39.076+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:39.084+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:31:39.186+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:39.185+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:39.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:39.197+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:31:39.219+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.237 seconds
[2025-07-18T11:32:09.531+0000] {processor.py:186} INFO - Started process (PID=1870) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:32:09.532+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:32:09.536+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.535+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:32:09.614+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.614+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:09.623+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:32:09.731+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.731+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:09.742+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:09.742+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:32:09.762+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.237 seconds
[2025-07-18T11:32:39.863+0000] {processor.py:186} INFO - Started process (PID=1999) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:32:39.864+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:32:39.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.866+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:32:39.951+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.950+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:39.959+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:32:40.060+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:40.060+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:40.071+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:40.071+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:32:40.092+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.235 seconds
[2025-07-18T11:33:10.632+0000] {processor.py:186} INFO - Started process (PID=2130) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:33:10.634+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:33:10.636+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:33:10.712+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.711+0000] {cost_tracking.py:76} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:10.718+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:33:10.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.821+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:10.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:10.832+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:33:10.852+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.226 seconds
[2025-07-18T11:33:42.841+0000] {processor.py:186} INFO - Started process (PID=2282) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:33:42.842+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:33:42.845+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.844+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:33:43.227+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:43.227+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:43.235+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:33:43.342+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:43.342+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:43.352+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:43.351+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:33:43.372+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.538 seconds
[2025-07-18T11:34:14.190+0000] {processor.py:186} INFO - Started process (PID=2435) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:34:14.191+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:34:14.195+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.194+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:34:14.565+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.565+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:14.573+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:34:14.674+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.674+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:14.685+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:14.684+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:34:14.705+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.522 seconds
[2025-07-18T11:34:45.469+0000] {processor.py:186} INFO - Started process (PID=2588) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:34:45.470+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:34:45.473+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.472+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:34:45.833+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.833+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:45.843+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:34:45.971+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.971+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:45.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:45.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:34:46.008+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.545 seconds
[2025-07-18T11:35:17.285+0000] {processor.py:186} INFO - Started process (PID=2741) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:35:17.287+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:35:17.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.289+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:35:17.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.706+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:17.714+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:35:17.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.820+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:17.832+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:17.832+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:35:17.854+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.575 seconds
[2025-07-18T11:35:48.548+0000] {processor.py:186} INFO - Started process (PID=2894) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:35:48.550+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:35:48.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.552+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:35:48.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:48.965+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:48.972+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:35:49.079+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:49.079+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:49.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:49.091+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:35:49.112+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.570 seconds
[2025-07-18T11:36:19.717+0000] {processor.py:186} INFO - Started process (PID=3047) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:36:19.719+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:36:19.722+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:19.721+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:36:20.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:20.162+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:20.172+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:36:20.323+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:20.322+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:20.336+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:20.336+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:36:20.363+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.653 seconds
[2025-07-18T11:36:51.636+0000] {processor.py:186} INFO - Started process (PID=3200) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:36:51.637+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:36:51.641+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:51.640+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:36:52.047+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:52.047+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:52.054+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:36:52.183+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:52.183+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:52.199+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:52.199+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:36:52.414+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.787 seconds
[2025-07-18T11:37:22.803+0000] {processor.py:186} INFO - Started process (PID=3353) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:37:22.804+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:37:22.808+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:22.807+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:37:23.192+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:23.191+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:23.200+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:37:23.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:23.312+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:23.483+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:23.483+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:37:23.503+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.707 seconds
[2025-07-18T11:37:54.759+0000] {processor.py:186} INFO - Started process (PID=3507) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:37:54.760+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:37:54.764+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:54.763+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:37:55.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:55.161+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:55.171+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:37:55.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:55.412+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:55.426+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:55.426+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:37:55.447+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.696 seconds
[2025-07-18T11:38:26.252+0000] {processor.py:186} INFO - Started process (PID=3667) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:38:26.253+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:38:26.256+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.256+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:38:26.605+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.604+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:26.615+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:38:26.852+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.851+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:26.860+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:26.860+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:38:26.880+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.635 seconds
[2025-07-18T11:38:57.102+0000] {processor.py:186} INFO - Started process (PID=3820) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:38:57.104+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:38:57.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:57.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:38:57.635+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:57.635+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:57.642+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:38:57.738+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:57.738+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:57.747+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:57.747+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:38:57.768+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.672 seconds
[2025-07-18T11:39:28.234+0000] {processor.py:186} INFO - Started process (PID=3979) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:39:28.234+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:39:28.237+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.237+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:39:28.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.793+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:28.801+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:39:28.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.906+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:28.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:28.917+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:39:28.938+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.710 seconds
[2025-07-18T11:39:59.220+0000] {processor.py:186} INFO - Started process (PID=4132) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:39:59.221+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:39:59.224+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.223+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:39:59.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.714+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:59.721+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:39:59.821+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.820+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:59.830+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:59.830+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:39:59.850+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.637 seconds
[2025-07-18T11:40:30.418+0000] {processor.py:186} INFO - Started process (PID=4285) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:40:30.419+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:40:30.422+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:30.422+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:40:30.987+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:30.986+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:30.995+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:40:31.110+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:31.110+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:31.123+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:31.123+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:40:31.145+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.736 seconds
[2025-07-18T11:41:01.595+0000] {processor.py:186} INFO - Started process (PID=4444) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:41:01.596+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:41:01.599+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:01.599+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:41:02.116+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:02.116+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:02.123+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:41:02.220+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:02.220+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:02.232+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:02.232+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:41:02.255+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.665 seconds
[2025-07-18T11:41:32.470+0000] {processor.py:186} INFO - Started process (PID=4603) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:41:32.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:41:32.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:32.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:41:33.013+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:33.013+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:33.020+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:41:33.122+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:33.121+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:33.133+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:33.132+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:41:33.150+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.686 seconds
[2025-07-18T11:42:03.671+0000] {processor.py:186} INFO - Started process (PID=4762) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:42:03.672+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:42:03.675+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:03.675+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:42:04.170+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:04.170+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:04.177+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:42:04.289+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:04.288+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:04.299+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:04.298+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:42:04.319+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.654 seconds
[2025-07-18T11:42:34.423+0000] {processor.py:186} INFO - Started process (PID=4921) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:42:34.424+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:42:34.427+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:34.427+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:42:34.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:34.979+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:34.986+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:42:35.092+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:35.091+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:35.103+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:35.103+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:42:35.126+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.709 seconds
[2025-07-18T11:43:05.269+0000] {processor.py:186} INFO - Started process (PID=5080) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:43:05.270+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:43:05.272+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:05.272+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:43:05.777+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:05.777+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:05.785+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:43:05.895+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:05.894+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:05.908+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:05.908+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:43:05.931+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.668 seconds
[2025-07-18T11:43:36.477+0000] {processor.py:186} INFO - Started process (PID=5239) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:43:36.478+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:43:36.482+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:36.481+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:43:37.045+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:37.045+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:37.052+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:43:37.146+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:37.146+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:37.158+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:37.157+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:43:37.177+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.705 seconds
[2025-07-18T11:44:07.271+0000] {processor.py:186} INFO - Started process (PID=5398) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:44:07.272+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:44:07.276+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:07.275+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:44:07.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:07.809+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:07.818+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:44:07.918+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:07.918+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:07.933+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:07.933+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:44:07.953+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.688 seconds
[2025-07-18T11:44:38.414+0000] {processor.py:186} INFO - Started process (PID=5557) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:44:38.415+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:44:38.417+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:38.416+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:44:38.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:38.962+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:38.970+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:44:39.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:39.070+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:39.082+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:39.082+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:44:39.103+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.696 seconds
[2025-07-18T11:45:09.757+0000] {processor.py:186} INFO - Started process (PID=5716) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:45:09.759+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:45:09.763+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:09.762+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:45:10.271+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:10.270+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:10.277+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:45:10.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:10.378+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:10.389+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:10.389+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:45:10.412+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.663 seconds
[2025-07-18T11:45:40.679+0000] {processor.py:186} INFO - Started process (PID=5875) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:45:40.681+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:45:40.684+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:40.684+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:45:41.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:41.194+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:41.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:45:41.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:41.320+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:41.330+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:41.330+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:45:41.347+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.676 seconds
[2025-07-18T11:46:11.938+0000] {processor.py:186} INFO - Started process (PID=6034) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:46:11.939+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:46:11.943+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:11.942+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:46:12.521+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:12.521+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:12.527+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:46:12.644+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:12.643+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:12.657+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:12.657+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:46:12.675+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.745 seconds
[2025-07-18T11:46:42.789+0000] {processor.py:186} INFO - Started process (PID=6193) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:46:42.790+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:46:42.793+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:42.793+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:46:43.361+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:43.361+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:43.369+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:46:43.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:43.469+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:43.481+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:43.481+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:46:43.504+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.721 seconds
[2025-07-18T11:47:13.674+0000] {processor.py:186} INFO - Started process (PID=6352) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:47:13.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:47:13.679+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:13.679+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:47:14.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:14.282+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:14.287+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:47:14.404+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:14.404+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:14.415+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:14.415+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:47:14.435+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.768 seconds
[2025-07-18T11:47:44.558+0000] {processor.py:186} INFO - Started process (PID=6511) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:47:44.560+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:47:44.562+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:44.562+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:47:45.112+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:45.112+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:47:45.119+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:47:45.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:45.215+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:47:45.226+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:47:45.225+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:47:45.250+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.699 seconds
[2025-07-18T11:48:15.425+0000] {processor.py:186} INFO - Started process (PID=6668) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:48:15.426+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:48:15.431+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:15.430+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:48:15.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:15.977+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:15.985+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:48:16.081+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:16.081+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:16.091+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:16.091+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:48:16.113+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.695 seconds
[2025-07-18T11:48:46.761+0000] {processor.py:186} INFO - Started process (PID=6827) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:48:46.762+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:48:46.765+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:46.765+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:48:47.278+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:47.277+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:48:47.285+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:48:47.383+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:47.383+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:48:47.395+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:48:47.395+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:48:47.415+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.661 seconds
[2025-07-18T11:49:17.784+0000] {processor.py:186} INFO - Started process (PID=6986) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:49:17.785+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:49:17.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:17.788+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:49:18.377+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:18.376+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:18.382+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:49:18.485+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:18.484+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:18.496+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:18.496+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:49:18.516+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.738 seconds
[2025-07-18T11:49:48.846+0000] {processor.py:186} INFO - Started process (PID=7145) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:49:48.847+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:49:48.850+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:48.850+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:49:49.365+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:49.365+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:49:49.371+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:49:49.475+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:49.475+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:49:49.487+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:49:49.487+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:49:49.509+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.669 seconds
[2025-07-18T11:50:19.998+0000] {processor.py:186} INFO - Started process (PID=7306) to work on /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:50:19.999+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_weights_pipeline.py for tasks to queue
[2025-07-18T11:50:20.002+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:20.001+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:50:20.507+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:20.507+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:50:20.513+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_weights_pipeline' retrieved from /opt/airflow/dags/chatgpt_weights_pipeline.py
[2025-07-18T11:50:20.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:20.617+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:50:20.629+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:50:20.628+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_weights_pipeline to None, run_after=None
[2025-07-18T11:50:20.649+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_weights_pipeline.py took 0.658 seconds
