#!/usr/bin/env python3
"""
Быстрый экспорт данных cost_tracking в Google Sheets
Использует предустановленные настройки для вашей таблицы
"""

import sys
from pathlib import Path

# Добавляем путь к модулям проекта
sys.path.append(str(Path(__file__).parent.parent / "dags"))

def quick_export(days=30):
    """
    Быстрый экспорт данных за указанное количество дней
    
    Args:
        days: Количество дней для экспорта (по умолчанию 30)
    """
    print(f"🚀 Быстрый экспорт данных cost_tracking за {days} дней")
    print("📊 Таблица: https://docs.google.com/spreadsheets/d/1u39n_ERZzDQRpq1MWdgkKH9g8eQm15yE-eVX3jligvk/edit")
    print("📋 Лист: BiomCost")
    print("-" * 60)
    
    try:
        from cost_tracking import export_and_upload_cost_data
        
        # Экспорт с предустановленными настройками
        success = export_and_upload_cost_data(days=days)
        
        if success:
            print("✅ Экспорт завершен успешно!")
            print("🎯 Данные обновлены в Google Sheets")
            print("🔗 Откройте таблицу: https://docs.google.com/spreadsheets/d/1u39n_ERZzDQRpq1MWdgkKH9g8eQm15yE-eVX3jligvk/edit")
            return True
        else:
            print("❌ Ошибка при экспорте данных")
            return False
            
    except Exception as e:
        print(f"❌ Ошибка: {e}")
        return False

def main():
    """Основная функция"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Быстрый экспорт cost_tracking в Google Sheets")
    parser.add_argument("--days", type=int, default=30, help="Количество дней для экспорта (по умолчанию: 30)")
    
    args = parser.parse_args()
    
    success = quick_export(args.days)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
