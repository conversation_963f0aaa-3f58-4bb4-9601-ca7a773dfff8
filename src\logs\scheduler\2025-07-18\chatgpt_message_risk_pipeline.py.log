[2025-07-18T11:25:59.785+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:25:59.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:25:59.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:25:59.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.944+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:59.956+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:26:00.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.203+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.215+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.224+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.236+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.246+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.258+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.267+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.268+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:00.280+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:00.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.281+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.282+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:26:00.301+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.525 seconds
[2025-07-18T11:26:30.632+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:26:30.634+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:26:30.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:26:30.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.864+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:26:30.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.966+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:26:30.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.369 seconds
[2025-07-18T11:27:02.103+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:02.104+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:27:02.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:02.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.203+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.210+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:02.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.313+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:27:02.349+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.252 seconds
[2025-07-18T11:27:32.794+0000] {processor.py:186} INFO - Started process (PID=666) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:32.795+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:27:32.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.797+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:32.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.877+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.885+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:32.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.982+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:27:33.015+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.228 seconds
[2025-07-18T11:28:03.319+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:03.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:28:03.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.322+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:03.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.392+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:03.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:03.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.500+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:03.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.511+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:28:03.532+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.218 seconds
[2025-07-18T11:28:33.674+0000] {processor.py:186} INFO - Started process (PID=928) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:33.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:28:33.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:33.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.749+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:33.757+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:33.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.853+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.863+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:28:33.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.215 seconds
[2025-07-18T11:29:04.701+0000] {processor.py:186} INFO - Started process (PID=1059) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:04.702+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:29:04.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.704+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:04.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.779+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:04.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:04.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.893+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:04.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.904+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:29:04.924+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.231 seconds
[2025-07-18T11:29:35.470+0000] {processor.py:186} INFO - Started process (PID=1190) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:35.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:29:35.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:35.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.556+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.565+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:35.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:35.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:29:35.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.240 seconds
[2025-07-18T11:30:05.891+0000] {processor.py:186} INFO - Started process (PID=1321) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:05.892+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:30:05.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:05.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.973+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.981+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:06.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:06.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.097+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:30:06.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.234 seconds
[2025-07-18T11:30:37.008+0000] {processor.py:186} INFO - Started process (PID=1452) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:37.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:30:37.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:37.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.086+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:37.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.209+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:30:37.228+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.227 seconds
[2025-07-18T11:31:07.668+0000] {processor.py:186} INFO - Started process (PID=1583) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:07.669+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:31:07.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:07.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.751+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.768+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:07.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:31:07.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.250 seconds
[2025-07-18T11:31:38.115+0000] {processor.py:186} INFO - Started process (PID=1712) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:38.116+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:31:38.119+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.118+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:38.194+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.194+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:38.202+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:38.305+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.305+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:38.316+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:38.316+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:31:38.335+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.227 seconds
[2025-07-18T11:32:08.600+0000] {processor.py:186} INFO - Started process (PID=1843) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:32:08.601+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:32:08.603+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.603+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:32:08.683+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.683+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:08.692+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:32:08.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.797+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:08.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:08.809+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:32:08.832+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.239 seconds
[2025-07-18T11:32:39.225+0000] {processor.py:186} INFO - Started process (PID=1974) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:32:39.226+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:32:39.230+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.229+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:32:39.303+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.302+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:32:39.310+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:32:39.413+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.413+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:32:39.424+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:32:39.424+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:32:39.444+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.225 seconds
[2025-07-18T11:33:09.787+0000] {processor.py:186} INFO - Started process (PID=2105) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:33:09.788+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:33:09.790+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:33:09.865+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.865+0000] {cost_tracking.py:76} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:09.873+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:33:09.979+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.979+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:09.990+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:09.990+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:33:10.012+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.232 seconds
[2025-07-18T11:33:41.572+0000] {processor.py:186} INFO - Started process (PID=2253) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:33:41.573+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:33:41.577+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.576+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:33:41.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:41.962+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:33:41.970+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:33:42.093+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.093+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:33:42.107+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:33:42.106+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:33:42.127+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.561 seconds
[2025-07-18T11:34:12.903+0000] {processor.py:186} INFO - Started process (PID=2406) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:34:12.904+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:34:12.907+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:12.907+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:34:13.321+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.321+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:13.330+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:34:13.440+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.439+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:13.452+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:13.452+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:34:13.472+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.575 seconds
[2025-07-18T11:34:44.259+0000] {processor.py:186} INFO - Started process (PID=2559) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:34:44.260+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:34:44.263+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.263+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:34:44.634+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.634+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:34:44.643+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:34:44.746+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.745+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:34:44.755+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:34:44.755+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:34:44.776+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.523 seconds
[2025-07-18T11:35:15.940+0000] {processor.py:186} INFO - Started process (PID=2712) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:35:15.942+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:35:15.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:15.944+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:35:16.378+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.378+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:16.385+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:35:16.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.489+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:16.502+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:16.501+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:35:16.523+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.590 seconds
[2025-07-18T11:35:47.183+0000] {processor.py:186} INFO - Started process (PID=2865) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:35:47.185+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:35:47.188+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.188+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:35:47.621+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.621+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:35:47.631+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:35:47.753+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.753+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:35:47.767+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:35:47.766+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:35:47.795+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.619 seconds
[2025-07-18T11:36:18.386+0000] {processor.py:186} INFO - Started process (PID=3018) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:36:18.388+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:36:18.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.391+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:36:18.823+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.822+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:18.834+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:36:18.962+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.962+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:18.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:18.975+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:36:18.997+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.620 seconds
[2025-07-18T11:36:50.065+0000] {processor.py:186} INFO - Started process (PID=3171) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:36:50.066+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:36:50.070+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.069+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:36:50.537+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.537+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:36:50.547+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:36:50.714+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.713+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:36:50.727+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:36:50.727+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:36:50.752+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.694 seconds
[2025-07-18T11:37:21.331+0000] {processor.py:186} INFO - Started process (PID=3332) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:37:21.331+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:37:21.334+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.334+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:37:21.687+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.686+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:21.696+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:37:21.912+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.912+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:21.921+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:21.920+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:37:21.941+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.617 seconds
[2025-07-18T11:37:52.586+0000] {processor.py:186} INFO - Started process (PID=3483) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:37:52.587+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:37:52.590+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.590+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:37:52.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:52.983+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:37:52.994+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:37:53.233+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.232+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:37:53.244+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:37:53.243+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:37:53.261+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.684 seconds
[2025-07-18T11:38:23.377+0000] {processor.py:186} INFO - Started process (PID=3636) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:38:23.378+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:38:23.381+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.381+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:38:23.878+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.877+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:23.885+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:38:23.975+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.975+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:23.985+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:23.985+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:38:24.011+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.640 seconds
[2025-07-18T11:38:54.908+0000] {processor.py:186} INFO - Started process (PID=3784) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:38:54.910+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:38:54.914+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:54.914+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:38:55.391+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:55.391+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:38:55.399+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:38:55.489+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:55.488+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:38:55.499+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:38:55.498+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:38:55.518+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.616 seconds
[2025-07-18T11:39:26.154+0000] {processor.py:186} INFO - Started process (PID=3942) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:39:26.156+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:39:26.162+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.162+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:39:26.661+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.661+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:26.669+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:39:26.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.762+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:26.773+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:26.773+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:39:26.789+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.648 seconds
[2025-07-18T11:39:57.052+0000] {processor.py:186} INFO - Started process (PID=4097) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:39:57.053+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:39:57.056+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:57.055+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:39:57.509+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:57.509+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:39:57.516+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:39:57.608+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:57.608+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:39:57.618+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:39:57.617+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:39:57.637+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.591 seconds
[2025-07-18T11:40:28.064+0000] {processor.py:186} INFO - Started process (PID=4254) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:40:28.065+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:40:28.068+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:28.068+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:40:28.552+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:28.552+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:28.560+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:40:28.658+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:28.658+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:28.668+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:28.668+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:40:28.687+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.631 seconds
[2025-07-18T11:40:59.049+0000] {processor.py:186} INFO - Started process (PID=4415) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:40:59.051+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:40:59.054+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:59.054+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:40:59.540+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:59.540+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:40:59.548+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:40:59.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:59.637+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:40:59.646+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:40:59.645+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:40:59.664+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.622 seconds
[2025-07-18T11:41:29.905+0000] {processor.py:186} INFO - Started process (PID=4572) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:41:29.907+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:41:29.909+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:29.909+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:41:30.385+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:30.385+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:41:30.393+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:41:30.490+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:30.490+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:41:30.503+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:41:30.502+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:41:30.522+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.624 seconds
[2025-07-18T11:42:00.943+0000] {processor.py:186} INFO - Started process (PID=4730) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:42:00.944+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:42:00.947+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:00.946+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:42:01.433+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:01.433+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:01.440+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:42:01.534+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:01.533+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:01.544+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:01.543+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:42:01.562+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.626 seconds
[2025-07-18T11:42:31.704+0000] {processor.py:186} INFO - Started process (PID=4889) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:42:31.705+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:42:31.707+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:31.707+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:42:32.206+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.206+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:42:32.214+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:42:32.309+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.309+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:42:32.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:42:32.318+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:42:32.337+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.639 seconds
[2025-07-18T11:43:02.721+0000] {processor.py:186} INFO - Started process (PID=5048) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:43:02.722+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:43:02.725+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:02.724+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:43:03.208+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.207+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:03.215+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:43:03.311+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.310+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:03.319+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:03.319+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:43:03.338+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.623 seconds
[2025-07-18T11:43:33.396+0000] {processor.py:186} INFO - Started process (PID=5204) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:43:33.397+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:43:33.400+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:33.400+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:43:33.881+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:33.880+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:43:33.886+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:43:33.981+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:33.980+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:43:33.992+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:43:33.992+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:43:34.012+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.622 seconds
[2025-07-18T11:44:04.324+0000] {processor.py:186} INFO - Started process (PID=5361) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:44:04.325+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:44:04.327+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.327+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:44:04.861+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.861+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:04.868+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:44:04.974+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.973+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:04.984+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:04.984+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:44:05.004+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.687 seconds
[2025-07-18T11:44:35.812+0000] {processor.py:186} INFO - Started process (PID=5525) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:44:35.813+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:44:35.816+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:35.815+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:44:36.360+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:36.360+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:44:36.368+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:44:36.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:36.474+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:44:36.484+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:44:36.484+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:44:36.502+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.696 seconds
[2025-07-18T11:45:06.849+0000] {processor.py:186} INFO - Started process (PID=5684) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:45:06.850+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:45:06.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:06.853+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:45:07.356+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:07.356+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:07.364+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:45:07.458+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:07.458+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:07.469+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:07.469+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:45:07.489+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.647 seconds
[2025-07-18T11:45:38.270+0000] {processor.py:186} INFO - Started process (PID=5844) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:45:38.271+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:45:38.275+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.274+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:45:38.762+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.762+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:45:38.768+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:45:38.858+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.857+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:45:38.867+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:45:38.867+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:45:38.887+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.624 seconds
[2025-07-18T11:46:09.249+0000] {processor.py:186} INFO - Started process (PID=6008) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:46:09.250+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:46:09.253+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:09.252+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:46:09.809+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:09.808+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:09.817+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:46:09.927+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:09.926+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:09.941+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:09.941+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:46:09.967+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.724 seconds
[2025-07-18T11:46:40.326+0000] {processor.py:186} INFO - Started process (PID=6167) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:46:40.326+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:46:40.329+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:40.329+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:46:40.831+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:40.830+0000] {cost_tracking.py:79} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:46:40.836+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:46:40.922+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:40.921+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:46:40.930+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:46:40.930+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:46:40.954+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.635 seconds
