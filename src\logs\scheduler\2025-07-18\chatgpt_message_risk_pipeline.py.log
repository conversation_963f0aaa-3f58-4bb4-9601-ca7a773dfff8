[2025-07-18T11:25:59.785+0000] {processor.py:186} INFO - Started process (PID=271) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:25:59.786+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:25:59.789+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.789+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:25:59.944+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:25:59.944+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: <PERSON>rro<PERSON> 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:25:59.956+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:26:00.204+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.203+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.215+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.215+0000] {override.py:1900} INFO - Created Permission View: can read on DAG:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.225+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.224+0000] {override.py:1900} INFO - Created Permission View: can edit on DAG:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.236+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.236+0000] {override.py:1900} INFO - Created Permission View: can delete on DAG Run:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.247+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.246+0000] {override.py:1900} INFO - Created Permission View: menu access on DAG Run:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.258+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.258+0000] {override.py:1900} INFO - Created Permission View: can read on DAG Run:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.267+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.267+0000] {override.py:1900} INFO - Created Permission View: can create on DAG Run:chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.268+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.268+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:00.280+0000] {logging_mixin.py:190} WARNING - /home/<USER>/.local/lib/python3.12/site-packages/airflow/models/dag.py:3901 DeprecationWarning: The dag_concurrency option in [core] has been renamed to max_active_tasks_per_dag - the old setting has been used, but please update your config.
[2025-07-18T11:26:00.281+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.281+0000] {dag.py:3262} INFO - Creating ORM DAG for chatgpt_message_risk_pipeline
[2025-07-18T11:26:00.282+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:00.282+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:26:00.301+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.525 seconds
[2025-07-18T11:26:30.632+0000] {processor.py:186} INFO - Started process (PID=402) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:26:30.634+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:26:30.637+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.636+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:26:30.864+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.864+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:26:30.870+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:26:30.966+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.966+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:26:30.977+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:26:30.976+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:26:30.995+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.369 seconds
[2025-07-18T11:27:02.103+0000] {processor.py:186} INFO - Started process (PID=533) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:02.104+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:27:02.106+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.106+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:02.203+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.203+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:02.210+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:02.313+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.313+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:02.325+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:02.325+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:27:02.349+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.252 seconds
[2025-07-18T11:27:32.794+0000] {processor.py:186} INFO - Started process (PID=666) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:32.795+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:27:32.798+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.797+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:32.877+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.877+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:27:32.885+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:27:32.982+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.982+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:27:32.995+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:27:32.995+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:27:33.015+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.228 seconds
[2025-07-18T11:28:03.319+0000] {processor.py:186} INFO - Started process (PID=797) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:03.320+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:28:03.322+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.322+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:03.392+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.392+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:03.401+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:03.500+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.500+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:03.512+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:03.511+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:28:03.532+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.218 seconds
[2025-07-18T11:28:33.674+0000] {processor.py:186} INFO - Started process (PID=928) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:33.675+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:28:33.677+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.677+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:33.749+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.749+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:28:33.757+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:28:33.853+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.853+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:28:33.863+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:28:33.863+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:28:33.883+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.215 seconds
[2025-07-18T11:29:04.701+0000] {processor.py:186} INFO - Started process (PID=1059) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:04.702+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:29:04.705+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.704+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:04.779+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.779+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:04.788+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:04.893+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.893+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:04.904+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:04.904+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:29:04.924+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.231 seconds
[2025-07-18T11:29:35.470+0000] {processor.py:186} INFO - Started process (PID=1190) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:35.471+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:29:35.474+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.474+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:35.557+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.556+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:29:35.565+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:29:35.672+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.671+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:29:35.682+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:29:35.682+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:29:35.703+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.240 seconds
[2025-07-18T11:30:05.891+0000] {processor.py:186} INFO - Started process (PID=1321) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:05.892+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:30:05.896+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.895+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:05.973+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:05.973+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:05.981+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:06.086+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.086+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:06.097+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:06.097+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:30:06.119+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.234 seconds
[2025-07-18T11:30:37.008+0000] {processor.py:186} INFO - Started process (PID=1452) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:37.009+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:30:37.011+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.010+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:37.087+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.086+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:30:37.094+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:30:37.197+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.197+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:30:37.209+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:30:37.209+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:30:37.228+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.227 seconds
[2025-07-18T11:31:07.668+0000] {processor.py:186} INFO - Started process (PID=1583) to work on /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:07.669+0000] {processor.py:914} INFO - Processing file /opt/airflow/dags/chatgpt_message_risk_pipeline.py for tasks to queue
[2025-07-18T11:31:07.671+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.671+0000] {dagbag.py:588} INFO - Filling up the DagBag from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:07.752+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.751+0000] {cost_tracking.py:58} ERROR - Ошибка подключения к Redis: Error 111 connecting to localhost:6379. Connection refused.
[2025-07-18T11:31:07.768+0000] {processor.py:925} INFO - DAG(s) 'chatgpt_message_risk_pipeline' retrieved from /opt/airflow/dags/chatgpt_message_risk_pipeline.py
[2025-07-18T11:31:07.880+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.880+0000] {dag.py:3239} INFO - Sync 1 DAGs
[2025-07-18T11:31:07.892+0000] {logging_mixin.py:190} INFO - [2025-07-18T11:31:07.892+0000] {dag.py:4180} INFO - Setting next_dagrun for chatgpt_message_risk_pipeline to None, run_after=None
[2025-07-18T11:31:07.912+0000] {processor.py:208} INFO - Processing /opt/airflow/dags/chatgpt_message_risk_pipeline.py took 0.250 seconds
